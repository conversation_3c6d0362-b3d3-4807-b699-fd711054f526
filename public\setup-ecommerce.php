<?php
// إعداد المتجر الإلكتروني مع بوابات الدفع السعودية
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    echo "<h2>🛒 إعداد المتجر الإلكتروني...</h2>";
} catch (Exception $e) {
    die("<h2>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</h2>");
}

// إنشاء جدول الفئات
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS product_categories (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            image VARCHAR(255),
            parent_id INT NULL,
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES product_categories(id) ON DELETE SET NULL
        )
    ");
    echo "<p>✅ تم إنشاء جدول فئات المنتجات</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول الفئات: " . $e->getMessage() . "</p>";
}

// إنشاء جدول المنتجات
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS products (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            short_description VARCHAR(500),
            sku VARCHAR(50) UNIQUE,
            price DECIMAL(10,2) NOT NULL,
            sale_price DECIMAL(10,2) NULL,
            cost_price DECIMAL(10,2) NULL,
            stock_quantity INT DEFAULT 0,
            min_stock_level INT DEFAULT 5,
            weight DECIMAL(8,2) DEFAULT 0,
            dimensions VARCHAR(100),
            category_id INT,
            brand VARCHAR(100),
            status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active',
            featured BOOLEAN DEFAULT FALSE,
            digital BOOLEAN DEFAULT FALSE,
            tax_rate DECIMAL(5,2) DEFAULT 15.00,
            meta_title VARCHAR(200),
            meta_description VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE SET NULL,
            INDEX idx_sku (sku),
            INDEX idx_category (category_id),
            INDEX idx_status (status)
        )
    ");
    echo "<p>✅ تم إنشاء جدول المنتجات</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول المنتجات: " . $e->getMessage() . "</p>";
}

// إنشاء جدول صور المنتجات
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS product_images (
            id INT PRIMARY KEY AUTO_INCREMENT,
            product_id INT NOT NULL,
            image_path VARCHAR(255) NOT NULL,
            alt_text VARCHAR(200),
            sort_order INT DEFAULT 0,
            is_primary BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✅ تم إنشاء جدول صور المنتجات</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول الصور: " . $e->getMessage() . "</p>";
}

// إنشاء جدول العملاء
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS customers (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(150) UNIQUE NOT NULL,
            phone VARCHAR(20),
            date_of_birth DATE NULL,
            gender ENUM('male', 'female') NULL,
            status ENUM('active', 'inactive', 'blocked') DEFAULT 'active',
            email_verified BOOLEAN DEFAULT FALSE,
            phone_verified BOOLEAN DEFAULT FALSE,
            total_orders INT DEFAULT 0,
            total_spent DECIMAL(12,2) DEFAULT 0,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_email (email),
            INDEX idx_phone (phone)
        )
    ");
    echo "<p>✅ تم إنشاء جدول العملاء</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول العملاء: " . $e->getMessage() . "</p>";
}

// إنشاء جدول عناوين العملاء
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS customer_addresses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            customer_id INT NOT NULL,
            type ENUM('billing', 'shipping', 'both') DEFAULT 'both',
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            company VARCHAR(150),
            address_line_1 VARCHAR(200) NOT NULL,
            address_line_2 VARCHAR(200),
            city VARCHAR(100) NOT NULL,
            state VARCHAR(100) NOT NULL,
            postal_code VARCHAR(20),
            country VARCHAR(100) DEFAULT 'Saudi Arabia',
            phone VARCHAR(20),
            is_default BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✅ تم إنشاء جدول عناوين العملاء</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول العناوين: " . $e->getMessage() . "</p>";
}

// إنشاء جدول سلة التسوق
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS shopping_cart (
            id INT PRIMARY KEY AUTO_INCREMENT,
            customer_id INT NULL,
            session_id VARCHAR(100) NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            price DECIMAL(10,2) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            INDEX idx_session (session_id),
            INDEX idx_customer (customer_id)
        )
    ");
    echo "<p>✅ تم إنشاء جدول سلة التسوق</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول السلة: " . $e->getMessage() . "</p>";
}

// إنشاء جدول الطلبات
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS orders (
            id INT PRIMARY KEY AUTO_INCREMENT,
            order_number VARCHAR(50) UNIQUE NOT NULL,
            customer_id INT NOT NULL,
            status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded') DEFAULT 'pending',
            payment_status ENUM('pending', 'paid', 'failed', 'refunded', 'partially_refunded') DEFAULT 'pending',
            payment_method VARCHAR(50),
            payment_gateway VARCHAR(50),
            transaction_id VARCHAR(100),
            subtotal DECIMAL(12,2) NOT NULL,
            tax_amount DECIMAL(12,2) DEFAULT 0,
            shipping_amount DECIMAL(12,2) DEFAULT 0,
            discount_amount DECIMAL(12,2) DEFAULT 0,
            total_amount DECIMAL(12,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'SAR',
            billing_address JSON,
            shipping_address JSON,
            notes TEXT,
            shipped_at TIMESTAMP NULL,
            delivered_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
            INDEX idx_order_number (order_number),
            INDEX idx_customer (customer_id),
            INDEX idx_status (status),
            INDEX idx_payment_status (payment_status)
        )
    ");
    echo "<p>✅ تم إنشاء جدول الطلبات</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول الطلبات: " . $e->getMessage() . "</p>";
}

// إنشاء جدول تفاصيل الطلبات
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS order_items (
            id INT PRIMARY KEY AUTO_INCREMENT,
            order_id INT NOT NULL,
            product_id INT NOT NULL,
            product_name VARCHAR(200) NOT NULL,
            product_sku VARCHAR(50),
            quantity INT NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(12,2) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT
        )
    ");
    echo "<p>✅ تم إنشاء جدول تفاصيل الطلبات</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول تفاصيل الطلبات: " . $e->getMessage() . "</p>";
}

// إنشاء جدول بوابات الدفع
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS payment_gateways (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            code VARCHAR(50) UNIQUE NOT NULL,
            description TEXT,
            logo VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            is_sandbox BOOLEAN DEFAULT FALSE,
            supported_currencies JSON,
            config JSON,
            fees_percentage DECIMAL(5,2) DEFAULT 0,
            fees_fixed DECIMAL(10,2) DEFAULT 0,
            min_amount DECIMAL(10,2) DEFAULT 0,
            max_amount DECIMAL(10,2) DEFAULT 999999,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_code (code),
            INDEX idx_active (is_active)
        )
    ");
    echo "<p>✅ تم إنشاء جدول بوابات الدفع</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول بوابات الدفع: " . $e->getMessage() . "</p>";
}

// إنشاء جدول معاملات الدفع
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS payment_transactions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            order_id INT NOT NULL,
            gateway_id INT NOT NULL,
            transaction_id VARCHAR(100) UNIQUE NOT NULL,
            gateway_transaction_id VARCHAR(100),
            amount DECIMAL(12,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'SAR',
            status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
            gateway_response JSON,
            fees_amount DECIMAL(10,2) DEFAULT 0,
            net_amount DECIMAL(12,2) NOT NULL,
            processed_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE RESTRICT,
            FOREIGN KEY (gateway_id) REFERENCES payment_gateways(id) ON DELETE RESTRICT,
            INDEX idx_transaction_id (transaction_id),
            INDEX idx_gateway_transaction_id (gateway_transaction_id),
            INDEX idx_status (status)
        )
    ");
    echo "<p>✅ تم إنشاء جدول معاملات الدفع</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول المعاملات: " . $e->getMessage() . "</p>";
}

// إنشاء جدول كوبونات الخصم
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS discount_coupons (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(50) UNIQUE NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            type ENUM('percentage', 'fixed_amount', 'free_shipping') NOT NULL,
            value DECIMAL(10,2) NOT NULL,
            min_order_amount DECIMAL(10,2) DEFAULT 0,
            max_discount_amount DECIMAL(10,2) NULL,
            usage_limit INT NULL,
            usage_count INT DEFAULT 0,
            usage_limit_per_customer INT DEFAULT 1,
            valid_from TIMESTAMP NULL,
            valid_until TIMESTAMP NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_code (code),
            INDEX idx_active (is_active)
        )
    ");
    echo "<p>✅ تم إنشاء جدول كوبونات الخصم</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول الكوبونات: " . $e->getMessage() . "</p>";
}

// إنشاء جدول التقييمات
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS product_reviews (
            id INT PRIMARY KEY AUTO_INCREMENT,
            product_id INT NOT NULL,
            customer_id INT NOT NULL,
            order_id INT NULL,
            rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
            title VARCHAR(200),
            comment TEXT,
            is_verified BOOLEAN DEFAULT FALSE,
            is_approved BOOLEAN DEFAULT FALSE,
            helpful_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,
            UNIQUE KEY unique_customer_product (customer_id, product_id),
            INDEX idx_product (product_id),
            INDEX idx_rating (rating)
        )
    ");
    echo "<p>✅ تم إنشاء جدول التقييمات</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول التقييمات: " . $e->getMessage() . "</p>";
}

// إدراج بوابات الدفع السعودية
try {
    $gateways = [
        [
            'name' => 'مدى',
            'code' => 'mada',
            'description' => 'بطاقات مدى السعودية',
            'logo' => '/images/gateways/mada.png',
            'supported_currencies' => '["SAR"]',
            'config' => '{"merchant_id":"","api_key":"","endpoint":"https://api.mada.sa"}',
            'fees_percentage' => 1.75,
            'sort_order' => 1
        ],
        [
            'name' => 'تابي - الدفع بالتقسيط',
            'code' => 'tabby',
            'description' => 'ادفع على 4 دفعات بدون فوائد',
            'logo' => '/images/gateways/tabby.png',
            'supported_currencies' => '["SAR","AED","KWD","BHD"]',
            'config' => '{"public_key":"","secret_key":"","endpoint":"https://api.tabby.ai"}',
            'fees_percentage' => 2.9,
            'min_amount' => 100,
            'max_amount' => 10000,
            'sort_order' => 2
        ],
        [
            'name' => 'تمارا - الدفع الآجل',
            'code' => 'tamara',
            'description' => 'اشتري الآن وادفع لاحقاً',
            'logo' => '/images/gateways/tamara.png',
            'supported_currencies' => '["SAR","AED","KWD"]',
            'config' => '{"merchant_token":"","api_url":"https://api.tamara.co"}',
            'fees_percentage' => 3.5,
            'min_amount' => 50,
            'max_amount' => 5000,
            'sort_order' => 3
        ],
        [
            'name' => 'هايبر باي',
            'code' => 'hyperpay',
            'description' => 'بوابة دفع شاملة',
            'logo' => '/images/gateways/hyperpay.png',
            'supported_currencies' => '["SAR","USD","EUR","AED"]',
            'config' => '{"entity_id":"","access_token":"","endpoint":"https://eu-test.oppwa.com"}',
            'fees_percentage' => 2.75,
            'sort_order' => 4
        ],
        [
            'name' => 'باي تابس',
            'code' => 'paytabs',
            'description' => 'حلول دفع متقدمة',
            'logo' => '/images/gateways/paytabs.png',
            'supported_currencies' => '["SAR","USD","EUR","AED"]',
            'config' => '{"profile_id":"","server_key":"","endpoint":"https://secure.paytabs.sa"}',
            'fees_percentage' => 2.85,
            'sort_order' => 5
        ],
        [
            'name' => 'STC Pay',
            'code' => 'stcpay',
            'description' => 'محفظة STC الرقمية',
            'logo' => '/images/gateways/stcpay.png',
            'supported_currencies' => '["SAR"]',
            'config' => '{"merchant_id":"","api_key":"","endpoint":"https://api.stcpay.com.sa"}',
            'fees_percentage' => 1.5,
            'sort_order' => 6
        ]
    ];

    foreach ($gateways as $gateway) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO payment_gateways 
            (name, code, description, logo, supported_currencies, config, fees_percentage, min_amount, max_amount, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $gateway['name'],
            $gateway['code'],
            $gateway['description'],
            $gateway['logo'],
            $gateway['supported_currencies'],
            $gateway['config'],
            $gateway['fees_percentage'],
            $gateway['min_amount'] ?? 0,
            $gateway['max_amount'] ?? 999999,
            $gateway['sort_order']
        ]);
    }
    
    echo "<p>✅ تم إدراج بوابات الدفع السعودية</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إدراج بوابات الدفع: " . $e->getMessage() . "</p>";
}

// إنشاء مجلدات المتجر
$directories = [
    '../uploads/products',
    '../uploads/categories',
    '../uploads/gateways',
    '../uploads/reviews'
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
        echo "<p>✅ تم إنشاء مجلد: $dir</p>";
    }
}

// عرض إحصائيات قاعدة البيانات
echo "<hr>";
echo "<h3>📊 إحصائيات المتجر الإلكتروني:</h3>";

try {
    $tables = [
        'product_categories' => 'فئات المنتجات',
        'products' => 'المنتجات',
        'product_images' => 'صور المنتجات',
        'customers' => 'العملاء',
        'customer_addresses' => 'عناوين العملاء',
        'shopping_cart' => 'سلة التسوق',
        'orders' => 'الطلبات',
        'order_items' => 'تفاصيل الطلبات',
        'payment_gateways' => 'بوابات الدفع',
        'payment_transactions' => 'معاملات الدفع',
        'discount_coupons' => 'كوبونات الخصم',
        'product_reviews' => 'التقييمات'
    ];
    
    foreach ($tables as $table => $name) {
        $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
        echo "<p>✅ جدول <strong>$name</strong>: $count سجل</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ خطأ في جلب الإحصائيات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🎉 تم إعداد المتجر الإلكتروني بنجاح!</h3>";
echo "<p>الميزات المضافة:</p>";
echo "<ul>";
echo "<li>✅ نظام إدارة المنتجات والفئات</li>";
echo "<li>✅ سلة تسوق ذكية</li>";
echo "<li>✅ نظام الطلبات المتكامل</li>";
echo "<li>✅ 6 بوابات دفع سعودية</li>";
echo "<li>✅ نظام العملاء والعناوين</li>";
echo "<li>✅ كوبونات الخصم</li>";
echo "<li>✅ نظام التقييمات</li>";
echo "</ul>";
echo "<p><a href='/store-admin.php' class='btn btn-primary'>إدارة المتجر</a> ";
echo "<a href='/store.php' class='btn btn-success'>عرض المتجر</a></p>";
echo "</div>";

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
ul { margin: 10px 0; padding-left: 20px; }
.btn { display: inline-block; padding: 8px 16px; margin: 5px; background: #007bff; color: white; border-radius: 5px; text-decoration: none; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
</style>";
?>
