<?php
$title = 'تسجيل الدخول';
ob_start();
?>

<div class="auth-container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-body p-5">
                    <!-- Logo -->
                    <div class="text-center mb-4">
                        <i class="fas fa-hands-helping fa-3x text-primary mb-3"></i>
                        <h3 class="card-title">نظام إدارة المؤسسات</h3>
                        <p class="text-muted">تسجيل الدخول إلى حسابك</p>
                    </div>

                    <!-- Login Form -->
                    <form method="POST" action="/auth/login" id="loginForm">
                        <?= \App\Middleware\CsrfMiddleware::getTokenField() ?>
                        
                        <!-- Email -->
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" 
                                       class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                       id="email" 
                                       name="email" 
                                       value="<?= htmlspecialchars(old('email')) ?>"
                                       required 
                                       autofocus>
                                <?php if (isset($errors['email'])): ?>
                                <div class="invalid-feedback">
                                    <?= htmlspecialchars($errors['email'][0]) ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Password -->
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" 
                                       class="form-control <?= isset($errors['password']) ? 'is-invalid' : '' ?>" 
                                       id="password" 
                                       name="password" 
                                       required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if (isset($errors['password'])): ?>
                                <div class="invalid-feedback">
                                    <?= htmlspecialchars($errors['password'][0]) ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Remember Me -->
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
                            <label class="form-check-label" for="remember">
                                تذكرني
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </div>
                    </form>

                    <!-- Additional Links -->
                    <div class="text-center mt-4">
                        <a href="/auth/forgot-password" class="text-decoration-none">
                            <i class="fas fa-key me-1"></i>
                            نسيت كلمة المرور؟
                        </a>
                    </div>

                    <!-- Demo Accounts -->
                    <?php if (($_ENV['APP_ENV'] ?? 'production') === 'local'): ?>
                    <div class="mt-4">
                        <hr>
                        <h6 class="text-center text-muted">حسابات تجريبية</h6>
                        <div class="row g-2">
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="fillDemo('<EMAIL>', 'password')">
                                    مدير النظام
                                </button>
                            </div>
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="fillDemo('<EMAIL>', 'password')">
                                    مدير منظمة
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- System Info -->
            <div class="text-center mt-3">
                <small class="text-muted">
                    نظام إدارة مؤسسات القطاع غير الربحي v1.0
                </small>
            </div>
        </div>
    </div>
</div>

<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.card {
    border: none;
    border-radius: 15px;
}

.input-group-text {
    background-color: #f8f9fa;
    border-left: none;
}

.form-control {
    border-right: none;
}

.form-control:focus {
    box-shadow: none;
    border-color: #ced4da;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    transform: translateY(-1px);
}

#togglePassword {
    border-left: none;
}
</style>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const password = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        password.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Demo account filler
function fillDemo(email, password) {
    document.getElementById('email').value = email;
    document.getElementById('password').value = password;
}

// Form submission handling
document.getElementById('loginForm').addEventListener('submit', function() {
    const btn = document.getElementById('loginBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
    btn.disabled = true;
});

// Auto-hide alerts after 5 seconds
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    });
}, 5000);
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>
