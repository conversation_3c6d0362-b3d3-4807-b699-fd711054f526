<?php
require_once '../config/database.php';
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: /login.php');
    exit;
}

$success = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'generate_payroll') {
        try {
            $employee_id = $_POST['employee_id'];
            $pay_period_start = $_POST['pay_period_start'];
            $pay_period_end = $_POST['pay_period_end'];

            // جلب بيانات الموظف
            $employee_stmt = $pdo->prepare("
                SELECT e.*, u.name
                FROM employees e
                JOIN users u ON e.user_id = u.id
                WHERE e.id = ?
            ");
            $employee_stmt->execute([$employee_id]);
            $employee = $employee_stmt->fetch();

            if (!$employee) {
                throw new Exception("الموظف غير موجود");
            }

            // التحقق من عدم وجود راتب لنفس الفترة
            $existing_stmt = $pdo->prepare("
                SELECT id FROM payroll
                WHERE employee_id = ? AND pay_period_start = ? AND pay_period_end = ?
            ");
            $existing_stmt->execute([$employee_id, $pay_period_start, $pay_period_end]);

            if ($existing_stmt->fetch()) {
                throw new Exception("يوجد راتب محسوب لهذا الموظف في نفس الفترة");
            }

            // حساب الراتب
            $basic_salary = $employee['salary'];
            $overtime_hours = floatval($_POST['overtime_hours'] ?? 0);
            $overtime_rate = floatval($_POST['overtime_rate'] ?? 0);
            $allowances = floatval($_POST['allowances'] ?? 0);
            $deductions = floatval($_POST['deductions'] ?? 0);

            // حساب الإضافي
            $overtime_amount = $overtime_hours * $overtime_rate;

            // حساب الضرائب والتأمينات (نسب افتراضية)
            $gross_salary = $basic_salary + $overtime_amount + $allowances;
            $tax_rate = 0.05; // 5%
            $insurance_rate = 0.09; // 9%

            $tax_amount = $gross_salary * $tax_rate;
            $insurance_amount = $gross_salary * $insurance_rate;

            $total_deductions = $deductions + $tax_amount + $insurance_amount;
            $net_salary = $gross_salary - $total_deductions;

            // إدراج الراتب
            $payroll_stmt = $pdo->prepare("
                INSERT INTO payroll (
                    employee_id, pay_period_start, pay_period_end, basic_salary,
                    overtime_hours, overtime_rate, allowances, deductions,
                    tax_amount, insurance_amount, net_salary, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $payroll_stmt->execute([
                $employee_id, $pay_period_start, $pay_period_end, $basic_salary,
                $overtime_hours, $overtime_rate, $allowances, $deductions,
                $tax_amount, $insurance_amount, $net_salary, $_POST['notes'] ?? null
            ]);

            $success = "تم حساب الراتب بنجاح";

        } catch (Exception $e) {
            $error = "خطأ في حساب الراتب: " . $e->getMessage();
        }
    }

    elseif ($action === 'approve_payroll') {
        try {
            $payroll_id = $_POST['payroll_id'];

            $stmt = $pdo->prepare("UPDATE payroll SET status = 'approved' WHERE id = ?");
            $stmt->execute([$payroll_id]);

            $success = "تم اعتماد الراتب بنجاح";

        } catch (Exception $e) {
            $error = "خطأ في اعتماد الراتب: " . $e->getMessage();
        }
    }

    elseif ($action === 'mark_paid') {
        try {
            $payroll_id = $_POST['payroll_id'];

            $stmt = $pdo->prepare("
                UPDATE payroll
                SET status = 'paid', paid_at = NOW()
                WHERE id = ? AND status = 'approved'
            ");
            $stmt->execute([$payroll_id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception("لا يمكن تحديد الراتب كمدفوع إلا بعد اعتماده");
            }

            $success = "تم تحديد الراتب كمدفوع";

        } catch (Exception $e) {
            $error = "خطأ في تحديث حالة الراتب: " . $e->getMessage();
        }
    }
}

// جلب الموظفين النشطين
$employees = $pdo->query("
    SELECT e.id, e.employee_id, u.name, e.salary, d.name as department_name
    FROM employees e
    JOIN users u ON e.user_id = u.id
    JOIN departments d ON e.department_id = d.id
    WHERE e.status = 'active'
    ORDER BY u.name
")->fetchAll();

// جلب الرواتب
$filter = $_GET['filter'] ?? 'current_month';
$status_filter = $_GET['status'] ?? '';

$where_conditions = [];
$params = [];

if ($filter === 'current_month') {
    $where_conditions[] = "MONTH(p.pay_period_start) = MONTH(CURRENT_DATE()) AND YEAR(p.pay_period_start) = YEAR(CURRENT_DATE())";
} elseif ($filter === 'last_month') {
    $where_conditions[] = "MONTH(p.pay_period_start) = MONTH(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH)) AND YEAR(p.pay_period_start) = YEAR(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH))";
}

if ($status_filter) {
    $where_conditions[] = "p.status = ?";
    $params[] = $status_filter;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

$payroll_query = "
    SELECT p.*, e.employee_id, u.name as employee_name,
           d.name as department_name, pos.title as position_title
    FROM payroll p
    JOIN employees e ON p.employee_id = e.id
    JOIN users u ON e.user_id = u.id
    JOIN departments d ON e.department_id = d.id
    JOIN positions pos ON e.position_id = pos.id
    {$where_clause}
    ORDER BY p.created_at DESC
";

$stmt = $pdo->prepare($payroll_query);
$stmt->execute($params);
$payroll_records = $stmt->fetchAll();

// إحصائيات
$stats = [
    'total_payroll' => $pdo->query("SELECT SUM(net_salary) FROM payroll WHERE MONTH(pay_period_start) = MONTH(CURRENT_DATE())")->fetchColumn() ?: 0,
    'pending_approvals' => $pdo->query("SELECT COUNT(*) FROM payroll WHERE status = 'draft'")->fetchColumn(),
    'paid_this_month' => $pdo->query("SELECT COUNT(*) FROM payroll WHERE status = 'paid' AND MONTH(pay_period_start) = MONTH(CURRENT_DATE())")->fetchColumn(),
    'avg_salary' => $pdo->query("SELECT AVG(net_salary) FROM payroll WHERE MONTH(pay_period_start) = MONTH(CURRENT_DATE())")->fetchColumn() ?: 0
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الرواتب - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #28a745, #20c997); }
        .stats-card { background: linear-gradient(45deg, #28a745, #20c997); color: white; border-radius: 15px; }
        .payroll-card { border-radius: 10px; transition: transform 0.2s; }
        .payroll-card:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .status-draft { color: #ffc107; }
        .status-approved { color: #17a2b8; }
        .status-paid { color: #28a745; }
        .salary-amount { font-weight: bold; font-size: 1.1em; }
        .calculation-section { background-color: #f8f9fa; border-radius: 8px; padding: 15px; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-money-bill-wave me-2"></i>إدارة الرواتب
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="/employees.php">
                    <i class="fas fa-users me-1"></i>الموظفين
                </a>
                <a class="nav-link" href="/leave-requests.php">
                    <i class="fas fa-calendar-alt me-1"></i>الإجازات
                </a>
                <a class="nav-link" href="/messages.php">
                    <i class="fas fa-envelope me-1"></i>الرسائل
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-money-bill fa-2x mb-2"></i>
                    <h4><?= number_format($stats['total_payroll'], 0) ?></h4>
                    <small>إجمالي الرواتب هذا الشهر</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4><?= $stats['pending_approvals'] ?></h4>
                    <small>في انتظار الاعتماد</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4><?= $stats['paid_this_month'] ?></h4>
                    <small>مدفوعة هذا الشهر</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h4><?= number_format($stats['avg_salary'], 0) ?></h4>
                    <small>متوسط الراتب</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- نموذج حساب راتب جديد -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-calculator me-2"></i>حساب راتب جديد</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="payrollForm">
                            <input type="hidden" name="action" value="generate_payroll">

                            <div class="mb-3">
                                <label class="form-label">الموظف *</label>
                                <select name="employee_id" class="form-select" required id="employeeSelect">
                                    <option value="">اختر الموظف</option>
                                    <?php foreach ($employees as $emp): ?>
                                    <option value="<?= $emp['id'] ?>" data-salary="<?= $emp['salary'] ?>">
                                        <?= htmlspecialchars($emp['name']) ?> - <?= htmlspecialchars($emp['employee_id']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">فترة الراتب - من *</label>
                                <input type="date" name="pay_period_start" class="form-control" required value="<?= date('Y-m-01') ?>">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">فترة الراتب - إلى *</label>
                                <input type="date" name="pay_period_end" class="form-control" required value="<?= date('Y-m-t') ?>">
                            </div>

                            <div class="calculation-section mb-3">
                                <h6 class="text-success mb-3">تفاصيل الحساب</h6>

                                <div class="mb-3">
                                    <label class="form-label">الراتب الأساسي</label>
                                    <input type="number" name="basic_salary" class="form-control" readonly id="basicSalary">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ساعات إضافية</label>
                                    <input type="number" name="overtime_hours" class="form-control" step="0.5" value="0" id="overtimeHours">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">سعر الساعة الإضافية</label>
                                    <input type="number" name="overtime_rate" class="form-control" step="0.01" value="0" id="overtimeRate">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">البدلات</label>
                                    <input type="number" name="allowances" class="form-control" step="0.01" value="0" id="allowances">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الخصومات</label>
                                    <input type="number" name="deductions" class="form-control" step="0.01" value="0" id="deductions">
                                </div>

                                <div class="alert alert-info small" id="calculationPreview">
                                    <strong>معاينة الحساب:</strong><br>
                                    الراتب الإجمالي: <span id="grossSalary">0</span> ريال<br>
                                    الضرائب (5%): <span id="taxAmount">0</span> ريال<br>
                                    التأمينات (9%): <span id="insuranceAmount">0</span> ريال<br>
                                    <hr class="my-2">
                                    <strong>الراتب الصافي: <span id="netSalary">0</span> ريال</strong>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea name="notes" class="form-control" rows="2"></textarea>
                            </div>

                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-calculator me-2"></i>حساب الراتب
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة الرواتب -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>سجل الرواتب</h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" onchange="filterPayroll()" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="draft" <?= $status_filter === 'draft' ? 'selected' : '' ?>>مسودة</option>
                                <option value="approved" <?= $status_filter === 'approved' ? 'selected' : '' ?>>معتمد</option>
                                <option value="paid" <?= $status_filter === 'paid' ? 'selected' : '' ?>>مدفوع</option>
                            </select>
                            <div class="btn-group">
                                <a href="?filter=current_month" class="btn btn-sm <?= $filter === 'current_month' ? 'btn-success' : 'btn-outline-success' ?>">
                                    هذا الشهر
                                </a>
                                <a href="?filter=last_month" class="btn btn-sm <?= $filter === 'last_month' ? 'btn-success' : 'btn-outline-success' ?>">
                                    الشهر الماضي
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($payroll_records)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-money-bill fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد سجلات رواتب</p>
                        </div>
                        <?php else: ?>
                        <div class="row">
                            <?php foreach ($payroll_records as $payroll): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card payroll-card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0"><?= htmlspecialchars($payroll['employee_name']) ?></h6>
                                            <span class="badge status-<?= $payroll['status'] ?>">
                                                <?php
                                                $status_labels = [
                                                    'draft' => 'مسودة',
                                                    'approved' => 'معتمد',
                                                    'paid' => 'مدفوع'
                                                ];
                                                echo $status_labels[$payroll['status']] ?? $payroll['status'];
                                                ?>
                                            </span>
                                        </div>

                                        <div class="small text-muted mb-2">
                                            <div><i class="fas fa-calendar me-1"></i>
                                                <?= date('Y/m/d', strtotime($payroll['pay_period_start'])) ?> -
                                                <?= date('Y/m/d', strtotime($payroll['pay_period_end'])) ?>
                                            </div>
                                            <div><i class="fas fa-building me-1"></i><?= htmlspecialchars($payroll['department_name']) ?></div>
                                            <div><i class="fas fa-briefcase me-1"></i><?= htmlspecialchars($payroll['position_title']) ?></div>
                                        </div>

                                        <div class="mb-2">
                                            <div class="small">
                                                راتب أساسي: <?= number_format($payroll['basic_salary'], 0) ?> ريال<br>
                                                <?php if ($payroll['overtime_hours'] > 0): ?>
                                                إضافي: <?= $payroll['overtime_hours'] ?> ساعة × <?= number_format($payroll['overtime_rate'], 0) ?> = <?= number_format($payroll['overtime_hours'] * $payroll['overtime_rate'], 0) ?> ريال<br>
                                                <?php endif; ?>
                                                <?php if ($payroll['allowances'] > 0): ?>
                                                بدلات: <?= number_format($payroll['allowances'], 0) ?> ريال<br>
                                                <?php endif; ?>
                                                <?php if ($payroll['deductions'] > 0): ?>
                                                خصومات: <?= number_format($payroll['deductions'], 0) ?> ريال<br>
                                                <?php endif; ?>
                                                ضرائب: <?= number_format($payroll['tax_amount'], 0) ?> ريال<br>
                                                تأمينات: <?= number_format($payroll['insurance_amount'], 0) ?> ريال
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="salary-amount text-success"><?= number_format($payroll['net_salary'], 0) ?> ريال</span>
                                            <div class="btn-group btn-group-sm">
                                                <?php if ($payroll['status'] === 'draft'): ?>
                                                <button class="btn btn-outline-info" onclick="approvePayroll(<?= $payroll['id'] ?>)">
                                                    <i class="fas fa-check"></i> اعتماد
                                                </button>
                                                <?php elseif ($payroll['status'] === 'approved'): ?>
                                                <button class="btn btn-outline-success" onclick="markPaid(<?= $payroll['id'] ?>)">
                                                    <i class="fas fa-money-bill"></i> دفع
                                                </button>
                                                <?php endif; ?>
                                                <button class="btn btn-outline-primary" onclick="viewPayroll(<?= $payroll['id'] ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <?php if ($payroll['notes']): ?>
                                        <div class="alert alert-light small mt-2 mb-0">
                                            <strong>ملاحظات:</strong> <?= htmlspecialchars($payroll['notes']) ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الراتب الأساسي عند اختيار الموظف
        document.getElementById('employeeSelect').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const salary = selectedOption.getAttribute('data-salary') || 0;
            document.getElementById('basicSalary').value = salary;
            calculateSalary();
        });

        // حساب الراتب تلقائياً
        function calculateSalary() {
            const basicSalary = parseFloat(document.getElementById('basicSalary').value) || 0;
            const overtimeHours = parseFloat(document.getElementById('overtimeHours').value) || 0;
            const overtimeRate = parseFloat(document.getElementById('overtimeRate').value) || 0;
            const allowances = parseFloat(document.getElementById('allowances').value) || 0;
            const deductions = parseFloat(document.getElementById('deductions').value) || 0;

            const overtimeAmount = overtimeHours * overtimeRate;
            const grossSalary = basicSalary + overtimeAmount + allowances;

            const taxAmount = grossSalary * 0.05; // 5%
            const insuranceAmount = grossSalary * 0.09; // 9%

            const netSalary = grossSalary - deductions - taxAmount - insuranceAmount;

            // تحديث المعاينة
            document.getElementById('grossSalary').textContent = Math.round(grossSalary).toLocaleString();
            document.getElementById('taxAmount').textContent = Math.round(taxAmount).toLocaleString();
            document.getElementById('insuranceAmount').textContent = Math.round(insuranceAmount).toLocaleString();
            document.getElementById('netSalary').textContent = Math.round(netSalary).toLocaleString();
        }

        // ربط الأحداث بحقول الإدخال
        ['overtimeHours', 'overtimeRate', 'allowances', 'deductions'].forEach(id => {
            document.getElementById(id).addEventListener('input', calculateSalary);
        });

        function filterPayroll() {
            const status = document.getElementById('statusFilter').value;
            const currentUrl = new URL(window.location);
            if (status) {
                currentUrl.searchParams.set('status', status);
            } else {
                currentUrl.searchParams.delete('status');
            }
            window.location.href = currentUrl.toString();
        }

        function approvePayroll(id) {
            if (confirm('هل أنت متأكد من اعتماد هذا الراتب؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="approve_payroll">
                    <input type="hidden" name="payroll_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function markPaid(id) {
            if (confirm('هل أنت متأكد من تحديد هذا الراتب كمدفوع؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="mark_paid">
                    <input type="hidden" name="payroll_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function viewPayroll(id) {
            // TODO: عرض تفاصيل الراتب
            alert('سيتم إضافة صفحة التفاصيل قريباً');
        }
    </script>
</body>
</html>