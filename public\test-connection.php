<?php
// اختبار الاتصال بقاعدة البيانات
echo "<h2>🔧 اختبار الاتصال بقاعدة البيانات</h2>";

try {
    // محاولة الاتصال بـ MySQL
    $host = 'localhost';
    $username = 'root';
    $password = '';
    
    echo "<p>📡 محاولة الاتصال بـ MySQL...</p>";
    
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ تم الاتصال بـ MySQL بنجاح</p>";
    
    // التحقق من وجود قاعدة البيانات
    $database = 'nonprofit_cms';
    $result = $pdo->query("SHOW DATABASES LIKE '$database'");
    
    if ($result->rowCount() > 0) {
        echo "<p>✅ قاعدة البيانات '$database' موجودة</p>";
        
        // الاتصال بقاعدة البيانات
        $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        
        echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
        
        // التحقق من الجداول
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>📊 الجداول الموجودة (" . count($tables) . " جدول):</p>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        
        // التحقق من المستخدمين
        if (in_array('users', $tables)) {
            $user_count = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
            echo "<p>👥 عدد المستخدمين: $user_count</p>";
        }
        
        // التحقق من الموظفين
        if (in_array('employees', $tables)) {
            $employee_count = $pdo->query("SELECT COUNT(*) FROM employees")->fetchColumn();
            echo "<p>👨‍💼 عدد الموظفين: $employee_count</p>";
        }
        
        // التحقق من المنتجات (إذا كانت موجودة)
        if (in_array('products', $tables)) {
            $product_count = $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn();
            echo "<p>🛍️ عدد المنتجات: $product_count</p>";
        }
        
        echo "<hr>";
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🎉 قاعدة البيانات تعمل بشكل صحيح!</h3>";
        echo "<p>يمكنك الآن:</p>";
        echo "<ul>";
        echo "<li><a href='/dashboard.php'>الذهاب للوحة التحكم</a></li>";
        echo "<li><a href='/login.php'>تسجيل الدخول</a></li>";
        echo "<li><a href='/store.php'>زيارة المتجر</a></li>";
        echo "<li><a href='/store-admin.php'>إدارة المتجر</a></li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<p>❌ قاعدة البيانات '$database' غير موجودة</p>";
        echo "<p><a href='/create-database.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء قاعدة البيانات</a></p>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الحلول المقترحة:</strong></p>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل خادم MySQL (XAMPP/WAMP/MAMP)</li>";
    echo "<li>تحقق من بيانات الاتصال (المضيف، اسم المستخدم، كلمة المرور)</li>";
    echo "<li>تأكد من وجود صلاحيات الوصول لقاعدة البيانات</li>";
    echo "</ul>";
    
    echo "<h4>خطوات استكشاف الأخطاء:</h4>";
    echo "<ol>";
    echo "<li>افتح XAMPP Control Panel</li>";
    echo "<li>تأكد من تشغيل Apache و MySQL</li>";
    echo "<li>اضغط على Admin بجانب MySQL لفتح phpMyAdmin</li>";
    echo "<li>أنشئ قاعدة بيانات جديدة باسم 'nonprofit_cms'</li>";
    echo "<li><a href='/create-database.php'>اضغط هنا لإنشاء قاعدة البيانات تلقائياً</a></li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ عام</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
ul, ol { margin: 10px 0; padding-left: 20px; }
</style>";
?>
