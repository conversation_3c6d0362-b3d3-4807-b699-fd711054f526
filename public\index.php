<?php

// تحديد مسار الجذر
define('ROOT_PATH', dirname(__DIR__));

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// تحميل الكلاسات الأساسية مباشرة
require_once ROOT_PATH . '/core/Database/Connection.php';
require_once ROOT_PATH . '/core/Database/Model.php';
require_once ROOT_PATH . '/core/Router/Router.php';
require_once ROOT_PATH . '/core/Auth/Auth.php';
require_once ROOT_PATH . '/core/Validation/Validator.php';
require_once ROOT_PATH . '/app/Controllers/BaseController.php';
require_once ROOT_PATH . '/app/Controllers/HomeController.php';
require_once ROOT_PATH . '/app/Controllers/AuthController.php';
require_once ROOT_PATH . '/app/Controllers/DashboardController.php';
require_once ROOT_PATH . '/app/Models/User.php';
require_once ROOT_PATH . '/app/Models/Organization.php';
require_once ROOT_PATH . '/app/Middleware/AuthMiddleware.php';
require_once ROOT_PATH . '/app/Middleware/GuestMiddleware.php';
require_once ROOT_PATH . '/app/Middleware/RoleMiddleware.php';
require_once ROOT_PATH . '/app/Middleware/PermissionMiddleware.php';
require_once ROOT_PATH . '/app/Middleware/CsrfMiddleware.php';
require_once ROOT_PATH . '/app/Middleware/ThrottleMiddleware.php';

// Autoloader للكلاسات الإضافية
spl_autoload_register(function ($class) {
    $paths = [
        'App\\' => ROOT_PATH . '/app/',
        'Core\\' => ROOT_PATH . '/core/',
    ];

    foreach ($paths as $prefix => $base_dir) {
        $len = strlen($prefix);
        if (strncmp($prefix, $class, $len) !== 0) {
            continue;
        }

        $relative_class = substr($class, $len);
        $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';

        if (file_exists($file)) {
            require $file;
            return;
        }
    }
});

// معالجة الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', $_ENV['APP_DEBUG'] ?? false);

// إعداد المنطقة الزمنية
date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'Asia/Riyadh');

// إعداد الترميز
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

// بدء الجلسة
session_start();

// إعداد CSRF Token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// معالجة الأخطاء المخصصة
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }

    $error = [
        'severity' => $severity,
        'message' => $message,
        'file' => $file,
        'line' => $line,
        'time' => date('Y-m-d H:i:s'),
        'user_id' => $_SESSION['user_id'] ?? null,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];

    // تسجيل الخطأ
    $logFile = ROOT_PATH . '/storage/logs/error_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, json_encode($error) . PHP_EOL, FILE_APPEND | LOCK_EX);

    // عرض الخطأ في وضع التطوير
    if ($_ENV['APP_DEBUG'] ?? false) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<strong>خطأ:</strong> {$message}<br>";
        echo "<strong>الملف:</strong> {$file}<br>";
        echo "<strong>السطر:</strong> {$line}";
        echo "</div>";
    }

    return true;
});

// معالجة الاستثناءات
set_exception_handler(function($exception) {
    $error = [
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString(),
        'time' => date('Y-m-d H:i:s'),
        'user_id' => $_SESSION['user_id'] ?? null,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];

    // تسجيل الاستثناء
    $logFile = ROOT_PATH . '/storage/logs/exception_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, json_encode($error) . PHP_EOL, FILE_APPEND | LOCK_EX);

    // عرض صفحة خطأ مخصصة
    http_response_code(500);

    if ($_ENV['APP_DEBUG'] ?? false) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<h3>استثناء غير معالج</h3>";
        echo "<strong>الرسالة:</strong> " . $exception->getMessage() . "<br>";
        echo "<strong>الملف:</strong> " . $exception->getFile() . "<br>";
        echo "<strong>السطر:</strong> " . $exception->getLine() . "<br>";
        echo "<strong>التتبع:</strong><pre>" . $exception->getTraceAsString() . "</pre>";
        echo "</div>";
    } else {
        echo "<h1>خطأ في الخادم</h1>";
        echo "<p>حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقاً.</p>";
    }
});

// فحص وضع الصيانة
if (file_exists(ROOT_PATH . '/storage/maintenance.flag')) {
    http_response_code(503);
    include ROOT_PATH . '/app/Views/maintenance.php';
    exit;
}

// تحميل التوجيهات
$router = require_once ROOT_PATH . '/routes/web.php';

// الحصول على URI والطريقة
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';

// إزالة query string من URI
$requestUri = parse_url($requestUri, PHP_URL_PATH);

// تنظيف URI
$requestUri = rtrim($requestUri, '/');
if (empty($requestUri)) {
    $requestUri = '/';
}

// تسجيل الطلب
$logData = [
    'method' => $requestMethod,
    'uri' => $requestUri,
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'user_id' => $_SESSION['user_id'] ?? null,
    'time' => date('Y-m-d H:i:s')
];

$accessLogFile = ROOT_PATH . '/storage/logs/access_' . date('Y-m-d') . '.log';
file_put_contents($accessLogFile, json_encode($logData) . PHP_EOL, FILE_APPEND | LOCK_EX);

try {
    // تحميل التوجيهات
    $router = require_once ROOT_PATH . '/routes/web.php';

    // تشغيل التوجيه
    $router->dispatch($requestUri, $requestMethod);
} catch (Exception $e) {
    // في حالة الخطأ، عرض صفحة بسيطة
    include ROOT_PATH . '/public/simple.php';
    exit;
}

// تنظيف البيانات المؤقتة
if (isset($_SESSION['old_input'])) {
    unset($_SESSION['old_input']);
}

if (isset($_SESSION['validation_errors'])) {
    unset($_SESSION['validation_errors']);
}
