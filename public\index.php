<?php
// نظام إدارة المؤسسات غير الربحية
// ملف الدخول الرئيسي

// تحديد مسار الجذر
define('ROOT_PATH', dirname(__DIR__));

// بدء الجلسة
session_start();

// التحقق من وجود ملف التثبيت
if (file_exists(__DIR__ . '/install.php') && !file_exists(ROOT_PATH . '/.env')) {
    // إعادة توجيه إلى معالج التثبيت
    header('Location: /install.php');
    exit;
}

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// معالجة الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', $_ENV['APP_DEBUG'] ?? false);

// إعداد المنطقة الزمنية
date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'Asia/Riyadh');

// إعداد الترميز
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

// إعداد CSRF Token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// الحصول على URI والطريقة
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';

// إزالة query string من URI
$requestUri = parse_url($requestUri, PHP_URL_PATH);

// تنظيف URI
$requestUri = rtrim($requestUri, '/');
if (empty($requestUri)) {
    $requestUri = '/';
}

// توجيه بسيط
if ($requestUri === '/' || $requestUri === '/index.php') {
    // عرض الصفحة الرئيسية
    include ROOT_PATH . '/public/simple.php';
} elseif ($requestUri === '/install.php') {
    // عرض معالج التثبيت
    if (file_exists(__DIR__ . '/install.php')) {
        include __DIR__ . '/install.php';
    } elseif (file_exists(ROOT_PATH . '/install.php')) {
        include ROOT_PATH . '/install.php';
    } else {
        echo "ملف التثبيت غير موجود";
    }
} elseif ($requestUri === '/test.php') {
    // عرض صفحة الاختبار
    include ROOT_PATH . '/public/test.php';
} elseif ($requestUri === '/mysql-check.php') {
    // عرض أداة فحص MySQL
    include ROOT_PATH . '/public/mysql-check.php';
} elseif ($requestUri === '/check.php') {
    // عرض أداة فحص بسيطة
    include ROOT_PATH . '/public/check.php';
} else {
    // صفحة غير موجودة
    http_response_code(404);
    echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>404 - الصفحة غير موجودة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
</head>
<body class='bg-light d-flex align-items-center justify-content-center' style='min-height: 100vh;'>
    <div class='text-center'>
        <h1 class='display-1 text-muted'>404</h1>
        <h2>الصفحة غير موجودة</h2>
        <p class='text-muted'>الصفحة التي تبحث عنها غير موجودة.</p>
        <a href='/' class='btn btn-primary'>العودة للصفحة الرئيسية</a>
    </div>
</body>
</html>";
}
?>
