<?php
// تحديث متقدم لنظام CMS - إضافة الحوكمة والوسائط والمقالات
define('ROOT_PATH', dirname(__DIR__));
session_start();

// التحقق من تسجيل الدخول كمدير
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in'] || $_SESSION['user_role'] !== 'admin') {
    die('غير مسموح - يجب تسجيل الدخول كمدير');
}

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// إعداد قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

$success = false;
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // إنشاء جداول النظام المتقدم
        
        // 1. جدول تصنيفات الحوكمة
        $pdo->exec("CREATE TABLE IF NOT EXISTS governance_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            slug VARCHAR(200) NOT NULL,
            description TEXT,
            icon VARCHAR(50) DEFAULT 'fas fa-folder',
            color VARCHAR(7) DEFAULT '#007bff',
            sort_order INT DEFAULT 0,
            is_required TINYINT DEFAULT 0,
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // 2. جدول وثائق الحوكمة
        $pdo->exec("CREATE TABLE IF NOT EXISTS governance_documents (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL,
            slug VARCHAR(200) NOT NULL,
            description TEXT,
            category_id INT NOT NULL,
            file_path VARCHAR(255),
            file_name VARCHAR(200),
            file_size INT,
            file_type VARCHAR(50),
            version VARCHAR(20) DEFAULT '1.0',
            effective_date DATE,
            expiry_date DATE,
            approval_status VARCHAR(20) DEFAULT 'pending',
            approved_by INT,
            approved_at TIMESTAMP NULL,
            uploaded_by INT NOT NULL,
            download_count INT DEFAULT 0,
            is_public TINYINT DEFAULT 1,
            tags TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // 3. جدول مكتبة الوسائط
        $pdo->exec("CREATE TABLE IF NOT EXISTS media_library (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            file_path VARCHAR(255) NOT NULL,
            file_name VARCHAR(200) NOT NULL,
            original_name VARCHAR(200) NOT NULL,
            file_size INT NOT NULL,
            file_type VARCHAR(20) NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            dimensions VARCHAR(20),
            duration INT,
            thumbnail_path VARCHAR(255),
            alt_text VARCHAR(200),
            caption TEXT,
            tags TEXT,
            category VARCHAR(50) DEFAULT 'general',
            uploaded_by INT NOT NULL,
            is_featured TINYINT DEFAULT 0,
            download_count INT DEFAULT 0,
            view_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // 4. جدول تصنيفات المقالات
        $pdo->exec("CREATE TABLE IF NOT EXISTS article_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            slug VARCHAR(200) NOT NULL,
            description TEXT,
            color VARCHAR(7) DEFAULT '#28a745',
            icon VARCHAR(50) DEFAULT 'fas fa-file-alt',
            sort_order INT DEFAULT 0,
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // 5. جدول المقالات
        $pdo->exec("CREATE TABLE IF NOT EXISTS articles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL,
            slug VARCHAR(200) NOT NULL,
            excerpt TEXT,
            content LONGTEXT NOT NULL,
            featured_image VARCHAR(255),
            category_id INT,
            author_id INT NOT NULL,
            status VARCHAR(20) DEFAULT 'draft',
            is_featured TINYINT DEFAULT 0,
            is_sticky TINYINT DEFAULT 0,
            reading_time INT,
            views_count INT DEFAULT 0,
            likes_count INT DEFAULT 0,
            shares_count INT DEFAULT 0,
            meta_title VARCHAR(200),
            meta_description TEXT,
            tags TEXT,
            published_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // 6. جدول تعليقات المقالات
        $pdo->exec("CREATE TABLE IF NOT EXISTS article_comments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            article_id INT NOT NULL,
            parent_id INT NULL,
            author_name VARCHAR(100) NOT NULL,
            author_email VARCHAR(100) NOT NULL,
            author_website VARCHAR(200),
            content TEXT NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // إدراج تصنيفات الحوكمة الافتراضية (معايير المركز الوطني)
        $governance_categories = [
            ['الهيكل التنظيمي', 'organizational-structure', 'وثائق الهيكل التنظيمي ومجلس الإدارة', 'fas fa-sitemap', '#007bff', 1, 1],
            ['السياسات والإجراءات', 'policies-procedures', 'السياسات والإجراءات المؤسسية', 'fas fa-clipboard-list', '#28a745', 2, 1],
            ['التقارير المالية', 'financial-reports', 'التقارير المالية والميزانيات', 'fas fa-chart-line', '#ffc107', 3, 1],
            ['تقارير الأداء', 'performance-reports', 'تقارير الأداء والإنجازات', 'fas fa-trophy', '#17a2b8', 4, 1],
            ['الشفافية والإفصاح', 'transparency', 'وثائق الشفافية والإفصاح', 'fas fa-eye', '#6f42c1', 5, 1],
            ['إدارة المخاطر', 'risk-management', 'خطط وسياسات إدارة المخاطر', 'fas fa-shield-alt', '#dc3545', 6, 1],
            ['الامتثال والرقابة', 'compliance', 'وثائق الامتثال والرقابة الداخلية', 'fas fa-gavel', '#fd7e14', 7, 1],
            ['التدقيق الداخلي', 'internal-audit', 'تقارير التدقيق الداخلي', 'fas fa-search', '#20c997', 8, 1]
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO governance_categories (name, slug, description, icon, color, sort_order, is_required) VALUES (?, ?, ?, ?, ?, ?, ?)");
        foreach ($governance_categories as $category) {
            $stmt->execute($category);
        }
        
        // إدراج تصنيفات المقالات الافتراضية
        $article_categories = [
            ['مقالات عامة', 'general-articles', 'مقالات متنوعة', '#28a745', 'fas fa-file-alt', 1],
            ['دراسات وأبحاث', 'studies-research', 'الدراسات والأبحاث المتخصصة', '#007bff', 'fas fa-microscope', 2],
            ['قصص نجاح', 'success-stories', 'قصص النجاح والإنجازات', '#ffc107', 'fas fa-star', 3],
            ['التوعية والتثقيف', 'awareness', 'مقالات التوعية والتثقيف', '#17a2b8', 'fas fa-lightbulb', 4],
            ['الممارسات الجيدة', 'best-practices', 'أفضل الممارسات في القطاع', '#6f42c1', 'fas fa-thumbs-up', 5]
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO article_categories (name, slug, description, color, icon, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($article_categories as $category) {
            $stmt->execute($category);
        }
        
        // إنشاء مجلدات التحميل
        $upload_dirs = [
            'public/uploads/governance',
            'public/uploads/media',
            'public/uploads/articles',
            'public/uploads/thumbnails'
        ];
        
        foreach ($upload_dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        $success = true;
        
    } catch (PDOException $e) {
        $error = "خطأ في إنشاء الجداول: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث النظام المتقدم - CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .update-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            max-width: 800px;
            margin: 50px auto;
        }
        .feature-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="update-card">
            <div class="text-center mb-4">
                <i class="fas fa-rocket fa-3x text-primary mb-3"></i>
                <h2>تحديث النظام المتقدم</h2>
                <p class="text-muted">إضافة الحوكمة ومكتبة الوسائط والمقالات</p>
            </div>

            <?php if ($success): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>تم التحديث بنجاح!</h5>
                <p>تم إنشاء جميع الجداول والمجلدات المطلوبة.</p>
                <hr>
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <a href="/governance-admin.php" class="btn btn-primary w-100">
                            <i class="fas fa-gavel me-1"></i>
                            إدارة الحوكمة
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="/media-library.php" class="btn btn-success w-100">
                            <i class="fas fa-photo-video me-1"></i>
                            مكتبة الوسائط
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="/articles-admin.php" class="btn btn-info w-100">
                            <i class="fas fa-file-alt me-1"></i>
                            إدارة المقالات
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="/dashboard.php" class="btn btn-secondary w-100">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
            <?php elseif ($error): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>خطأ في التحديث</h5>
                <p><?= htmlspecialchars($error) ?></p>
            </div>
            <?php else: ?>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-gavel fa-2x text-primary mb-2"></i>
                        <h5>قسم الحوكمة</h5>
                        <p class="small">معايير المركز الوطني للقطاع غير الربحي</p>
                        <ul class="list-unstyled small text-start">
                            <li>• 8 تصنيفات أساسية</li>
                            <li>• رفع الوثائق والملفات</li>
                            <li>• نظام الموافقات</li>
                            <li>• تتبع الإصدارات</li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-photo-video fa-2x text-success mb-2"></i>
                        <h5>مكتبة الوسائط</h5>
                        <p class="small">إدارة شاملة للصور والفيديوهات</p>
                        <ul class="list-unstyled small text-start">
                            <li>• رفع متعدد الملفات</li>
                            <li>• معاينة مصغرة</li>
                            <li>• تصنيف ووسوم</li>
                            <li>• إحصائيات التحميل</li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-file-alt fa-2x text-info mb-2"></i>
                        <h5>نظام المقالات</h5>
                        <p class="small">منفصل عن الأخبار مع مميزات متقدمة</p>
                        <ul class="list-unstyled small text-start">
                            <li>• تصنيفات متخصصة</li>
                            <li>• نظام التعليقات</li>
                            <li>• SEO محسن</li>
                            <li>• إحصائيات مفصلة</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <form method="POST">
                <div class="alert alert-warning">
                    <h5><i class="fas fa-info-circle me-2"></i>ما سيتم إنشاؤه:</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>الجداول:</strong>
                            <ul class="mb-0 small">
                                <li>governance_categories</li>
                                <li>governance_documents</li>
                                <li>media_library</li>
                                <li>article_categories</li>
                                <li>articles</li>
                                <li>article_comments</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <strong>المجلدات:</strong>
                            <ul class="mb-0 small">
                                <li>uploads/governance</li>
                                <li>uploads/media</li>
                                <li>uploads/articles</li>
                                <li>uploads/thumbnails</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-rocket me-2"></i>
                        بدء التحديث المتقدم
                    </button>
                </div>
            </form>
            <?php endif; ?>
            
            <div class="text-center mt-3">
                <a href="/dashboard.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
    </div>
</body>
</html>
