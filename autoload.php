<?php
/**
 * Autoloader مخصص للنظام
 * يستخدم في حالة عدم توفر Composer
 */

spl_autoload_register(function ($class) {
    // تحديد المسارات الأساسية
    $paths = [
        'App\\' => __DIR__ . '/app/',
        'Core\\' => __DIR__ . '/core/',
    ];
    
    // البحث عن الكلاس في المسارات المحددة
    foreach ($paths as $prefix => $base_dir) {
        // التحقق من أن الكلاس يبدأ بالبادئة
        $len = strlen($prefix);
        if (strncmp($prefix, $class, $len) !== 0) {
            continue;
        }
        
        // الحصول على اسم الكلاس النسبي
        $relative_class = substr($class, $len);
        
        // تحويل namespace إلى مسار ملف
        $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
        
        // تحميل الملف إذا وجد
        if (file_exists($file)) {
            require $file;
            return;
        }
    }
});

// تحميل الملفات الأساسية المطلوبة
$required_files = [
    __DIR__ . '/core/Database/Connection.php',
    __DIR__ . '/core/Database/Model.php',
    __DIR__ . '/core/Router/Router.php',
    __DIR__ . '/core/Auth/Auth.php',
    __DIR__ . '/core/Validation/Validator.php',
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        require_once $file;
    }
}
