<?php

namespace Core\Auth;

// use Firebase\JWT\JWT;
// use Firebase\JWT\Key;
use App\Models\User;

class Auth
{
    private static $instance = null;
    private $user = null;
    private $config;

    private function __construct()
    {
        $this->config = require __DIR__ . '/../../config/app.php';
        $this->initializeSession();
    }

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function initializeSession(): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function attempt(string $email, string $password, bool $remember = false): bool
    {
        $userModel = new User();
        $user = $userModel->findBy('email', $email);

        if ($user && $this->verifyPassword($password, $user['password'])) {
            if ($user['is_active'] == 0) {
                return false; // Account is deactivated
            }

            $this->login($user, $remember);
            $this->updateLastLogin($user['id']);
            return true;
        }

        $this->recordFailedAttempt($email);
        return false;
    }

    public function login(array $user, bool $remember = false): void
    {
        $this->user = $user;
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['logged_in'] = true;

        if ($remember) {
            $token = $this->generateRememberToken($user['id']);
            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days
        }

        $this->regenerateSession();
    }

    public function logout(): void
    {
        $this->user = null;
        session_destroy();

        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
    }

    public function check(): bool
    {
        if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
            if ($this->user === null) {
                $this->loadUser($_SESSION['user_id']);
            }
            return true;
        }

        // Check remember token
        if (isset($_COOKIE['remember_token'])) {
            return $this->loginFromRememberToken($_COOKIE['remember_token']);
        }

        return false;
    }

    public function guest(): bool
    {
        return !$this->check();
    }

    public function user(): ?array
    {
        if ($this->check()) {
            return $this->user;
        }
        return null;
    }

    public function id(): ?int
    {
        $user = $this->user();
        return $user ? $user['id'] : null;
    }

    public function hasRole(string $role): bool
    {
        $user = $this->user();
        return $user && $user['role'] === $role;
    }

    public function hasPermission(string $permission): bool
    {
        $user = $this->user();
        if (!$user) {
            return false;
        }

        // Load user permissions
        $userModel = new User();
        $permissions = $userModel->getUserPermissions($user['id']);

        return in_array($permission, $permissions);
    }

    public function generateJWT(array $user): string
    {
        // JWT مؤقتاً معطل - يمكن تفعيله بعد تثبيت Firebase JWT
        $payload = [
            'iss' => $this->config['url'] ?? 'localhost',
            'aud' => $this->config['url'] ?? 'localhost',
            'iat' => time(),
            'exp' => time() + ($this->config['security']['jwt_ttl'] ?? 3600),
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role'],
        ];

        // إرجاع token مؤقت
        return base64_encode(json_encode($payload));
    }

    public function validateJWT(string $token): ?array
    {
        try {
            // فك تشفير Token المؤقت
            $decoded = json_decode(base64_decode($token), true);

            // التحقق من انتهاء الصلاحية
            if (isset($decoded['exp']) && $decoded['exp'] < time()) {
                return null;
            }

            return $decoded;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    private function loadUser(int $userId): void
    {
        $userModel = new User();
        $this->user = $userModel->find($userId);
    }

    private function updateLastLogin(int $userId): void
    {
        $userModel = new User();
        $userModel->update($userId, ['last_login' => date('Y-m-d H:i:s')]);
    }

    private function recordFailedAttempt(string $email): void
    {
        // Record failed login attempt for security monitoring
        $attempts = $_SESSION['login_attempts'][$email] ?? 0;
        $_SESSION['login_attempts'][$email] = $attempts + 1;
        $_SESSION['last_attempt_time'][$email] = time();
    }

    private function isAccountLocked(string $email): bool
    {
        $attempts = $_SESSION['login_attempts'][$email] ?? 0;
        $lastAttempt = $_SESSION['last_attempt_time'][$email] ?? 0;
        $lockoutDuration = $this->config['security']['login_lockout_duration'];
        $maxAttempts = $this->config['security']['login_attempts'];

        if ($attempts >= $maxAttempts) {
            return (time() - $lastAttempt) < $lockoutDuration;
        }

        return false;
    }

    private function generateRememberToken(int $userId): string
    {
        $token = bin2hex(random_bytes(32));

        // Store token in database
        $userModel = new User();
        $userModel->update($userId, ['remember_token' => hash('sha256', $token)]);

        return $token;
    }

    private function loginFromRememberToken(string $token): bool
    {
        $hashedToken = hash('sha256', $token);
        $userModel = new User();
        $user = $userModel->findBy('remember_token', $hashedToken);

        if ($user && $user['is_active'] == 1) {
            $this->login($user);
            return true;
        }

        return false;
    }

    private function regenerateSession(): void
    {
        session_regenerate_id(true);
    }
}
