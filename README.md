# نظام إدارة مؤسسات القطاع غير الربحي

نظام متكامل لإدارة مؤسسات القطاع غير الربحي شبيه بنظام "رافد"، مبني باستخدام PHP وMySQL مع نموذج MVC.

## المميزات الرئيسية

### 🏢 إدارة المنظمات
- تسجيل وإدارة بيانات المنظمات والجمعيات
- متابعة تراخيص المنظمات وتواريخ انتهائها
- إدارة الوثائق والمستندات الرسمية
- تقييم أداء المنظمات

### 👥 إدارة المستخدمين والصلاحيات
- نظام أدوار وصلاحيات متقدم
- إدارة المستخدمين والموظفين
- نظام مصادقة آمن مع JWT
- سجل تدقيق شامل

### 💼 إدارة الموارد البشرية
- إدارة الموظفين والمتطوعين
- نظام الرواتب والمكافآت
- متابعة الحضور والانصراف
- إدارة الإجازات والطلبات

### 📊 إدارة المشاريع والبرامج
- تخطيط وتنفيذ المشاريع
- متابعة المهام والأنشطة
- إدارة الميزانيات والموارد
- تقارير الأداء والإنجاز

### 💰 إدارة التبرعات والشؤون المالية
- تسجيل التبرعات النقدية والعينية
- إدارة الحسابات المالية
- نظام القيود المحاسبية
- التقارير المالية المتقدمة

### 📦 إدارة المستودعات والمخزون
- تتبع المواد والأصناف
- إدارة المخازن والمستودعات
- حركات الصرف والاستلام
- تقارير المخزون

### 📅 إدارة الاجتماعات والعقود
- جدولة الاجتماعات
- إدارة العقود والاتفاقيات
- نظام الموافقات الإلكترونية
- أرشيف الوثائق

## متطلبات النظام

- PHP 8.0 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx مع mod_rewrite
- Composer لإدارة التبعيات
- Node.js (اختياري للتطوير)

## التثبيت

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/nonprofit-cms.git
cd nonprofit-cms
```

### 2. تثبيت التبعيات
```bash
composer install
```

### 3. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
mysql -u root -p
CREATE DATABASE nonprofit_cms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. إعداد متغيرات البيئة
```bash
cp .env.example .env
```

قم بتحرير ملف `.env` وإدخال بيانات قاعدة البيانات:
```env
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=nonprofit_cms
DB_USERNAME=root
DB_PASSWORD=your_password
```

### 5. تشغيل الترحيلات والبيانات الأولية
```bash
# تشغيل ملفات SQL
mysql -u root -p nonprofit_cms < database/migrations/001_create_initial_tables.sql
mysql -u root -p nonprofit_cms < database/seeds/001_initial_data.sql
```

### 6. إعداد الصلاحيات
```bash
chmod -R 755 storage/
chmod -R 755 public/uploads/
```

### 7. تشغيل الخادم
```bash
# للتطوير
php -S localhost:8000 -t public

# أو استخدام Composer
composer serve
```

## الاستخدام

### تسجيل الدخول الأولي
- الرابط: `http://localhost:8000/auth/login`
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `password`

### الوحدات الرئيسية

#### 1. لوحة التحكم
- `/dashboard` - الصفحة الرئيسية
- عرض الإحصائيات والتقارير السريعة
- الإشعارات والمهام المعلقة

#### 2. إدارة المستخدمين
- `/users` - قائمة المستخدمين
- `/users/create` - إضافة مستخدم جديد
- `/roles` - إدارة الأدوار والصلاحيات

#### 3. إدارة المنظمات
- `/organizations` - قائمة المنظمات
- `/organizations/create` - تسجيل منظمة جديدة

#### 4. الموارد البشرية
- `/employees` - إدارة الموظفين
- `/volunteers` - إدارة المتطوعين

#### 5. المشاريع
- `/projects` - قائمة المشاريع
- `/projects/create` - إنشاء مشروع جديد

#### 6. الشؤون المالية
- `/finance` - الحسابات المالية
- `/donations` - إدارة التبرعات
- `/inventory` - إدارة المخزون

## البنية التقنية

### نموذج MVC
```
app/
├── Controllers/     # المتحكمات
├── Models/         # النماذج
├── Views/          # القوالب
├── Middleware/     # الوسطاء
├── Services/       # الخدمات
└── Helpers/        # المساعدات

core/
├── Database/       # قاعدة البيانات
├── Router/         # التوجيه
├── Auth/          # المصادقة
└── Validation/    # التحقق من صحة البيانات
```

### الأمان
- حماية CSRF
- تشفير كلمات المرور
- تنظيف المدخلات
- حماية من SQL Injection
- نظام صلاحيات متقدم

### واجهة API
```
POST /api/v1/auth/login     # تسجيل الدخول
GET  /api/v1/organizations  # قائمة المنظمات
GET  /api/v1/projects       # قائمة المشاريع
GET  /api/v1/donations      # قائمة التبرعات
```

## التطوير

### إضافة وحدة جديدة
1. إنشاء Model في `app/Models/`
2. إنشاء Controller في `app/Controllers/`
3. إنشاء Views في `app/Views/`
4. إضافة Routes في `routes/web.php`
5. إنشاء Migration في `database/migrations/`

### إضافة صلاحية جديدة
```sql
INSERT INTO permissions (name, display_name, module) 
VALUES ('module.action', 'وصف الصلاحية', 'module_name');
```

### إضافة Middleware جديد
```php
<?php
namespace App\Middleware;

class CustomMiddleware
{
    public function handle(): bool
    {
        // منطق الـ middleware
        return true;
    }
}
```

## النشر على الخادم

### 1. رفع الملفات
```bash
# رفع جميع الملفات عدا:
# - .env (إنشاء نسخة جديدة)
# - storage/ (إعداد الصلاحيات)
# - vendor/ (تشغيل composer install)
```

### 2. إعداد Apache/Nginx
```apache
# Apache .htaccess
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^.*$ /index.php [L,QSA]
```

### 3. إعداد SSL
```bash
# استخدام Let's Encrypt
certbot --apache -d yourdomain.com
```

### 4. إعداد Cron Jobs
```bash
# تنظيف الملفات المؤقتة
0 2 * * * /usr/bin/php /path/to/project/scripts/cleanup.php

# إرسال التقارير الدورية
0 8 * * 1 /usr/bin/php /path/to/project/scripts/weekly_reports.php
```

## الدعم والمساهمة

### الإبلاغ عن الأخطاء
- استخدم GitHub Issues
- قدم وصفاً مفصلاً للمشكلة
- أرفق لقطات الشاشة إن أمكن

### المساهمة في التطوير
1. Fork المشروع
2. إنشاء branch جديد
3. تطبيق التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

## الدعم التقني

للحصول على الدعم التقني:
- البريد الإلكتروني: <EMAIL>
- الوثائق: [docs.example.com](https://docs.example.com)
- المجتمع: [community.example.com](https://community.example.com)

---

**نظام إدارة مؤسسات القطاع غير الربحي** - حلول تقنية متقدمة لخدمة المجتمع
