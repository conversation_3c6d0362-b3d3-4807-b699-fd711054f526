<?php
session_start();

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// إنشاء session_id للزوار غير المسجلين
if (!isset($_SESSION['cart_session_id'])) {
    $_SESSION['cart_session_id'] = session_id();
}

// جلب الفئات النشطة
$categories = $pdo->query("
    SELECT * FROM product_categories 
    WHERE is_active = 1 
    ORDER BY sort_order, name
")->fetchAll();

// جلب المنتجات المميزة
$featured_products = $pdo->query("
    SELECT p.*, c.name as category_name,
           (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
    FROM products p
    LEFT JOIN product_categories c ON p.category_id = c.id
    WHERE p.status = 'active' AND p.featured = 1
    ORDER BY p.created_at DESC
    LIMIT 8
")->fetchAll();

// جلب أحدث المنتجات
$latest_products = $pdo->query("
    SELECT p.*, c.name as category_name,
           (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
    FROM products p
    LEFT JOIN product_categories c ON p.category_id = c.id
    WHERE p.status = 'active'
    ORDER BY p.created_at DESC
    LIMIT 12
")->fetchAll();

// جلب عدد المنتجات في السلة
$cart_count = 0;
if (isset($_SESSION['user_id'])) {
    $customer = $pdo->prepare("SELECT id FROM customers WHERE user_id = ?");
    $customer->execute([$_SESSION['user_id']]);
    $customer_data = $customer->fetch();
    
    if ($customer_data) {
        $cart_stmt = $pdo->prepare("SELECT SUM(quantity) FROM shopping_cart WHERE customer_id = ?");
        $cart_stmt->execute([$customer_data['id']]);
        $cart_count = $cart_stmt->fetchColumn() ?: 0;
    }
} else {
    $cart_stmt = $pdo->prepare("SELECT SUM(quantity) FROM shopping_cart WHERE session_id = ?");
    $cart_stmt->execute([$_SESSION['cart_session_id']]);
    $cart_count = $cart_stmt->fetchColumn() ?: 0;
}

// جلب بوابات الدفع النشطة
$payment_gateways = $pdo->query("
    SELECT * FROM payment_gateways 
    WHERE is_active = 1 
    ORDER BY sort_order
")->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المتجر الإلكتروني - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #6f42c1, #e91e63); }
        .hero-section { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .product-card { 
            border-radius: 15px; 
            transition: transform 0.3s, box-shadow 0.3s;
            border: none;
            overflow: hidden;
        }
        .product-card:hover { 
            transform: translateY(-5px); 
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .product-image {
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
        }
        .price-original { text-decoration: line-through; color: #6c757d; }
        .price-sale { color: #dc3545; font-weight: bold; }
        .category-card {
            background: linear-gradient(45deg, #17a2b8, #20c997);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s;
        }
        .category-card:hover {
            transform: scale(1.05);
            text-decoration: none;
            color: white;
        }
        .payment-gateway {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s;
        }
        .payment-gateway:hover {
            border-color: #6f42c1;
            background-color: #f8f9fa;
        }
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .footer {
            background: #343a40;
            color: white;
            padding: 40px 0;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/store.php">
                <i class="fas fa-store me-2"></i>المتجر الإلكتروني
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#featured">المنتجات المميزة</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#categories">الفئات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#latest">أحدث المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#payment">طرق الدفع</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="/cart.php">
                            <i class="fas fa-shopping-cart"></i>
                            السلة
                            <?php if ($cart_count > 0): ?>
                            <span class="cart-badge"><?= $cart_count ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <?php if (isset($_SESSION['user_id'])): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard.php">
                            <i class="fas fa-user me-1"></i><?= $_SESSION['user_name'] ?? 'حسابي' ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/logout.php">
                            <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                        </a>
                    </li>
                    <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="/login.php">
                            <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 mb-4">🛒 مرحباً بك في متجرنا الإلكتروني</h1>
            <p class="lead mb-4">اكتشف مجموعة واسعة من المنتجات عالية الجودة مع خيارات دفع متنوعة وآمنة</p>
            <div class="row justify-content-center">
                <div class="col-md-3 mb-3">
                    <div class="d-flex align-items-center justify-content-center">
                        <i class="fas fa-shipping-fast fa-2x me-3"></i>
                        <div>
                            <h6 class="mb-0">شحن سريع</h6>
                            <small>توصيل في نفس اليوم</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="d-flex align-items-center justify-content-center">
                        <i class="fas fa-shield-alt fa-2x me-3"></i>
                        <div>
                            <h6 class="mb-0">دفع آمن</h6>
                            <small>حماية كاملة للمعاملات</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="d-flex align-items-center justify-content-center">
                        <i class="fas fa-undo fa-2x me-3"></i>
                        <div>
                            <h6 class="mb-0">إرجاع مجاني</h6>
                            <small>خلال 30 يوم</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="d-flex align-items-center justify-content-center">
                        <i class="fas fa-headset fa-2x me-3"></i>
                        <div>
                            <h6 class="mb-0">دعم 24/7</h6>
                            <small>خدمة عملاء متميزة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container mt-5">
        <!-- الفئات -->
        <?php if (!empty($categories)): ?>
        <section id="categories" class="mb-5">
            <h2 class="text-center mb-4">
                <i class="fas fa-tags me-2"></i>تسوق حسب الفئة
            </h2>
            <div class="row">
                <?php foreach (array_slice($categories, 0, 6) as $category): ?>
                <div class="col-md-4 col-lg-2 mb-3">
                    <a href="/category.php?id=<?= $category['id'] ?>" class="category-card d-block">
                        <i class="fas fa-cube fa-2x mb-2"></i>
                        <h6><?= htmlspecialchars($category['name']) ?></h6>
                    </a>
                </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php endif; ?>

        <!-- المنتجات المميزة -->
        <?php if (!empty($featured_products)): ?>
        <section id="featured" class="mb-5">
            <h2 class="text-center mb-4">
                <i class="fas fa-star me-2"></i>المنتجات المميزة
            </h2>
            <div class="row">
                <?php foreach ($featured_products as $product): ?>
                <div class="col-md-6 col-lg-3 mb-4">
                    <div class="card product-card h-100">
                        <div class="product-image">
                            <?php if ($product['primary_image']): ?>
                            <img src="<?= htmlspecialchars($product['primary_image']) ?>" class="card-img-top" alt="<?= htmlspecialchars($product['name']) ?>">
                            <?php else: ?>
                            <i class="fas fa-image fa-3x"></i>
                            <?php endif; ?>
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title"><?= htmlspecialchars($product['name']) ?></h6>
                            <p class="card-text text-muted small flex-grow-1">
                                <?= htmlspecialchars(substr($product['short_description'] ?? '', 0, 80)) ?>
                            </p>
                            <div class="price mb-2">
                                <?php if ($product['sale_price']): ?>
                                <span class="price-original"><?= number_format($product['price'], 2) ?> ر.س</span><br>
                                <span class="price-sale"><?= number_format($product['sale_price'], 2) ?> ر.س</span>
                                <?php else: ?>
                                <span class="fw-bold"><?= number_format($product['price'], 2) ?> ر.س</span>
                                <?php endif; ?>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary btn-sm flex-grow-1" onclick="addToCart(<?= $product['id'] ?>)">
                                    <i class="fas fa-cart-plus me-1"></i>أضف للسلة
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="viewProduct(<?= $product['id'] ?>)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php endif; ?>

        <!-- أحدث المنتجات -->
        <?php if (!empty($latest_products)): ?>
        <section id="latest" class="mb-5">
            <h2 class="text-center mb-4">
                <i class="fas fa-clock me-2"></i>أحدث المنتجات
            </h2>
            <div class="row">
                <?php foreach ($latest_products as $product): ?>
                <div class="col-md-6 col-lg-3 mb-4">
                    <div class="card product-card h-100">
                        <div class="product-image">
                            <?php if ($product['primary_image']): ?>
                            <img src="<?= htmlspecialchars($product['primary_image']) ?>" class="card-img-top" alt="<?= htmlspecialchars($product['name']) ?>">
                            <?php else: ?>
                            <i class="fas fa-image fa-3x"></i>
                            <?php endif; ?>
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title"><?= htmlspecialchars($product['name']) ?></h6>
                            <p class="card-text text-muted small flex-grow-1">
                                <?= htmlspecialchars(substr($product['short_description'] ?? '', 0, 80)) ?>
                            </p>
                            <div class="price mb-2">
                                <?php if ($product['sale_price']): ?>
                                <span class="price-original"><?= number_format($product['price'], 2) ?> ر.س</span><br>
                                <span class="price-sale"><?= number_format($product['sale_price'], 2) ?> ر.س</span>
                                <?php else: ?>
                                <span class="fw-bold"><?= number_format($product['price'], 2) ?> ر.س</span>
                                <?php endif; ?>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary btn-sm flex-grow-1" onclick="addToCart(<?= $product['id'] ?>)">
                                    <i class="fas fa-cart-plus me-1"></i>أضف للسلة
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="viewProduct(<?= $product['id'] ?>)">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php endif; ?>

        <!-- طرق الدفع -->
        <?php if (!empty($payment_gateways)): ?>
        <section id="payment" class="mb-5">
            <h2 class="text-center mb-4">
                <i class="fas fa-credit-card me-2"></i>طرق الدفع المتاحة
            </h2>
            <div class="row justify-content-center">
                <?php foreach ($payment_gateways as $gateway): ?>
                <div class="col-md-4 col-lg-2 mb-3">
                    <div class="payment-gateway">
                        <i class="fas fa-credit-card fa-2x mb-2 text-primary"></i>
                        <h6><?= htmlspecialchars($gateway['name']) ?></h6>
                        <small class="text-muted"><?= $gateway['fees_percentage'] ?>% رسوم</small>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <div class="text-center mt-3">
                <p class="text-muted">جميع المعاملات محمية بأعلى معايير الأمان</p>
            </div>
        </section>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>المتجر الإلكتروني</h5>
                    <p>متجرك الموثوق للتسوق الإلكتروني مع أفضل المنتجات وأسعار تنافسية.</p>
                </div>
                <div class="col-md-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="/store.php" class="text-light">الرئيسية</a></li>
                        <li><a href="/products.php" class="text-light">جميع المنتجات</a></li>
                        <li><a href="/cart.php" class="text-light">سلة التسوق</a></li>
                        <li><a href="/contact.php" class="text-light">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>تواصل معنا</h5>
                    <p><i class="fas fa-phone me-2"></i>+966 50 123 4567</p>
                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram fa-lg"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-whatsapp fa-lg"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 المتجر الإلكتروني. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addToCart(productId) {
            // سيتم تنفيذ إضافة المنتج للسلة
            fetch('/add-to-cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `product_id=${productId}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث عداد السلة
                    updateCartCount();
                    
                    // إظهار رسالة نجاح
                    showToast('تم إضافة المنتج للسلة بنجاح!', 'success');
                } else {
                    showToast(data.message || 'خطأ في إضافة المنتج', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('خطأ في الاتصال', 'error');
            });
        }

        function viewProduct(productId) {
            window.location.href = `/product.php?id=${productId}`;
        }

        function updateCartCount() {
            fetch('/get-cart-count.php')
                .then(response => response.json())
                .then(data => {
                    const badge = document.querySelector('.cart-badge');
                    if (data.count > 0) {
                        if (badge) {
                            badge.textContent = data.count;
                        } else {
                            // إنشاء badge جديد
                            const cartLink = document.querySelector('a[href="/cart.php"]');
                            const newBadge = document.createElement('span');
                            newBadge.className = 'cart-badge';
                            newBadge.textContent = data.count;
                            cartLink.appendChild(newBadge);
                        }
                    } else if (badge) {
                        badge.remove();
                    }
                });
        }

        function showToast(message, type) {
            // إنشاء toast بسيط
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            
            document.body.appendChild(toast);
            
            // إزالة التوست بعد 3 ثواني
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }

        // تحديث عداد السلة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();
        });
    </script>
</body>
</html>
