<?php
// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];

    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // التحقق من الاتصال
    $pdo->query("SELECT 1");
    echo "<h2>✅ الاتصال بقاعدة البيانات ناجح</h2>";
    echo "<p>قاعدة البيانات: <strong>$dbname</strong></p>";
} catch (Exception $e) {
    die("<h2>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</h2>");
}

// قراءة وتنفيذ ملف SQL
$sql_file = '../database/migrations/002_create_hr_tables.sql';

if (!file_exists($sql_file)) {
    die("<h2>❌ ملف SQL غير موجود: $sql_file</h2>");
}

$sql_content = file_get_contents($sql_file);

// تقسيم الاستعلامات
$queries = array_filter(array_map('trim', explode(';', $sql_content)));

echo "<h2>🚀 بدء إنشاء جداول نظام الموارد البشرية...</h2>";

$success_count = 0;
$error_count = 0;

foreach ($queries as $query) {
    if (empty($query) || strpos($query, '--') === 0) {
        continue;
    }

    try {
        $pdo->exec($query);
        $success_count++;

        // استخراج اسم الجدول من الاستعلام
        if (preg_match('/CREATE TABLE\s+(\w+)/i', $query, $matches)) {
            echo "<p>✅ تم إنشاء جدول: <strong>{$matches[1]}</strong></p>";
        } elseif (preg_match('/INSERT INTO\s+(\w+)/i', $query, $matches)) {
            echo "<p>✅ تم إدراج بيانات في جدول: <strong>{$matches[1]}</strong></p>";
        } else {
            echo "<p>✅ تم تنفيذ استعلام بنجاح</p>";
        }

    } catch (Exception $e) {
        $error_count++;
        echo "<p>❌ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "</p>";
        echo "<pre style='background: #f8f8f8; padding: 10px; border-radius: 5px;'>" . htmlspecialchars(substr($query, 0, 200)) . "...</pre>";
    }
}

echo "<hr>";
echo "<h3>📊 ملخص العملية:</h3>";
echo "<p>✅ استعلامات ناجحة: <strong>$success_count</strong></p>";
echo "<p>❌ استعلامات فاشلة: <strong>$error_count</strong></p>";

if ($error_count === 0) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 تم إنشاء نظام الموارد البشرية بنجاح!</h3>";
    echo "<p>يمكنك الآن الوصول إلى:</p>";
    echo "<ul>";
    echo "<li><a href='/employees.php' target='_blank'>إدارة الموظفين</a></li>";
    echo "<li><a href='/leave-requests.php' target='_blank'>إدارة الإجازات</a></li>";
    echo "<li><a href='/payroll.php' target='_blank'>إدارة الرواتب</a></li>";
    echo "<li><a href='/messages.php' target='_blank'>المراسلة الداخلية</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠️ تحذير: حدثت بعض الأخطاء</h3>";
    echo "<p>يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل المتابعة.</p>";
    echo "</div>";
}

// التحقق من الجداول المنشأة
echo "<h3>📋 الجداول المنشأة:</h3>";
$tables = ['departments', 'positions', 'employees', 'leave_requests', 'payroll', 'internal_messages', 'message_recipients', 'notifications'];

foreach ($tables as $table) {
    try {
        $result = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $result->fetchColumn();
        echo "<p>✅ جدول <strong>$table</strong>: $count سجل</p>";
    } catch (Exception $e) {
        echo "<p>❌ جدول <strong>$table</strong>: غير موجود أو خطأ</p>";
    }
}

echo "<hr>";
echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='/dashboard.php'>لوحة التحكم الرئيسية</a></li>";
echo "<li><a href='/employees.php'>إدارة الموظفين</a></li>";
echo "<li><a href='/leave-requests.php'>إدارة الإجازات</a></li>";
echo "<li><a href='/payroll.php'>إدارة الرواتب</a></li>";
echo "<li><a href='/messages.php'>المراسلة الداخلية</a></li>";
echo "</ul>";

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
ul { margin: 10px 0; padding-left: 20px; }
</style>";
?>
