<?php
// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    echo "<h2>✅ الاتصال بقاعدة البيانات ناجح</h2>";
    echo "<p>قاعدة البيانات: <strong>$dbname</strong></p>";
} catch (Exception $e) {
    die("<h2>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</h2>");
}

echo "<h2>🔧 إصلاح جدول المستخدمين...</h2>";

// حذف الجداول المعطلة أولاً
try {
    $pdo->exec("DROP TABLE IF EXISTS users");
    echo "<p>🗑️ تم حذف جدول المستخدمين القديم</p>";
} catch (Exception $e) {
    echo "<p>⚠️ تحذير: " . $e->getMessage() . "</p>";
}

try {
    $pdo->exec("DROP TABLE IF EXISTS sessions");
    echo "<p>🗑️ تم حذف جدول الجلسات القديم</p>";
} catch (Exception $e) {
    echo "<p>⚠️ تحذير: " . $e->getMessage() . "</p>";
}

// إنشاء جدول المستخدمين المبسط
try {
    $pdo->exec("
        CREATE TABLE users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'manager', 'employee') DEFAULT 'employee',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p>✅ تم إنشاء جدول: <strong>users</strong> (مبسط)</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول users: " . $e->getMessage() . "</p>";
}

// إنشاء مستخدم إداري افتراضي
try {
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->prepare("
        INSERT INTO users (name, email, password, role, is_active) VALUES (?, ?, ?, 'admin', 1)
    ")->execute(['المدير العام', '<EMAIL>', $admin_password]);
    
    echo "<p>✅ تم إنشاء المستخدم الإداري الافتراضي</p>";
    echo "<div style='background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>بيانات الدخول:</strong><br>";
    echo "البريد الإلكتروني: <strong><EMAIL></strong><br>";
    echo "كلمة المرور: <strong>admin123</strong>";
    echo "</div>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء المستخدم الإداري: " . $e->getMessage() . "</p>";
}

// إنشاء بعض المستخدمين التجريبيين
try {
    $test_password = password_hash('123456', PASSWORD_DEFAULT);
    
    $test_users = [
        ['أحمد محمد', '<EMAIL>', 'manager'],
        ['فاطمة علي', '<EMAIL>', 'employee'],
        ['محمد سالم', '<EMAIL>', 'employee'],
        ['نورا أحمد', '<EMAIL>', 'employee']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
    
    foreach ($test_users as $user) {
        $stmt->execute([$user[0], $user[1], $test_password, $user[2]]);
    }
    
    echo "<p>✅ تم إنشاء " . count($test_users) . " مستخدمين تجريبيين</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء المستخدمين التجريبيين: " . $e->getMessage() . "</p>";
}

// التحقق من الجداول الموجودة
echo "<hr>";
echo "<h3>📋 الجداول الموجودة في قاعدة البيانات:</h3>";

try {
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($tables as $table) {
        $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
        echo "<p>✅ جدول <strong>$table</strong>: $count سجل</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ خطأ في جلب قائمة الجداول: " . $e->getMessage() . "</p>";
}

// عرض المستخدمين المنشأين
echo "<hr>";
echo "<h3>👥 المستخدمين المنشأين:</h3>";

try {
    $users = $pdo->query("SELECT id, name, email, role, is_active FROM users")->fetchAll();
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th>ID</th><th>الاسم</th><th>البريد الإلكتروني</th><th>الدور</th><th>نشط</th></tr>";
    
    foreach ($users as $user) {
        $status = $user['is_active'] ? '✅ نشط' : '❌ غير نشط';
        $role_ar = [
            'admin' => 'مدير',
            'manager' => 'مدير قسم', 
            'employee' => 'موظف'
        ][$user['role']] ?? $user['role'];
        
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>$role_ar</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في جلب المستخدمين: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🎉 تم إصلاح جدول المستخدمين بنجاح!</h3>";
echo "<p>يمكنك الآن:</p>";
echo "<ul>";
echo "<li>تسجيل الدخول باستخدام: <strong><EMAIL></strong> / <strong>admin123</strong></li>";
echo "<li><a href='/employees.php' target='_blank'>الوصول لإدارة الموظفين</a></li>";
echo "<li><a href='/login.php' target='_blank'>صفحة تسجيل الدخول</a></li>";
echo "</ul>";
echo "</div>";

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
ul { margin: 10px 0; padding-left: 20px; }
table { font-size: 14px; }
th, td { padding: 8px; text-align: right; }
th { background: #007bff; color: white; }
</style>";
?>
