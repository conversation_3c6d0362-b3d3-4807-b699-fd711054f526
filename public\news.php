<?php
// صفحة الأخبار للزوار
define('ROOT_PATH', dirname(__DIR__));

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// إعداد قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// معاملات البحث والتصفية
$category_id = $_GET['category'] ?? null;
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 6;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = ["a.status = 'published'"];
$params = [];

if ($category_id) {
    $where_conditions[] = "a.category_id = ?";
    $params[] = $category_id;
}

if ($search) {
    $where_conditions[] = "(a.title LIKE ? OR a.excerpt LIKE ? OR a.content LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = implode(' AND ', $where_conditions);

// جلب الأخبار
try {
    // عدد الأخبار الإجمالي
    $count_sql = "SELECT COUNT(*) as total FROM news_articles a WHERE $where_clause";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_articles = $stmt->fetch()['total'];
    $total_pages = ceil($total_articles / $per_page);
    
    // جلب الأخبار للصفحة الحالية
    $sql = "SELECT a.*, c.name as category_name, c.color as category_color, u.name as author_name 
            FROM news_articles a 
            LEFT JOIN news_categories c ON a.category_id = c.id 
            LEFT JOIN users u ON a.author_id = u.id 
            WHERE $where_clause 
            ORDER BY a.is_featured DESC, a.published_at DESC 
            LIMIT $per_page OFFSET $offset";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $articles = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $articles = [];
    $total_articles = 0;
    $total_pages = 0;
}

// جلب التصنيفات
try {
    $stmt = $pdo->query("SELECT * FROM news_categories WHERE status = 'active' ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $categories = [];
}

// جلب الأخبار المميزة
try {
    $stmt = $pdo->query("
        SELECT a.*, c.name as category_name, c.color as category_color 
        FROM news_articles a 
        LEFT JOIN news_categories c ON a.category_id = c.id 
        WHERE a.status = 'published' AND a.is_featured = 1 
        ORDER BY a.published_at DESC 
        LIMIT 3
    ");
    $featured_articles = $stmt->fetchAll();
} catch (PDOException $e) {
    $featured_articles = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأخبار - نظام إدارة المؤسسات غير الربحية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            margin-bottom: 50px;
        }
        .article-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            margin-bottom: 30px;
            height: 100%;
        }
        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        .article-image {
            height: 200px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }
        .article-content {
            padding: 25px;
        }
        .category-badge {
            font-size: 0.8rem;
            padding: 4px 12px;
            border-radius: 20px;
            color: white;
            text-decoration: none;
        }
        .featured-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            font-weight: bold;
        }
        .search-box {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
        .pagination .page-link {
            border-radius: 10px;
            margin: 0 2px;
            border: none;
            color: #667eea;
        }
        .pagination .page-link:hover {
            background-color: #667eea;
            color: white;
        }
        .pagination .page-item.active .page-link {
            background-color: #667eea;
            border-color: #667eea;
        }
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand text-primary fw-bold" href="/">
                <i class="fas fa-hands-helping me-2"></i>
                نظام إدارة المؤسسات
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/news.php">الأخبار</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/login.php">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            تسجيل الدخول
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">
                        <i class="fas fa-newspaper me-3"></i>
                        أخبار المؤسسة
                    </h1>
                    <p class="lead mb-0">
                        تابع آخر الأخبار والمستجدات والفعاليات الخاصة بالمؤسسات غير الربحية
                    </p>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-globe fa-5x" style="opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </section>

    <div class="container">
        <!-- Search and Filter -->
        <div class="search-box">
            <form method="GET" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" name="search" 
                               value="<?= htmlspecialchars($search) ?>" 
                               placeholder="البحث في الأخبار...">
                    </div>
                </div>
                <div class="col-md-4">
                    <select class="form-select" name="category">
                        <option value="">جميع التصنيفات</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?= $category['id'] ?>" 
                                <?= ($category_id == $category['id']) ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- Featured Articles -->
        <?php if (!empty($featured_articles) && !$search && !$category_id): ?>
        <div class="mb-5">
            <h3 class="mb-4">
                <i class="fas fa-star text-warning me-2"></i>
                الأخبار المميزة
            </h3>
            <div class="row">
                <?php foreach ($featured_articles as $article): ?>
                <div class="col-md-4">
                    <div class="article-card">
                        <div class="article-image">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="article-content">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <?php if ($article['category_name']): ?>
                                <span class="category-badge" style="background-color: <?= $article['category_color'] ?>">
                                    <?= htmlspecialchars($article['category_name']) ?>
                                </span>
                                <?php endif; ?>
                                <span class="badge featured-badge">
                                    <i class="fas fa-star me-1"></i>مميز
                                </span>
                            </div>
                            <h5 class="mb-3">
                                <a href="/news-single.php?slug=<?= urlencode($article['slug']) ?>" 
                                   class="text-decoration-none text-dark">
                                    <?= htmlspecialchars($article['title']) ?>
                                </a>
                            </h5>
                            <p class="text-muted mb-3">
                                <?= htmlspecialchars(substr($article['excerpt'] ?? '', 0, 100)) ?>...
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?= date('Y-m-d', strtotime($article['published_at'])) ?>
                                </small>
                                <a href="/news-single.php?slug=<?= urlencode($article['slug']) ?>" 
                                   class="btn btn-outline-primary btn-sm">
                                    اقرأ المزيد
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Articles Grid -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3>
                        <?php if ($search): ?>
                            نتائج البحث عن "<?= htmlspecialchars($search) ?>"
                        <?php elseif ($category_id): ?>
                            <?php 
                            $current_category = array_filter($categories, fn($c) => $c['id'] == $category_id);
                            $current_category = reset($current_category);
                            echo htmlspecialchars($current_category['name'] ?? 'التصنيف');
                            ?>
                        <?php else: ?>
                            جميع الأخبار
                        <?php endif; ?>
                    </h3>
                    <span class="text-muted">
                        <?= number_format($total_articles) ?> خبر
                    </span>
                </div>
            </div>
        </div>

        <?php if (empty($articles)): ?>
        <div class="text-center py-5">
            <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أخبار</h5>
            <p class="text-muted">لم يتم العثور على أخبار تطابق معايير البحث</p>
        </div>
        <?php else: ?>
        
        <div class="row">
            <?php foreach ($articles as $article): ?>
            <div class="col-lg-4 col-md-6">
                <div class="article-card">
                    <div class="article-image">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <div class="article-content">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <?php if ($article['category_name']): ?>
                            <a href="/news.php?category=<?= $article['category_id'] ?>" 
                               class="category-badge" style="background-color: <?= $article['category_color'] ?>">
                                <?= htmlspecialchars($article['category_name']) ?>
                            </a>
                            <?php endif; ?>
                            <?php if ($article['is_featured']): ?>
                            <span class="badge featured-badge">
                                <i class="fas fa-star me-1"></i>مميز
                            </span>
                            <?php endif; ?>
                        </div>
                        <h5 class="mb-3">
                            <a href="/news-single.php?slug=<?= urlencode($article['slug']) ?>" 
                               class="text-decoration-none text-dark">
                                <?= htmlspecialchars($article['title']) ?>
                            </a>
                        </h5>
                        <p class="text-muted mb-3">
                            <?= htmlspecialchars(substr($article['excerpt'] ?? '', 0, 100)) ?>...
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?= date('Y-m-d', strtotime($article['published_at'])) ?>
                            </small>
                            <a href="/news-single.php?slug=<?= urlencode($article['slug']) ?>" 
                               class="btn btn-outline-primary btn-sm">
                                اقرأ المزيد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="تصفح الأخبار" class="mt-5">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&category=<?= $category_id ?>">
                        السابق
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <li class="page-item <?= ($i === $page) ? 'active' : '' ?>">
                    <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&category=<?= $category_id ?>">
                        <?= $i ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&category=<?= $category_id ?>">
                        التالي
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
        
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-4 mt-5">
        <div class="container">
            <p class="mb-0">
                &copy; 2024 نظام إدارة المؤسسات غير الربحية. جميع الحقوق محفوظة.
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
