<?php
session_start();
header('Content-Type: application/json');

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'count' => 0]);
    exit;
}

try {
    $cart_count = 0;
    
    // إنشاء session_id للزوار غير المسجلين
    if (!isset($_SESSION['cart_session_id'])) {
        $_SESSION['cart_session_id'] = session_id();
    }
    
    if (isset($_SESSION['user_id'])) {
        // للمستخدمين المسجلين
        $customer_stmt = $pdo->prepare("SELECT id FROM customers WHERE user_id = ?");
        $customer_stmt->execute([$_SESSION['user_id']]);
        $customer_data = $customer_stmt->fetch();
        
        if ($customer_data) {
            $count_stmt = $pdo->prepare("SELECT SUM(quantity) FROM shopping_cart WHERE customer_id = ?");
            $count_stmt->execute([$customer_data['id']]);
            $cart_count = $count_stmt->fetchColumn() ?: 0;
        }
    } else {
        // للزوار غير المسجلين
        $count_stmt = $pdo->prepare("SELECT SUM(quantity) FROM shopping_cart WHERE session_id = ?");
        $count_stmt->execute([$_SESSION['cart_session_id']]);
        $cart_count = $count_stmt->fetchColumn() ?: 0;
    }
    
    echo json_encode([
        'success' => true,
        'count' => (int)$cart_count
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'count' => 0]);
}
?>
