<?php
/**
 * ملف تحديث النظام
 * يستخدم لتطبيق التحديثات والترقيات على النظام
 */

// التحقق من الصلاحيات
if (!isset($_SESSION)) {
    session_start();
}

// التحقق من تسجيل الدخول كمدير
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'super_admin') {
    die('غير مصرح لك بالوصول إلى هذه الصفحة');
}

// إصدارات النظام
$versions = [
    '1.0.0' => 'الإصدار الأولي',
    '1.0.1' => 'إصلاحات أمنية وتحسينات',
    '1.1.0' => 'إضافة وحدة التقارير المتقدمة',
    '1.2.0' => 'إضافة واجهة API كاملة',
];

// الحصول على الإصدار الحالي
function getCurrentVersion() {
    $configFile = __DIR__ . '/config/app.php';
    if (file_exists($configFile)) {
        $config = include $configFile;
        return $config['version'] ?? '1.0.0';
    }
    return '1.0.0';
}

// تطبيق تحديث معين
function applyUpdate($version) {
    $updateFile = __DIR__ . "/database/updates/update_{$version}.sql";
    
    if (!file_exists($updateFile)) {
        return false;
    }
    
    try {
        // الاتصال بقاعدة البيانات
        $config = include __DIR__ . '/config/database.php';
        $dbConfig = $config['connections'][$config['default']];
        
        $pdo = new PDO(
            "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset=utf8mb4",
            $dbConfig['username'],
            $dbConfig['password'],
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        // قراءة وتنفيذ ملف التحديث
        $sql = file_get_contents($updateFile);
        $statements = explode(';', $sql);
        
        $pdo->beginTransaction();
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // تحديث رقم الإصدار في قاعدة البيانات
        $pdo->exec("UPDATE settings SET value = '{$version}' WHERE key_name = 'app_version'");
        
        $pdo->commit();
        
        return true;
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollback();
        }
        error_log("Update Error: " . $e->getMessage());
        return false;
    }
}

// إنشاء نسخة احتياطية
function createBackup() {
    $config = include __DIR__ . '/config/database.php';
    $dbConfig = $config['connections'][$config['default']];
    
    $backupDir = __DIR__ . '/storage/backups';
    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
    }
    
    $backupFile = $backupDir . '/backup_' . date('Y-m-d_H-i-s') . '.sql';
    
    $command = sprintf(
        'mysqldump -h%s -u%s -p%s %s > %s',
        $dbConfig['host'],
        $dbConfig['username'],
        $dbConfig['password'],
        $dbConfig['database'],
        $backupFile
    );
    
    exec($command, $output, $returnCode);
    
    return $returnCode === 0 ? $backupFile : false;
}

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'backup') {
        $backupFile = createBackup();
        if ($backupFile) {
            echo "<div class='alert alert-success'>تم إنشاء النسخة الاحتياطية: " . basename($backupFile) . "</div>";
        } else {
            echo "<div class='alert alert-danger'>فشل في إنشاء النسخة الاحتياطية</div>";
        }
    } elseif ($action === 'update') {
        $targetVersion = $_POST['version'] ?? '';
        
        if (applyUpdate($targetVersion)) {
            echo "<div class='alert alert-success'>تم تطبيق التحديث إلى الإصدار {$targetVersion} بنجاح</div>";
        } else {
            echo "<div class='alert alert-danger'>فشل في تطبيق التحديث</div>";
        }
    }
}

$currentVersion = getCurrentVersion();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث النظام - نظام إدارة المؤسسات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .update-container { max-width: 800px; margin: 50px auto; }
        .version-card { margin-bottom: 1rem; }
        .current-version { border: 2px solid #28a745; }
        .available-update { border: 2px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container update-container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">
                    <i class="fas fa-sync-alt me-2"></i>
                    تحديث النظام
                </h3>
            </div>
            <div class="card-body">
                <!-- معلومات الإصدار الحالي -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card current-version">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h5>الإصدار الحالي</h5>
                                <h3 class="text-success"><?= $currentVersion ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-download fa-3x text-primary mb-3"></i>
                                <h5>آخر إصدار متاح</h5>
                                <h3 class="text-primary"><?= array_key_last($versions) ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نسخة احتياطية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            إنشاء نسخة احتياطية
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">يُنصح بشدة بإنشاء نسخة احتياطية قبل تطبيق أي تحديثات</p>
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="backup">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-database me-2"></i>
                                إنشاء نسخة احتياطية
                            </button>
                        </form>
                    </div>
                </div>

                <!-- قائمة الإصدارات -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            الإصدارات المتاحة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($versions as $version => $description): ?>
                        <div class="card version-card <?= $version === $currentVersion ? 'current-version' : '' ?>">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h6 class="mb-1">
                                            الإصدار <?= $version ?>
                                            <?php if ($version === $currentVersion): ?>
                                            <span class="badge bg-success">مثبت حالياً</span>
                                            <?php elseif (version_compare($version, $currentVersion, '>')): ?>
                                            <span class="badge bg-warning">متاح للتحديث</span>
                                            <?php endif; ?>
                                        </h6>
                                        <p class="text-muted mb-0"><?= $description ?></p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <?php if (version_compare($version, $currentVersion, '>')): ?>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="action" value="update">
                                            <input type="hidden" name="version" value="<?= $version ?>">
                                            <button type="submit" class="btn btn-primary btn-sm" 
                                                    onclick="return confirm('هل أنت متأكد من تطبيق هذا التحديث؟')">
                                                <i class="fas fa-download me-1"></i>
                                                تحديث
                                            </button>
                                        </form>
                                        <?php elseif ($version === $currentVersion): ?>
                                        <span class="text-success">
                                            <i class="fas fa-check-circle"></i>
                                            مثبت
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- تحذيرات مهمة -->
                <div class="alert alert-warning mt-4">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذيرات مهمة:</h6>
                    <ul class="mb-0">
                        <li>قم بإنشاء نسخة احتياطية قبل أي تحديث</li>
                        <li>تأكد من عدم وجود مستخدمين نشطين أثناء التحديث</li>
                        <li>قم بتجربة التحديث في بيئة تجريبية أولاً</li>
                        <li>احتفظ بنسخة من الملفات الأصلية</li>
                    </ul>
                </div>

                <!-- روابط مفيدة -->
                <div class="text-center mt-4">
                    <a href="/dashboard" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة إلى لوحة التحكم
                    </a>
                    <a href="/settings" class="btn btn-outline-primary">
                        <i class="fas fa-cog me-1"></i>
                        الإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
