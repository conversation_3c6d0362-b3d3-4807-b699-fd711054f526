<?php
/**
 * ملف التثبيت المبسط لنظام إدارة المؤسسات غير الربحية
 */

// تحديد مسار الجذر
define('ROOT_PATH', dirname(__DIR__));

// بدء الجلسة
session_start();

// التحقق من متطلبات النظام
function checkRequirements() {
    $requirements = [
        'PHP Version >= 8.0' => version_compare(PHP_VERSION, '8.0.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'JSON Extension' => extension_loaded('json'),
        'OpenSSL Extension' => extension_loaded('openssl'),
        'Mbstring Extension' => extension_loaded('mbstring'),
        'Fileinfo Extension' => extension_loaded('fileinfo'),
    ];

    $allMet = true;
    echo "<h2>فحص متطلبات النظام:</h2>\n";
    echo "<ul>\n";

    foreach ($requirements as $requirement => $met) {
        $status = $met ? '✅ متوفر' : '❌ غير متوفر';
        echo "<li>{$requirement}: {$status}</li>\n";
        if (!$met) $allMet = false;
    }

    echo "</ul>\n";

    if (!$allMet) {
        echo "<p style='color: red;'>يرجى تثبيت المتطلبات المفقودة قبل المتابعة.</p>\n";
        exit;
    }

    echo "<p style='color: green;'>جميع المتطلبات متوفرة!</p>\n";
}

// إنشاء قاعدة البيانات والجداول
function setupDatabase($config) {
    try {
        echo "<p>🔄 جاري اختبار الاتصال بـ MySQL...</p>\n";
        flush();

        $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
        echo "<p>📡 محاولة الاتصال بـ: {$dsn}</p>\n";
        flush();

        $pdo = new PDO(
            $dsn,
            $config['username'],
            $config['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 10
            ]
        );

        echo "<p>✅ تم الاتصال بـ MySQL بنجاح!</p>\n";

        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p>✅ تم إنشاء قاعدة البيانات: {$config['database']}</p>\n";

        // الاتصال بقاعدة البيانات المحددة
        $pdo = new PDO(
            "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4",
            $config['username'],
            $config['password'],
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );

        echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح!</p>\n";

        // إنشاء الجداول الأساسية
        createTables($pdo);
        
        // إدراج البيانات الأولية
        insertInitialData($pdo);

        return true;

    } catch (PDOException $e) {
        $errorCode = $e->getCode();
        $errorMessage = $e->getMessage();
        
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<h4>❌ خطأ في الاتصال بقاعدة البيانات</h4>";
        
        if (strpos($errorMessage, 'Connection refused') !== false || $errorCode == 2002) {
            echo "<p><strong>المشكلة:</strong> لا يمكن الاتصال بخادم MySQL</p>";
            echo "<p><strong>الحلول المقترحة:</strong></p>";
            echo "<ul>";
            echo "<li>تأكد من تشغيل خادم MySQL في WAMP</li>";
            echo "<li>تحقق من أن المنفذ 3306 متاح</li>";
            echo "<li>جرب استخدام 127.0.0.1 بدلاً من localhost</li>";
            echo "</ul>";
        } elseif (strpos($errorMessage, 'Access denied') !== false || $errorCode == 1045) {
            echo "<p><strong>المشكلة:</strong> خطأ في اسم المستخدم أو كلمة المرور</p>";
            echo "<p><strong>الحلول المقترحة:</strong></p>";
            echo "<ul>";
            echo "<li>تأكد من صحة اسم المستخدم (عادة 'root' في WAMP)</li>";
            echo "<li>تأكد من كلمة المرور (عادة فارغة في WAMP)</li>";
            echo "</ul>";
        } else {
            echo "<p><strong>رسالة الخطأ:</strong> {$errorMessage}</p>";
            echo "<p><strong>رمز الخطأ:</strong> {$errorCode}</p>";
        }
        
        echo "</div>";
        
        return false;
    }
}

// إنشاء الجداول
function createTables($pdo) {
    $tables = [
        // جدول المستخدمين
        "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'manager', 'employee', 'volunteer') DEFAULT 'employee',
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            organization_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // جدول المنظمات
        "CREATE TABLE IF NOT EXISTS organizations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            type ENUM('charity', 'ngo', 'foundation', 'association') DEFAULT 'charity',
            registration_number VARCHAR(100),
            email VARCHAR(255),
            phone VARCHAR(50),
            address TEXT,
            website VARCHAR(255),
            status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // جدول المشاريع
        "CREATE TABLE IF NOT EXISTS projects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            organization_id INT NOT NULL,
            manager_id INT,
            budget DECIMAL(15,2) DEFAULT 0,
            start_date DATE,
            end_date DATE,
            status ENUM('planning', 'active', 'completed', 'cancelled') DEFAULT 'planning',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (organization_id) REFERENCES organizations(id),
            FOREIGN KEY (manager_id) REFERENCES users(id)
        )",
        
        // جدول التبرعات
        "CREATE TABLE IF NOT EXISTS donations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            donor_name VARCHAR(255) NOT NULL,
            donor_email VARCHAR(255),
            donor_phone VARCHAR(50),
            amount DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'SAR',
            organization_id INT NOT NULL,
            project_id INT NULL,
            donation_type ENUM('one_time', 'monthly', 'yearly') DEFAULT 'one_time',
            payment_method ENUM('cash', 'bank_transfer', 'credit_card', 'online') DEFAULT 'cash',
            status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (organization_id) REFERENCES organizations(id),
            FOREIGN KEY (project_id) REFERENCES projects(id)
        )"
    ];
    
    foreach ($tables as $sql) {
        $pdo->exec($sql);
    }
    
    echo "<p>✅ تم إنشاء الجداول الأساسية</p>\n";
}

// إدراج البيانات الأولية
function insertInitialData($pdo) {
    // إنشاء منظمة افتراضية
    $pdo->exec("INSERT IGNORE INTO organizations (id, name, description, type) VALUES 
        (1, 'المنظمة الافتراضية', 'منظمة تجريبية للنظام', 'charity')");
    
    // إنشاء مستخدم مدير
    $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
    $pdo->exec("INSERT IGNORE INTO users (id, name, email, password, role, organization_id) VALUES 
        (1, 'المدير العام', '<EMAIL>', '$hashedPassword', 'admin', 1)");
    
    echo "<p>✅ تم إدراج البيانات الأولية</p>\n";
}

// إنشاء ملف .env
function createEnvFile($config) {
    $envContent = "APP_NAME=\"نظام إدارة المؤسسات غير الربحية\"
APP_ENV=production
APP_DEBUG=false
APP_URL={$config['app_url']}
APP_KEY=" . base64_encode(random_bytes(32)) . "
APP_TIMEZONE=Asia/Riyadh
APP_LOCALE=ar

DB_CONNECTION=mysql
DB_HOST={$config['host']}
DB_PORT={$config['port']}
DB_DATABASE={$config['database']}
DB_USERNAME={$config['username']}
DB_PASSWORD={$config['password']}

SESSION_DRIVER=file
SESSION_LIFETIME=120

UPLOAD_MAX_SIZE=10485760

JWT_SECRET=" . bin2hex(random_bytes(32)) . "
JWT_TTL=3600

PASSWORD_MIN_LENGTH=8
LOGIN_ATTEMPTS=5
LOGIN_LOCKOUT_DURATION=900";

    if (file_put_contents(ROOT_PATH . '/.env', $envContent)) {
        echo "<p>✅ تم إنشاء ملف .env</p>\n";
        return true;
    } else {
        echo "<p style='color: red;'>فشل في إنشاء ملف .env</p>\n";
        return false;
    }
}

// إعداد المجلدات
function setupDirectories() {
    $directories = [
        ROOT_PATH . '/storage/logs',
        ROOT_PATH . '/storage/cache',
        ROOT_PATH . '/storage/sessions',
        ROOT_PATH . '/public/uploads'
    ];

    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }

    echo "<p>✅ تم إعداد المجلدات</p>\n";
}

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $config = [
        'host' => $_POST['db_host'] ?? 'localhost',
        'port' => $_POST['db_port'] ?? '3306',
        'database' => $_POST['db_database'] ?? 'nonprofit_cms',
        'username' => $_POST['db_username'] ?? 'root',
        'password' => $_POST['db_password'] ?? '',
        'app_url' => $_POST['app_url'] ?? 'http://localhost:8000'
    ];

    echo "<!DOCTYPE html><html lang='ar' dir='rtl'><head><meta charset='UTF-8'><title>تثبيت النظام</title></head><body style='font-family: Arial; padding: 20px;'>";
    echo "<h1>جاري تثبيت النظام...</h1>\n";

    checkRequirements();

    if (setupDatabase($config)) {
        createEnvFile($config);
        setupDirectories();

        echo "<h2 style='color: green;'>🎉 تم تثبيت النظام بنجاح!</h2>\n";
        echo "<p><strong>بيانات تسجيل الدخول:</strong></p>\n";
        echo "<ul><li>البريد الإلكتروني: <EMAIL></li><li>كلمة المرور: password</li></ul>\n";
        echo "<p><a href='/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة للصفحة الرئيسية</a></p>\n";
        echo "<p style='color: orange;'><strong>تنبيه:</strong> يرجى حذف ملف setup.php بعد التثبيت.</p>\n";
    }
    
    echo "</body></html>";
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة المؤسسات غير الربحية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .install-container { max-width: 600px; margin: 50px auto; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class="container install-container">
        <div class="card">
            <div class="card-header text-center bg-primary text-white">
                <h3>🏢 تثبيت نظام إدارة المؤسسات غير الربحية</h3>
                <p class="mb-0">مرحباً بك في معالج التثبيت</p>
            </div>
            <div class="card-body p-4">
                <?php checkRequirements(); ?>

                <div class="alert alert-warning mt-4">
                    <h6>⚠️ تأكد من الآتي قبل التثبيت:</h6>
                    <ul class="mb-0">
                        <li>تشغيل WAMP وظهور الأيقونة باللون الأخضر</li>
                        <li>تشغيل خدمة MySQL من WAMP</li>
                    </ul>
                </div>

                <form method="POST" class="mt-4">
                    <h5>إعدادات قاعدة البيانات:</h5>

                    <div class="mb-3">
                        <label class="form-label">خادم قاعدة البيانات:</label>
                        <select name="db_host" class="form-control" required>
                            <option value="localhost">localhost</option>
                            <option value="127.0.0.1">127.0.0.1</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">منفذ قاعدة البيانات:</label>
                        <input type="text" name="db_port" class="form-control" value="3306" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم قاعدة البيانات:</label>
                        <input type="text" name="db_database" class="form-control" value="nonprofit_cms" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم:</label>
                        <input type="text" name="db_username" class="form-control" value="root" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">كلمة المرور:</label>
                        <input type="password" name="db_password" class="form-control" placeholder="اتركها فارغة في WAMP">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">رابط الموقع:</label>
                        <input type="url" name="app_url" class="form-control" value="http://localhost:8000" required>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            🚀 بدء التثبيت
                        </button>
                        <a href="/check.php" class="btn btn-outline-info">
                            🔍 فحص النظام
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
