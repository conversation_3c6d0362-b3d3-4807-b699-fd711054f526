<?php
session_start();

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];

    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: /login.php');
    exit;
}

$success = '';
$error = '';

// جلب معلومات الموظف الحالي
$current_employee = null;
$stmt = $pdo->prepare("SELECT * FROM employees WHERE user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$current_employee = $stmt->fetch();

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'submit_leave_request') {
        try {
            // التحقق من البيانات المطلوبة
            $required_fields = ['leave_type', 'start_date', 'end_date', 'reason'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("جميع الحقول المطلوبة يجب ملؤها");
                }
            }

            $start_date = new DateTime($_POST['start_date']);
            $end_date = new DateTime($_POST['end_date']);

            if ($start_date > $end_date) {
                throw new Exception("تاريخ البداية يجب أن يكون قبل تاريخ النهاية");
            }

            // حساب عدد الأيام
            $days_count = $start_date->diff($end_date)->days + 1;

            // التحقق من رصيد الإجازات
            if ($_POST['leave_type'] === 'vacation' && $current_employee['vacation_balance'] < $days_count) {
                throw new Exception("رصيد الإجازات غير كافي. الرصيد المتاح: " . $current_employee['vacation_balance'] . " يوم");
            }

            if ($_POST['leave_type'] === 'sick' && $current_employee['sick_leave_balance'] < $days_count) {
                throw new Exception("رصيد الإجازات المرضية غير كافي. الرصيد المتاح: " . $current_employee['sick_leave_balance'] . " يوم");
            }

            // إدراج طلب الإجازة
            $stmt = $pdo->prepare("
                INSERT INTO leave_requests (employee_id, leave_type, start_date, end_date, days_count, reason)
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $current_employee['id'],
                $_POST['leave_type'],
                $_POST['start_date'],
                $_POST['end_date'],
                $days_count,
                $_POST['reason']
            ]);

            // إضافة إشعار للمدير
            if ($current_employee['manager_id']) {
                $notification_stmt = $pdo->prepare("
                    INSERT INTO notifications (employee_id, type, title, message, related_id)
                    VALUES (?, 'leave_request', ?, ?, ?)
                ");

                $notification_stmt->execute([
                    $current_employee['manager_id'],
                    'طلب إجازة جديد',
                    'تم تقديم طلب إجازة جديد من ' . $_SESSION['user_name'],
                    $pdo->lastInsertId()
                ]);
            }

            $success = "تم تقديم طلب الإجازة بنجاح";

        } catch (Exception $e) {
            $error = "خطأ في تقديم طلب الإجازة: " . $e->getMessage();
        }
    }

    elseif ($action === 'approve_leave' || $action === 'reject_leave') {
        try {
            $leave_id = $_POST['leave_id'];
            $status = $action === 'approve_leave' ? 'approved' : 'rejected';

            $pdo->beginTransaction();

            // تحديث حالة الطلب
            $stmt = $pdo->prepare("
                UPDATE leave_requests
                SET status = ?, approved_by = ?, approved_at = NOW(), rejection_reason = ?
                WHERE id = ?
            ");

            $stmt->execute([
                $status,
                $current_employee['id'],
                $_POST['rejection_reason'] ?? null,
                $leave_id
            ]);

            // إذا تم الموافقة، خصم من رصيد الإجازات
            if ($status === 'approved') {
                $leave_stmt = $pdo->prepare("SELECT * FROM leave_requests WHERE id = ?");
                $leave_stmt->execute([$leave_id]);
                $leave_info = $leave_stmt->fetch();

                if ($leave_info['leave_type'] === 'vacation') {
                    $pdo->prepare("
                        UPDATE employees
                        SET vacation_balance = vacation_balance - ?
                        WHERE id = ?
                    ")->execute([$leave_info['days_count'], $leave_info['employee_id']]);
                } elseif ($leave_info['leave_type'] === 'sick') {
                    $pdo->prepare("
                        UPDATE employees
                        SET sick_leave_balance = sick_leave_balance - ?
                        WHERE id = ?
                    ")->execute([$leave_info['days_count'], $leave_info['employee_id']]);
                }
            }

            $pdo->commit();
            $success = $status === 'approved' ? "تم الموافقة على الطلب" : "تم رفض الطلب";

        } catch (Exception $e) {
            $pdo->rollBack();
            $error = "خطأ في معالجة الطلب: " . $e->getMessage();
        }
    }
}

// جلب طلبات الإجازات
$filter = $_GET['filter'] ?? 'my_requests';
$status_filter = $_GET['status'] ?? '';

$where_conditions = [];
$params = [];

if ($filter === 'my_requests') {
    $where_conditions[] = "lr.employee_id = ?";
    $params[] = $current_employee['id'];
} elseif ($filter === 'team_requests' && $current_employee) {
    // طلبات الفريق للمدراء
    $where_conditions[] = "e.manager_id = ?";
    $params[] = $current_employee['id'];
}

if ($status_filter) {
    $where_conditions[] = "lr.status = ?";
    $params[] = $status_filter;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

$leave_requests_query = "
    SELECT lr.*, e.employee_id, u.name as employee_name,
           d.name as department_name, p.title as position_title,
           au.name as approved_by_name
    FROM leave_requests lr
    JOIN employees e ON lr.employee_id = e.id
    JOIN users u ON e.user_id = u.id
    JOIN departments d ON e.department_id = d.id
    JOIN positions p ON e.position_id = p.id
    LEFT JOIN employees ae ON lr.approved_by = ae.id
    LEFT JOIN users au ON ae.user_id = au.id
    {$where_clause}
    ORDER BY lr.created_at DESC
";

$stmt = $pdo->prepare($leave_requests_query);
$stmt->execute($params);
$leave_requests = $stmt->fetchAll();

// إحصائيات
$stats = [
    'pending_requests' => $pdo->query("SELECT COUNT(*) FROM leave_requests WHERE status = 'pending'")->fetchColumn(),
    'approved_requests' => $pdo->query("SELECT COUNT(*) FROM leave_requests WHERE status = 'approved'")->fetchColumn(),
    'my_vacation_balance' => $current_employee['vacation_balance'] ?? 0,
    'my_sick_balance' => $current_employee['sick_leave_balance'] ?? 0
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإجازات - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #17a2b8, #138496); }
        .stats-card { background: linear-gradient(45deg, #17a2b8, #138496); color: white; border-radius: 15px; }
        .leave-card { border-radius: 10px; transition: transform 0.2s; }
        .leave-card:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .status-pending { color: #ffc107; }
        .status-approved { color: #28a745; }
        .status-rejected { color: #dc3545; }
        .balance-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        .balance-danger { background-color: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-calendar-alt me-2"></i>إدارة الإجازات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="/employees.php">
                    <i class="fas fa-users me-1"></i>الموظفين
                </a>
                <a class="nav-link" href="/payroll.php">
                    <i class="fas fa-money-bill me-1"></i>الرواتب
                </a>
                <a class="nav-link" href="/messages.php">
                    <i class="fas fa-envelope me-1"></i>الرسائل
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- تنبيه رصيد الإجازات -->
        <?php if ($current_employee): ?>
        <?php if ($current_employee['vacation_balance'] <= 5): ?>
        <div class="alert <?= $current_employee['vacation_balance'] <= 2 ? 'balance-danger' : 'balance-warning' ?> alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            تنبيه: رصيد إجازاتك السنوية منخفض (<?= $current_employee['vacation_balance'] ?> أيام متبقية)
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4><?= $stats['pending_requests'] ?></h4>
                    <small>طلبات معلقة</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4><?= $stats['approved_requests'] ?></h4>
                    <small>طلبات موافق عليها</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-umbrella-beach fa-2x mb-2"></i>
                    <h4><?= $stats['my_vacation_balance'] ?></h4>
                    <small>رصيد إجازاتي السنوية</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-user-md fa-2x mb-2"></i>
                    <h4><?= $stats['my_sick_balance'] ?></h4>
                    <small>رصيد إجازاتي المرضية</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- نموذج طلب إجازة جديد -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-plus-circle me-2"></i>طلب إجازة جديد</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="submit_leave_request">

                            <div class="mb-3">
                                <label class="form-label">نوع الإجازة *</label>
                                <select name="leave_type" class="form-select" required>
                                    <option value="">اختر نوع الإجازة</option>
                                    <option value="vacation">إجازة سنوية</option>
                                    <option value="sick">إجازة مرضية</option>
                                    <option value="emergency">إجازة طارئة</option>
                                    <option value="maternity">إجازة أمومة</option>
                                    <option value="paternity">إجازة أبوة</option>
                                    <option value="unpaid">إجازة بدون راتب</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">تاريخ البداية *</label>
                                <input type="date" name="start_date" class="form-control" required min="<?= date('Y-m-d') ?>">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">تاريخ النهاية *</label>
                                <input type="date" name="end_date" class="form-control" required min="<?= date('Y-m-d') ?>">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">سبب الإجازة *</label>
                                <textarea name="reason" class="form-control" rows="3" required placeholder="اذكر سبب طلب الإجازة..."></textarea>
                            </div>

                            <div class="mb-3">
                                <div class="alert alert-info small">
                                    <strong>رصيدك الحالي:</strong><br>
                                    إجازات سنوية: <?= $current_employee['vacation_balance'] ?? 0 ?> يوم<br>
                                    إجازات مرضية: <?= $current_employee['sick_leave_balance'] ?? 0 ?> يوم
                                </div>
                            </div>

                            <button type="submit" class="btn btn-info w-100">
                                <i class="fas fa-paper-plane me-2"></i>تقديم الطلب
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة طلبات الإجازات -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>طلبات الإجازات</h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" onchange="filterRequests()" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>معلقة</option>
                                <option value="approved" <?= $status_filter === 'approved' ? 'selected' : '' ?>>موافق عليها</option>
                                <option value="rejected" <?= $status_filter === 'rejected' ? 'selected' : '' ?>>مرفوضة</option>
                            </select>
                            <div class="btn-group">
                                <a href="?filter=my_requests" class="btn btn-sm <?= $filter === 'my_requests' ? 'btn-info' : 'btn-outline-info' ?>">
                                    طلباتي
                                </a>
                                <?php if ($current_employee): ?>
                                <a href="?filter=team_requests" class="btn btn-sm <?= $filter === 'team_requests' ? 'btn-info' : 'btn-outline-info' ?>">
                                    طلبات الفريق
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($leave_requests)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد طلبات إجازات</p>
                        </div>
                        <?php else: ?>
                        <div class="row">
                            <?php foreach ($leave_requests as $request): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card leave-card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0"><?= htmlspecialchars($request['employee_name']) ?></h6>
                                            <span class="badge status-<?= $request['status'] ?>">
                                                <?php
                                                $status_labels = [
                                                    'pending' => 'معلقة',
                                                    'approved' => 'موافق عليها',
                                                    'rejected' => 'مرفوضة',
                                                    'cancelled' => 'ملغاة'
                                                ];
                                                echo $status_labels[$request['status']] ?? $request['status'];
                                                ?>
                                            </span>
                                        </div>

                                        <div class="small text-muted mb-2">
                                            <div><i class="fas fa-calendar me-1"></i>
                                                <?= date('Y/m/d', strtotime($request['start_date'])) ?> -
                                                <?= date('Y/m/d', strtotime($request['end_date'])) ?>
                                            </div>
                                            <div><i class="fas fa-clock me-1"></i><?= $request['days_count'] ?> أيام</div>
                                            <div><i class="fas fa-tag me-1"></i>
                                                <?php
                                                $leave_types = [
                                                    'vacation' => 'إجازة سنوية',
                                                    'sick' => 'إجازة مرضية',
                                                    'emergency' => 'إجازة طارئة',
                                                    'maternity' => 'إجازة أمومة',
                                                    'paternity' => 'إجازة أبوة',
                                                    'unpaid' => 'إجازة بدون راتب'
                                                ];
                                                echo $leave_types[$request['leave_type']] ?? $request['leave_type'];
                                                ?>
                                            </div>
                                            <div><i class="fas fa-comment me-1"></i><?= htmlspecialchars($request['reason']) ?></div>
                                            <?php if ($request['approved_by_name']): ?>
                                            <div><i class="fas fa-user-check me-1"></i>بواسطة: <?= htmlspecialchars($request['approved_by_name']) ?></div>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ($request['status'] === 'pending' && $filter === 'team_requests'): ?>
                                        <div class="d-flex gap-2 mt-2">
                                            <button class="btn btn-success btn-sm" onclick="approveLeave(<?= $request['id'] ?>)">
                                                <i class="fas fa-check me-1"></i>موافقة
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="rejectLeave(<?= $request['id'] ?>)">
                                                <i class="fas fa-times me-1"></i>رفض
                                            </button>
                                        </div>
                                        <?php endif; ?>

                                        <?php if ($request['rejection_reason']): ?>
                                        <div class="alert alert-danger small mt-2 mb-0">
                                            <strong>سبب الرفض:</strong> <?= htmlspecialchars($request['rejection_reason']) ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لرفض الطلب -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">رفض طلب الإجازة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="reject_leave">
                        <input type="hidden" name="leave_id" id="rejectLeaveId">

                        <div class="mb-3">
                            <label class="form-label">سبب الرفض *</label>
                            <textarea name="rejection_reason" class="form-control" rows="3" required placeholder="اذكر سبب رفض الطلب..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">رفض الطلب</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function filterRequests() {
            const status = document.getElementById('statusFilter').value;
            const currentUrl = new URL(window.location);
            if (status) {
                currentUrl.searchParams.set('status', status);
            } else {
                currentUrl.searchParams.delete('status');
            }
            window.location.href = currentUrl.toString();
        }

        function approveLeave(id) {
            if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="approve_leave">
                    <input type="hidden" name="leave_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function rejectLeave(id) {
            document.getElementById('rejectLeaveId').value = id;
            new bootstrap.Modal(document.getElementById('rejectModal')).show();
        }

        // حساب عدد الأيام تلقائياً
        document.addEventListener('DOMContentLoaded', function() {
            const startDate = document.querySelector('input[name="start_date"]');
            const endDate = document.querySelector('input[name="end_date"]');

            function calculateDays() {
                if (startDate.value && endDate.value) {
                    const start = new Date(startDate.value);
                    const end = new Date(endDate.value);
                    const diffTime = Math.abs(end - start);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

                    // عرض عدد الأيام
                    let daysInfo = document.getElementById('daysInfo');
                    if (!daysInfo) {
                        daysInfo = document.createElement('small');
                        daysInfo.id = 'daysInfo';
                        daysInfo.className = 'text-info';
                        endDate.parentNode.appendChild(daysInfo);
                    }
                    daysInfo.textContent = `عدد الأيام: ${diffDays}`;
                }
            }

            startDate.addEventListener('change', calculateDays);
            endDate.addEventListener('change', calculateDays);
        });
    </script>
</body>
</html>