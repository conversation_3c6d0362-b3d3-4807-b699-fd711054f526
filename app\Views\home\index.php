<?php
$title = 'نظام إدارة المؤسسات غير الربحية';
ob_start();
?>

<div class="home-container">
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            نظام إدارة المؤسسات
                            <span class="text-primary">غير الربحية</span>
                        </h1>
                        <p class="hero-subtitle">
                            حلول تقنية متكاملة لإدارة وتطوير مؤسسات القطاع غير الربحي
                            مع أدوات متقدمة للإدارة والمتابعة والتقييم
                        </p>
                        <div class="hero-buttons">
                            <a href="/auth/login" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </a>
                            <a href="#features" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-info-circle me-2"></i>
                                اعرف المزيد
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <i class="fas fa-hands-helping hero-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">المميزات الرئيسية</h2>
                    <p class="section-subtitle">نظام شامل ومتكامل لجميع احتياجات المؤسسات غير الربحية</p>
                </div>
            </div>
            
            <div class="row g-4">
                <!-- إدارة المنظمات -->
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h4>إدارة المنظمات</h4>
                        <p>تسجيل وإدارة بيانات المنظمات والجمعيات مع متابعة التراخيص والوثائق الرسمية</p>
                    </div>
                </div>

                <!-- إدارة المستخدمين -->
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4>إدارة المستخدمين</h4>
                        <p>نظام أدوار وصلاحيات متقدم مع إدارة شاملة للمستخدمين والموظفين</p>
                    </div>
                </div>

                <!-- الموارد البشرية -->
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <h4>الموارد البشرية</h4>
                        <p>إدارة الموظفين والمتطوعين مع نظام الرواتب ومتابعة الأداء</p>
                    </div>
                </div>

                <!-- إدارة المشاريع -->
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <h4>إدارة المشاريع</h4>
                        <p>تخطيط وتنفيذ ومتابعة المشاريع والبرامج مع إدارة الميزانيات</p>
                    </div>
                </div>

                <!-- الشؤون المالية -->
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4>الشؤون المالية</h4>
                        <p>إدارة التبرعات والحسابات المالية مع نظام القيود المحاسبية</p>
                    </div>
                </div>

                <!-- التقارير -->
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4>التقارير والإحصائيات</h4>
                        <p>تقارير شاملة ولوحات بيانات تفاعلية لمتابعة الأداء والإنجازات</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section py-5 bg-primary text-white">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-3 col-6 mb-4">
                    <div class="stat-item">
                        <h3 class="stat-number">500+</h3>
                        <p class="stat-label">منظمة مسجلة</p>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="stat-item">
                        <h3 class="stat-number">10,000+</h3>
                        <p class="stat-label">مستخدم نشط</p>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="stat-item">
                        <h3 class="stat-number">2,500+</h3>
                        <p class="stat-label">مشروع منجز</p>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="stat-item">
                        <h3 class="stat-number">99.9%</h3>
                        <p class="stat-label">وقت التشغيل</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">تواصل معنا</h2>
                    <p class="section-subtitle">نحن هنا لمساعدتك في رحلة التحول الرقمي</p>
                </div>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="contact-info">
                        <div class="row g-4">
                            <div class="col-md-4 text-center">
                                <div class="contact-item">
                                    <i class="fas fa-envelope fa-2x text-primary mb-3"></i>
                                    <h5>البريد الإلكتروني</h5>
                                    <p><EMAIL></p>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="contact-item">
                                    <i class="fas fa-phone fa-2x text-primary mb-3"></i>
                                    <h5>الهاتف</h5>
                                    <p>+966 11 123 4567</p>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt fa-2x text-primary mb-3"></i>
                                    <h5>العنوان</h5>
                                    <p>الرياض، المملكة العربية السعودية</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer-section bg-dark text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 نظام إدارة المؤسسات غير الربحية. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-github"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>

<style>
.home-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-icon {
    font-size: 15rem;
    opacity: 0.1;
    text-align: center;
    display: block;
}

.features-section {
    background: white;
    color: #333;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    height: 100%;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

.stats-section {
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

.contact-section {
    background: #f8f9fa;
    color: #333;
}

.contact-item i {
    margin-bottom: 1rem;
}

.contact-item h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.footer-section {
    background: #2c3e50 !important;
}

.social-links a {
    font-size: 1.25rem;
    transition: opacity 0.3s ease;
}

.social-links a:hover {
    opacity: 0.7;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-icon {
        font-size: 8rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}
</style>

<script>
// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Counter animation
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');
    
    counters.forEach(counter => {
        const target = parseInt(counter.textContent.replace(/[^0-9]/g, ''));
        const increment = target / 100;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = counter.textContent.replace(/[0-9,]+/, target.toLocaleString());
                clearInterval(timer);
            } else {
                counter.textContent = counter.textContent.replace(/[0-9,]+/, Math.floor(current).toLocaleString());
            }
        }, 20);
    });
}

// Trigger counter animation when stats section is visible
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            animateCounters();
            observer.unobserve(entry.target);
        }
    });
});

const statsSection = document.querySelector('.stats-section');
if (statsSection) {
    observer.observe(statsSection);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>
