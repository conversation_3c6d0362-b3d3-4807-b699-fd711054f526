<?php

namespace Core\Database;

abstract class Model
{
    protected $connection;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $guarded = ['id', 'created_at', 'updated_at'];
    protected $timestamps = true;
    protected $dateFormat = 'Y-m-d H:i:s';

    public function __construct()
    {
        $this->connection = Connection::getInstance();
    }

    public function find(int $id): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id LIMIT 1";
        return $this->connection->fetch($sql, ['id' => $id]);
    }

    public function findBy(string $column, $value): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} = :value LIMIT 1";
        return $this->connection->fetch($sql, ['value' => $value]);
    }

    public function all(): array
    {
        $sql = "SELECT * FROM {$this->table}";
        return $this->connection->fetchAll($sql);
    }

    public function where(array $conditions): array
    {
        $whereClause = implode(' AND ', array_map(fn($key) => "{$key} = :{$key}", array_keys($conditions)));
        $sql = "SELECT * FROM {$this->table} WHERE {$whereClause}";
        return $this->connection->fetchAll($sql, $conditions);
    }

    public function create(array $data): int
    {
        $data = $this->filterFillable($data);
        
        if ($this->timestamps) {
            $data['created_at'] = date($this->dateFormat);
            $data['updated_at'] = date($this->dateFormat);
        }

        return $this->connection->insert($this->table, $data);
    }

    public function update(int $id, array $data): int
    {
        $data = $this->filterFillable($data);
        
        if ($this->timestamps) {
            $data['updated_at'] = date($this->dateFormat);
        }

        return $this->connection->update($this->table, $data, [$this->primaryKey => $id]);
    }

    public function delete(int $id): int
    {
        return $this->connection->delete($this->table, [$this->primaryKey => $id]);
    }

    public function paginate(int $page = 1, int $perPage = 15): array
    {
        $offset = ($page - 1) * $perPage;
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM {$this->table}";
        $totalResult = $this->connection->fetch($countSql);
        $total = $totalResult['total'];
        
        // Get paginated results
        $sql = "SELECT * FROM {$this->table} LIMIT {$perPage} OFFSET {$offset}";
        $data = $this->connection->fetchAll($sql);
        
        return [
            'data' => $data,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $total,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total),
        ];
    }

    public function search(string $column, string $term): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} LIKE :term";
        return $this->connection->fetchAll($sql, ['term' => "%{$term}%"]);
    }

    public function orderBy(string $column, string $direction = 'ASC'): array
    {
        $direction = strtoupper($direction) === 'DESC' ? 'DESC' : 'ASC';
        $sql = "SELECT * FROM {$this->table} ORDER BY {$column} {$direction}";
        return $this->connection->fetchAll($sql);
    }

    public function exists(array $conditions): bool
    {
        $whereClause = implode(' AND ', array_map(fn($key) => "{$key} = :{$key}", array_keys($conditions)));
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE {$whereClause}";
        $result = $this->connection->fetch($sql, $conditions);
        return $result['count'] > 0;
    }

    public function count(): int
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        $result = $this->connection->fetch($sql);
        return $result['count'];
    }

    protected function filterFillable(array $data): array
    {
        if (empty($this->fillable)) {
            return array_diff_key($data, array_flip($this->guarded));
        }

        return array_intersect_key($data, array_flip($this->fillable));
    }

    public function beginTransaction(): bool
    {
        return $this->connection->beginTransaction();
    }

    public function commit(): bool
    {
        return $this->connection->commit();
    }

    public function rollback(): bool
    {
        return $this->connection->rollback();
    }

    public function raw(string $sql, array $params = []): array
    {
        return $this->connection->fetchAll($sql, $params);
    }

    public function getTable(): string
    {
        return $this->table;
    }

    public function getPrimaryKey(): string
    {
        return $this->primaryKey;
    }
}
