<?php

namespace App\Middleware;

class CsrfMiddleware
{
    public function handle(): bool
    {
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        
        // التحقق من CSRF فقط للطلبات التي تغير البيانات
        if (in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? null;
            
            if (!$this->validateCsrfToken($token)) {
                $this->handleInvalidToken();
                return false;
            }
        }
        
        return true;
    }
    
    private function validateCsrfToken(?string $token): bool
    {
        if (!$token || !isset($_SESSION['csrf_token'])) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    private function handleInvalidToken(): void
    {
        if ($this->isAjaxRequest()) {
            http_response_code(419);
            header('Content-Type: application/json');
            echo json_encode([
                'message' => 'رمز الحماية غير صحيح. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.',
                'error' => 'csrf_token_mismatch'
            ]);
        } else {
            http_response_code(419);
            echo "<h1>419 - رمز الحماية غير صحيح</h1>";
            echo "<p>رمز الحماية غير صحيح. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.</p>";
            echo "<a href='javascript:history.back()'>العودة</a>";
        }
        exit;
    }
    
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    public static function generateToken(): string
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    public static function getTokenField(): string
    {
        $token = self::generateToken();
        return "<input type='hidden' name='csrf_token' value='{$token}'>";
    }
}
