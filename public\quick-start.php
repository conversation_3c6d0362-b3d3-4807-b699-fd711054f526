<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 تشغيل سريع - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .hero-section {
            padding: 50px 0;
            color: white;
            text-align: center;
        }
        .status-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .btn-custom {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            color: white;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s;
        }
        .btn-custom:hover {
            transform: scale(1.05);
            color: white;
            text-decoration: none;
        }
        .status-indicator {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-danger { background: #dc3545; }
        .step-box {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <h1 class="display-4 mb-4">🚀 تشغيل سريع لنظام CMS</h1>
            <p class="lead mb-5">دليل سريع لتشغيل النظام بدون Command Prompt</p>
        </div>
    </div>

    <div class="container">
        <!-- حالة النظام -->
        <div class="row">
            <div class="col-12">
                <div class="status-card">
                    <h3><i class="fas fa-heartbeat me-2"></i>حالة النظام</h3>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <p>
                                <span class="status-indicator <?php echo function_exists('mysqli_connect') ? 'status-success' : 'status-danger'; ?>"></span>
                                <strong>PHP:</strong> <?php echo function_exists('mysqli_connect') ? 'يعمل بشكل صحيح' : 'غير متاح'; ?>
                            </p>
                            <p>
                                <span class="status-indicator status-success"></span>
                                <strong>الملفات:</strong> متاحة
                            </p>
                            <p>
                                <span class="status-indicator status-success"></span>
                                <strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p>
                                <span class="status-indicator <?php 
                                    try {
                                        $pdo = new PDO("mysql:host=localhost", "root", "");
                                        echo 'status-success';
                                    } catch (Exception $e) {
                                        echo 'status-warning';
                                    }
                                ?>"></span>
                                <strong>MySQL:</strong> <?php 
                                    try {
                                        $pdo = new PDO("mysql:host=localhost", "root", "");
                                        echo 'متصل';
                                    } catch (Exception $e) {
                                        echo 'غير متصل - شغل WAMP';
                                    }
                                ?>
                            </p>
                            <p>
                                <span class="status-indicator <?php 
                                    try {
                                        $pdo = new PDO("mysql:host=localhost;dbname=nonprofit_cms", "root", "");
                                        echo 'status-success';
                                    } catch (Exception $e) {
                                        echo 'status-warning';
                                    }
                                ?>"></span>
                                <strong>قاعدة البيانات:</strong> <?php 
                                    try {
                                        $pdo = new PDO("mysql:host=localhost;dbname=nonprofit_cms", "root", "");
                                        echo 'جاهزة';
                                    } catch (Exception $e) {
                                        echo 'تحتاج إعداد';
                                    }
                                ?>
                            </p>
                            <p>
                                <span class="status-indicator status-success"></span>
                                <strong>المسار الحالي:</strong> <?php echo $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- خطوات التشغيل -->
        <div class="row">
            <div class="col-12">
                <div class="status-card">
                    <h3><i class="fas fa-list-ol me-2"></i>خطوات التشغيل السريع</h3>
                    
                    <div class="step-box">
                        <h5><i class="fas fa-download me-2 text-primary"></i>الخطوة 1: تحميل WAMP</h5>
                        <p>إذا لم يكن مثبت، حمل من: <a href="https://www.wampserver.com/en/" target="_blank">wampserver.com</a></p>
                    </div>

                    <div class="step-box">
                        <h5><i class="fas fa-copy me-2 text-success"></i>الخطوة 2: نسخ الملفات</h5>
                        <p>انسخ مجلد المشروع إلى: <code>C:\wamp64\www\CMS\</code></p>
                    </div>

                    <div class="step-box">
                        <h5><i class="fas fa-play me-2 text-warning"></i>الخطوة 3: تشغيل WAMP</h5>
                        <p>شغل WAMP وانتظر حتى تصبح الأيقونة خضراء</p>
                    </div>

                    <div class="step-box">
                        <h5><i class="fas fa-globe me-2 text-info"></i>الخطوة 4: فتح المتصفح</h5>
                        <p>اذهب إلى: <code>http://localhost/CMS/public/quick-start.php</code></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار سريعة -->
        <div class="row">
            <div class="col-12 text-center">
                <div class="status-card">
                    <h3><i class="fas fa-rocket me-2"></i>ابدأ الآن</h3>
                    <div class="mt-4">
                        <?php
                        try {
                            $pdo = new PDO("mysql:host=localhost;dbname=nonprofit_cms", "root", "");
                            echo '<a href="dashboard.php" class="btn-custom"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a>';
                            echo '<a href="store.php" class="btn-custom"><i class="fas fa-store me-2"></i>المتجر الإلكتروني</a>';
                            echo '<a href="login.php" class="btn-custom"><i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول</a>';
                        } catch (Exception $e) {
                            echo '<a href="create-database.php" class="btn-custom"><i class="fas fa-database me-2"></i>إنشاء قاعدة البيانات</a>';
                        }
                        ?>
                        <a href="test-connection.php" class="btn-custom"><i class="fas fa-plug me-2"></i>اختبار الاتصال</a>
                        <a href="home.php" class="btn-custom"><i class="fas fa-home me-2"></i>الصفحة الرئيسية</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="row">
            <div class="col-md-6">
                <div class="status-card">
                    <h5><i class="fas fa-info-circle me-2 text-primary"></i>معلومات مهمة</h5>
                    <ul>
                        <li><strong>المسار الحالي:</strong> <?php echo __DIR__; ?></li>
                        <li><strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></li>
                        <li><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></li>
                        <li><strong>المنطقة الزمنية:</strong> <?php echo date_default_timezone_get(); ?></li>
                    </ul>
                </div>
            </div>
            <div class="col-md-6">
                <div class="status-card">
                    <h5><i class="fas fa-question-circle me-2 text-warning"></i>مشاكل شائعة</h5>
                    <ul>
                        <li><strong>PHP غير معرف:</strong> استخدم WAMP بدلاً من Command Prompt</li>
                        <li><strong>MySQL غير متصل:</strong> تأكد من تشغيل WAMP</li>
                        <li><strong>قاعدة البيانات غير موجودة:</strong> اضغط "إنشاء قاعدة البيانات"</li>
                        <li><strong>الصفحة لا تعمل:</strong> تحقق من المسار الصحيح</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <footer class="text-center text-white py-4 mt-5">
        <div class="container">
            <p>&copy; 2024 نظام CMS الشامل - جاهز للاستخدام!</p>
            <p><i class="fas fa-clock me-2"></i>الوقت الحالي: <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
