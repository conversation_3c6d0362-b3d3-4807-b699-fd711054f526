-- البيانات الأولية للنظام

-- إدراج الأدوار الأساسية
INSERT INTO roles (name, display_name, description) VALUES
('super_admin', 'مدير النظام الرئيسي', 'صلاحيات كاملة على النظام'),
('admin', 'مدير النظام', 'صلاحيات إدارية عامة'),
('organization_admin', 'مدير المنظمة', 'إدارة المنظمة والموظفين'),
('hr_manager', 'مدير الموارد البشرية', 'إدارة الموظفين والمتطوعين'),
('finance_manager', 'مدير مالي', 'إدارة الشؤون المالية والتبرعات'),
('project_manager', 'مدير مشاريع', 'إدارة المشاريع والبرامج'),
('employee', 'موظف', 'صلاحيات أساسية للموظفين'),
('volunteer', 'متطوع', 'صلاحيات محدودة للمتطوعين');

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, display_name, module) VALUES
-- إدارة المستخدمين
('users.view', 'عرض المستخدمين', 'users'),
('users.create', 'إضافة مستخدم', 'users'),
('users.edit', 'تعديل المستخدمين', 'users'),
('users.delete', 'حذف المستخدمين', 'users'),
('users.activate', 'تفعيل/إلغاء تفعيل المستخدمين', 'users'),

-- إدارة الأدوار والصلاحيات
('roles.view', 'عرض الأدوار', 'roles'),
('roles.create', 'إضافة دور', 'roles'),
('roles.edit', 'تعديل الأدوار', 'roles'),
('roles.delete', 'حذف الأدوار', 'roles'),
('permissions.assign', 'تعيين الصلاحيات', 'permissions'),

-- إدارة المنظمات
('organizations.view', 'عرض المنظمات', 'organizations'),
('organizations.create', 'إضافة منظمة', 'organizations'),
('organizations.edit', 'تعديل المنظمات', 'organizations'),
('organizations.delete', 'حذف المنظمات', 'organizations'),
('organizations.approve', 'اعتماد المنظمات', 'organizations'),

-- إدارة الموظفين
('employees.view', 'عرض الموظفين', 'employees'),
('employees.create', 'إضافة موظف', 'employees'),
('employees.edit', 'تعديل الموظفين', 'employees'),
('employees.delete', 'حذف الموظفين', 'employees'),
('employees.payroll', 'إدارة الرواتب', 'employees'),

-- إدارة المتطوعين
('volunteers.view', 'عرض المتطوعين', 'volunteers'),
('volunteers.create', 'إضافة متطوع', 'volunteers'),
('volunteers.edit', 'تعديل المتطوعين', 'volunteers'),
('volunteers.delete', 'حذف المتطوعين', 'volunteers'),
('volunteers.approve', 'اعتماد المتطوعين', 'volunteers'),

-- إدارة المشاريع
('projects.view', 'عرض المشاريع', 'projects'),
('projects.create', 'إضافة مشروع', 'projects'),
('projects.edit', 'تعديل المشاريع', 'projects'),
('projects.delete', 'حذف المشاريع', 'projects'),
('projects.approve', 'اعتماد المشاريع', 'projects'),

-- إدارة التبرعات
('donations.view', 'عرض التبرعات', 'donations'),
('donations.create', 'إضافة تبرع', 'donations'),
('donations.edit', 'تعديل التبرعات', 'donations'),
('donations.delete', 'حذف التبرعات', 'donations'),
('donations.approve', 'اعتماد التبرعات', 'donations'),

-- إدارة المخزون
('inventory.view', 'عرض المخزون', 'inventory'),
('inventory.create', 'إضافة عنصر للمخزون', 'inventory'),
('inventory.edit', 'تعديل المخزون', 'inventory'),
('inventory.delete', 'حذف من المخزون', 'inventory'),
('inventory.transfer', 'نقل المخزون', 'inventory'),

-- إدارة الشؤون المالية
('finance.view', 'عرض الشؤون المالية', 'finance'),
('finance.create', 'إضافة قيد مالي', 'finance'),
('finance.edit', 'تعديل القيود المالية', 'finance'),
('finance.delete', 'حذف القيود المالية', 'finance'),
('finance.approve', 'اعتماد القيود المالية', 'finance'),
('finance.reports', 'التقارير المالية', 'finance'),

-- إدارة الاجتماعات
('meetings.view', 'عرض الاجتماعات', 'meetings'),
('meetings.create', 'إضافة اجتماع', 'meetings'),
('meetings.edit', 'تعديل الاجتماعات', 'meetings'),
('meetings.delete', 'حذف الاجتماعات', 'meetings'),

-- إدارة العقود
('contracts.view', 'عرض العقود', 'contracts'),
('contracts.create', 'إضافة عقد', 'contracts'),
('contracts.edit', 'تعديل العقود', 'contracts'),
('contracts.delete', 'حذف العقود', 'contracts'),
('contracts.approve', 'اعتماد العقود', 'contracts'),

-- التقارير
('reports.view', 'عرض التقارير', 'reports'),
('reports.create', 'إنشاء تقارير', 'reports'),
('reports.export', 'تصدير التقارير', 'reports'),

-- الإعدادات
('settings.view', 'عرض الإعدادات', 'settings'),
('settings.edit', 'تعديل الإعدادات', 'settings'),

-- سجل التدقيق
('audit.view', 'عرض سجل التدقيق', 'audit');

-- ربط الأدوار بالصلاحيات
-- مدير النظام الرئيسي - جميع الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- مدير النظام - معظم الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions WHERE name NOT LIKE 'settings.%';

-- مدير المنظمة
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions WHERE module IN ('employees', 'volunteers', 'projects', 'meetings', 'reports');

-- مدير الموارد البشرية
INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions WHERE module IN ('employees', 'volunteers');

-- مدير مالي
INSERT INTO role_permissions (role_id, permission_id)
SELECT 5, id FROM permissions WHERE module IN ('finance', 'donations', 'reports');

-- مدير مشاريع
INSERT INTO role_permissions (role_id, permission_id)
SELECT 6, id FROM permissions WHERE module IN ('projects', 'meetings', 'reports');

-- موظف - صلاحيات محدودة
INSERT INTO role_permissions (role_id, permission_id)
SELECT 7, id FROM permissions WHERE name LIKE '%.view' OR name LIKE '%.create';

-- متطوع - صلاحيات أساسية
INSERT INTO role_permissions (role_id, permission_id)
SELECT 8, id FROM permissions WHERE name LIKE '%.view' AND module IN ('projects', 'meetings');

-- إنشاء مستخدم مدير النظام الافتراضي
INSERT INTO users (name, email, password, role, is_active, created_at) VALUES
('مدير النظام', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', 1, NOW());

-- ربط مدير النظام بدور مدير النظام الرئيسي
INSERT INTO user_roles (user_id, role_id) VALUES (1, 1);

-- إعدادات النظام الافتراضية
INSERT INTO settings (key_name, value, description, type, is_public) VALUES
('app_name', 'نظام إدارة المؤسسات غير الربحية', 'اسم النظام', 'string', 1),
('app_version', '1.0.0', 'إصدار النظام', 'string', 1),
('default_language', 'ar', 'اللغة الافتراضية', 'string', 1),
('timezone', 'Asia/Riyadh', 'المنطقة الزمنية', 'string', 0),
('date_format', 'Y-m-d', 'تنسيق التاريخ', 'string', 0),
('currency', 'SAR', 'العملة الافتراضية', 'string', 1),
('max_upload_size', '10485760', 'الحد الأقصى لحجم الملف (بايت)', 'number', 0),
('session_lifetime', '120', 'مدة الجلسة (دقيقة)', 'number', 0),
('pagination_per_page', '15', 'عدد العناصر في الصفحة', 'number', 0),
('email_notifications', '1', 'تفعيل إشعارات البريد الإلكتروني', 'boolean', 0),
('sms_notifications', '0', 'تفعيل إشعارات الرسائل النصية', 'boolean', 0),
('maintenance_mode', '0', 'وضع الصيانة', 'boolean', 0);
