<?php
// تحديث قاعدة البيانات لإضافة المرفقات والتوقيعات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    echo "<h2>🔄 تحديث قاعدة البيانات...</h2>";
} catch (Exception $e) {
    die("<h2>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</h2>");
}

// تحديث جدول الرسائل الداخلية
try {
    $pdo->exec("
        ALTER TABLE internal_messages 
        ADD COLUMN IF NOT EXISTS priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        ADD COLUMN IF NOT EXISTS has_attachments BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS signature TEXT,
        ADD COLUMN IF NOT EXISTS read_receipt BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS delivery_receipt BOOLEAN DEFAULT FALSE
    ");
    echo "<p>✅ تم تحديث جدول الرسائل الداخلية</p>";
} catch (Exception $e) {
    echo "<p>⚠️ تحذير في تحديث جدول الرسائل: " . $e->getMessage() . "</p>";
}

// إنشاء جدول مرفقات الرسائل
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS message_attachments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            message_id INT NOT NULL,
            original_name VARCHAR(255) NOT NULL,
            stored_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            file_type VARCHAR(100) NOT NULL,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (message_id) REFERENCES internal_messages(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✅ تم إنشاء جدول مرفقات الرسائل</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول المرفقات: " . $e->getMessage() . "</p>";
}

// إنشاء جدول التوقيعات
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_signatures (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            signature_name VARCHAR(100) NOT NULL,
            signature_content TEXT NOT NULL,
            is_default BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✅ تم إنشاء جدول التوقيعات</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول التوقيعات: " . $e->getMessage() . "</p>";
}

// إنشاء جدول إعدادات المستخدمين
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            setting_key VARCHAR(100) NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_setting (user_id, setting_key)
        )
    ");
    echo "<p>✅ تم إنشاء جدول إعدادات المستخدمين</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول الإعدادات: " . $e->getMessage() . "</p>";
}

// إنشاء مجلد المرفقات
$uploads_dir = '../uploads';
$attachments_dir = $uploads_dir . '/attachments';

if (!file_exists($uploads_dir)) {
    mkdir($uploads_dir, 0755, true);
    echo "<p>✅ تم إنشاء مجلد الرفع</p>";
}

if (!file_exists($attachments_dir)) {
    mkdir($attachments_dir, 0755, true);
    echo "<p>✅ تم إنشاء مجلد المرفقات</p>";
}

// إنشاء ملف .htaccess لحماية المرفقات
$htaccess_content = "
# حماية المرفقات
<Files *>
    Order Deny,Allow
    Deny from all
</Files>

# السماح للملفات المسموحة فقط
<FilesMatch '\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|jpg|jpeg|png|gif)$'>
    Order Allow,Deny
    Allow from all
</FilesMatch>
";

file_put_contents($attachments_dir . '/.htaccess', $htaccess_content);
echo "<p>✅ تم إنشاء ملف الحماية للمرفقات</p>";

// إضافة توقيعات افتراضية للمستخدمين الموجودين
try {
    $users = $pdo->query("SELECT id, name, email FROM users")->fetchAll();
    
    foreach ($users as $user) {
        // التحقق من وجود توقيع افتراضي
        $existing = $pdo->prepare("SELECT id FROM user_signatures WHERE user_id = ? AND is_default = 1");
        $existing->execute([$user['id']]);
        
        if (!$existing->fetch()) {
            // إنشاء توقيع افتراضي
            $default_signature = "
<div style='font-family: Arial, sans-serif; color: #333; border-top: 2px solid #17a2b8; padding-top: 10px; margin-top: 20px;'>
    <strong>{$user['name']}</strong><br>
    <span style='color: #666;'>{$user['email']}</span><br>
    <small style='color: #999;'>نظام إدارة المؤسسات غير الربحية</small>
</div>";
            
            $stmt = $pdo->prepare("
                INSERT INTO user_signatures (user_id, signature_name, signature_content, is_default)
                VALUES (?, 'التوقيع الافتراضي', ?, 1)
            ");
            $stmt->execute([$user['id'], $default_signature]);
        }
    }
    
    echo "<p>✅ تم إنشاء التوقيعات الافتراضية للمستخدمين</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء التوقيعات: " . $e->getMessage() . "</p>";
}

// إضافة إعدادات افتراضية للمستخدمين
try {
    $users = $pdo->query("SELECT id FROM users")->fetchAll();
    
    $default_settings = [
        'email_notifications' => '1',
        'message_sound' => '1',
        'auto_signature' => '1',
        'read_receipt' => '0',
        'theme' => 'light'
    ];
    
    foreach ($users as $user) {
        foreach ($default_settings as $key => $value) {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO user_settings (user_id, setting_key, setting_value)
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$user['id'], $key, $value]);
        }
    }
    
    echo "<p>✅ تم إضافة الإعدادات الافتراضية للمستخدمين</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إضافة الإعدادات: " . $e->getMessage() . "</p>";
}

// عرض إحصائيات قاعدة البيانات
echo "<hr>";
echo "<h3>📊 إحصائيات قاعدة البيانات:</h3>";

try {
    $tables = [
        'users' => 'المستخدمين',
        'employees' => 'الموظفين',
        'internal_messages' => 'الرسائل الداخلية',
        'message_recipients' => 'مستقبلي الرسائل',
        'message_attachments' => 'مرفقات الرسائل',
        'user_signatures' => 'التوقيعات',
        'user_settings' => 'إعدادات المستخدمين'
    ];
    
    foreach ($tables as $table => $name) {
        $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
        echo "<p>✅ جدول <strong>$name</strong>: $count سجل</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ خطأ في جلب الإحصائيات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🎉 تم تحديث قاعدة البيانات بنجاح!</h3>";
echo "<p>الميزات الجديدة المضافة:</p>";
echo "<ul>";
echo "<li>✅ نظام مرفقات الرسائل</li>";
echo "<li>✅ نظام التوقيعات الرقمية</li>";
echo "<li>✅ إعدادات المستخدمين المخصصة</li>";
echo "<li>✅ أولويات الرسائل</li>";
echo "<li>✅ إشعارات القراءة والتسليم</li>";
echo "</ul>";
echo "<p><a href='/users.php' class='btn btn-primary'>إدارة المستخدمين</a> ";
echo "<a href='/messages.php' class='btn btn-success'>نظام الرسائل المحدث</a></p>";
echo "</div>";

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
ul { margin: 10px 0; padding-left: 20px; }
.btn { display: inline-block; padding: 8px 16px; margin: 5px; background: #007bff; color: white; border-radius: 5px; text-decoration: none; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
</style>";
?>
