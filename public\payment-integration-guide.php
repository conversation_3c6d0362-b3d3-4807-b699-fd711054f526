<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل تكامل بوابات الدفع السعودية - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #28a745, #20c997); }
        .gateway-card { 
            border-radius: 15px; 
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .gateway-card:hover { transform: translateY(-5px); }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .step-number {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
        }
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #17a2b8, #20c997);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-credit-card me-2"></i>دليل تكامل بوابات الدفع السعودية
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/store.php">
                    <i class="fas fa-store me-1"></i>المتجر
                </a>
                <a class="nav-link" href="/store-admin.php">
                    <i class="fas fa-cog me-1"></i>إدارة المتجر
                </a>
                <a class="nav-link" href="/dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- مقدمة -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h1 class="display-5">🏦 دليل تكامل بوابات الدفع السعودية</h1>
                    <p class="lead">دليل شامل لربط متجرك الإلكتروني مع أشهر بوابات الدفع في المملكة العربية السعودية</p>
                </div>
            </div>
        </div>

        <!-- الميزات الرئيسية -->
        <div class="row mb-5">
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon mx-auto">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h5>أمان عالي</h5>
                <p class="text-muted">جميع المعاملات محمية بأعلى معايير الأمان PCI DSS</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon mx-auto">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h5>متوافق مع الجوال</h5>
                <p class="text-muted">تجربة دفع سلسة على جميع الأجهزة المحمولة</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon mx-auto">
                    <i class="fas fa-clock"></i>
                </div>
                <h5>معالجة فورية</h5>
                <p class="text-muted">معالجة المدفوعات في الوقت الفعلي مع إشعارات فورية</p>
            </div>
        </div>

        <!-- بوابات الدفع -->
        <div class="row">
            <!-- مدى -->
            <div class="col-md-6 mb-4">
                <div class="card gateway-card h-100">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-credit-card me-2"></i>مدى (mada)</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الوصف:</strong> نظام الدفع الوطني السعودي الأكثر استخداماً</p>
                        <p><strong>الرسوم:</strong> 1.75%</p>
                        <p><strong>العملات:</strong> ريال سعودي (SAR)</p>
                        
                        <h6 class="mt-3">خطوات التكامل:</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">1</div>
                            <span>التسجيل في بوابة مدى للتجار</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">2</div>
                            <span>الحصول على Merchant ID و API Key</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">3</div>
                            <span>تكوين البوابة في النظام</span>
                        </div>
                        
                        <div class="code-block">
<pre><code>// مثال تكامل مدى
$mada_config = [
    'merchant_id' => 'YOUR_MERCHANT_ID',
    'api_key' => 'YOUR_API_KEY',
    'endpoint' => 'https://api.mada.sa/v1/payments',
    'return_url' => 'https://yourstore.com/payment/return',
    'cancel_url' => 'https://yourstore.com/payment/cancel'
];

// إنشاء طلب دفع
$payment_data = [
    'amount' => 100.00,
    'currency' => 'SAR',
    'order_id' => 'ORDER_123',
    'customer_email' => '<EMAIL>'
];

$response = mada_create_payment($mada_config, $payment_data);</code></pre>
                        </div>
                        
                        <a href="https://mada.com.sa" target="_blank" class="btn btn-success">
                            <i class="fas fa-external-link-alt me-2"></i>موقع مدى الرسمي
                        </a>
                    </div>
                </div>
            </div>

            <!-- تابي -->
            <div class="col-md-6 mb-4">
                <div class="card gateway-card h-100">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-calendar-alt me-2"></i>تابي (Tabby)</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الوصف:</strong> الدفع بالتقسيط على 4 دفعات بدون فوائد</p>
                        <p><strong>الرسوم:</strong> 2.9%</p>
                        <p><strong>العملات:</strong> SAR, AED, KWD, BHD</p>
                        <p><strong>الحد الأدنى:</strong> 100 ر.س | <strong>الحد الأقصى:</strong> 10,000 ر.س</p>
                        
                        <h6 class="mt-3">خطوات التكامل:</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">1</div>
                            <span>إنشاء حساب تاجر في تابي</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">2</div>
                            <span>الحصول على Public Key و Secret Key</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">3</div>
                            <span>تضمين Tabby SDK في موقعك</span>
                        </div>
                        
                        <div class="code-block">
<pre><code>// مثال تكامل تابي
$tabby_config = [
    'public_key' => 'pk_test_xxxxxxxx',
    'secret_key' => 'sk_test_xxxxxxxx',
    'endpoint' => 'https://api.tabby.ai/api/v2'
];

// إنشاء جلسة دفع
$session_data = [
    'payment' => [
        'amount' => '500.00',
        'currency' => 'SAR',
        'description' => 'Order #123'
    ],
    'order' => [
        'tax_amount' => '0.00',
        'shipping_amount' => '0.00',
        'discount_amount' => '0.00',
        'reference_id' => 'ORDER_123'
    ]
];

$session = tabby_create_session($tabby_config, $session_data);</code></pre>
                        </div>
                        
                        <a href="https://tabby.ai" target="_blank" class="btn btn-info">
                            <i class="fas fa-external-link-alt me-2"></i>موقع تابي الرسمي
                        </a>
                    </div>
                </div>
            </div>

            <!-- تمارا -->
            <div class="col-md-6 mb-4">
                <div class="card gateway-card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-shopping-bag me-2"></i>تمارا (Tamara)</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الوصف:</strong> اشتري الآن وادفع لاحقاً - حلول دفع مرنة</p>
                        <p><strong>الرسوم:</strong> 3.5%</p>
                        <p><strong>العملات:</strong> SAR, AED, KWD</p>
                        <p><strong>الحد الأدنى:</strong> 50 ر.س | <strong>الحد الأقصى:</strong> 5,000 ر.س</p>
                        
                        <h6 class="mt-3">خطوات التكامل:</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">1</div>
                            <span>التسجيل كتاجر في تمارا</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">2</div>
                            <span>الحصول على Merchant Token</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">3</div>
                            <span>تكامل Tamara API</span>
                        </div>
                        
                        <div class="code-block">
<pre><code>// مثال تكامل تمارا
$tamara_config = [
    'merchant_token' => 'YOUR_MERCHANT_TOKEN',
    'api_url' => 'https://api.tamara.co',
    'notification_url' => 'https://yourstore.com/tamara/webhook',
    'success_url' => 'https://yourstore.com/payment/success',
    'failure_url' => 'https://yourstore.com/payment/failure'
];

// إنشاء طلب دفع
$order_data = [
    'total_amount' => [
        'amount' => 300.00,
        'currency' => 'SAR'
    ],
    'description' => 'Order from Your Store',
    'merchant_url' => [
        'success' => $tamara_config['success_url'],
        'failure' => $tamara_config['failure_url'],
        'cancel' => $tamara_config['failure_url'],
        'notification' => $tamara_config['notification_url']
    ]
];

$checkout = tamara_create_checkout($tamara_config, $order_data);</code></pre>
                        </div>
                        
                        <a href="https://tamara.co" target="_blank" class="btn btn-warning">
                            <i class="fas fa-external-link-alt me-2"></i>موقع تمارا الرسمي
                        </a>
                    </div>
                </div>
            </div>

            <!-- هايبر باي -->
            <div class="col-md-6 mb-4">
                <div class="card gateway-card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-globe me-2"></i>هايبر باي (HyperPay)</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الوصف:</strong> بوابة دفع شاملة تدعم جميع وسائل الدفع</p>
                        <p><strong>الرسوم:</strong> 2.75%</p>
                        <p><strong>العملات:</strong> SAR, USD, EUR, AED</p>
                        
                        <h6 class="mt-3">خطوات التكامل:</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">1</div>
                            <span>إنشاء حساب في هايبر باي</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">2</div>
                            <span>الحصول على Entity ID و Access Token</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">3</div>
                            <span>تكوين Webhook URLs</span>
                        </div>
                        
                        <div class="code-block">
<pre><code>// مثال تكامل هايبر باي
$hyperpay_config = [
    'entity_id' => 'YOUR_ENTITY_ID',
    'access_token' => 'YOUR_ACCESS_TOKEN',
    'endpoint' => 'https://eu-test.oppwa.com/v1/checkouts',
    'test_mode' => true
];

// إنشاء checkout
$checkout_data = [
    'entityId' => $hyperpay_config['entity_id'],
    'amount' => '250.00',
    'currency' => 'SAR',
    'paymentType' => 'DB',
    'merchantTransactionId' => 'ORDER_123'
];

$checkout = hyperpay_create_checkout($hyperpay_config, $checkout_data);</code></pre>
                        </div>
                        
                        <a href="https://www.hyperpay.com" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-2"></i>موقع هايبر باي الرسمي
                        </a>
                    </div>
                </div>
            </div>

            <!-- باي تابس -->
            <div class="col-md-6 mb-4">
                <div class="card gateway-card h-100">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-credit-card me-2"></i>باي تابس (PayTabs)</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الوصف:</strong> حلول دفع متقدمة للتجارة الإلكترونية</p>
                        <p><strong>الرسوم:</strong> 2.85%</p>
                        <p><strong>العملات:</strong> SAR, USD, EUR, AED</p>
                        
                        <h6 class="mt-3">خطوات التكامل:</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">1</div>
                            <span>التسجيل في باي تابس</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">2</div>
                            <span>الحصول على Profile ID و Server Key</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">3</div>
                            <span>تكامل PayTabs API</span>
                        </div>
                        
                        <div class="code-block">
<pre><code>// مثال تكامل باي تابس
$paytabs_config = [
    'profile_id' => 'YOUR_PROFILE_ID',
    'server_key' => 'YOUR_SERVER_KEY',
    'endpoint' => 'https://secure.paytabs.sa/payment/request',
    'region' => 'SAU'
];

// إنشاء طلب دفع
$payment_data = [
    'profile_id' => $paytabs_config['profile_id'],
    'tran_type' => 'sale',
    'tran_class' => 'ecom',
    'cart_id' => 'ORDER_123',
    'cart_currency' => 'SAR',
    'cart_amount' => 180.00,
    'cart_description' => 'Payment for Order #123'
];

$payment = paytabs_create_payment($paytabs_config, $payment_data);</code></pre>
                        </div>
                        
                        <a href="https://www.paytabs.com" target="_blank" class="btn btn-secondary">
                            <i class="fas fa-external-link-alt me-2"></i>موقع باي تابس الرسمي
                        </a>
                    </div>
                </div>
            </div>

            <!-- STC Pay -->
            <div class="col-md-6 mb-4">
                <div class="card gateway-card h-100">
                    <div class="card-header bg-dark text-white">
                        <h5><i class="fas fa-mobile-alt me-2"></i>STC Pay</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الوصف:</strong> محفظة STC الرقمية الأكثر انتشاراً في السعودية</p>
                        <p><strong>الرسوم:</strong> 1.5%</p>
                        <p><strong>العملات:</strong> ريال سعودي (SAR)</p>
                        
                        <h6 class="mt-3">خطوات التكامل:</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">1</div>
                            <span>التسجيل في STC Pay للتجار</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">2</div>
                            <span>الحصول على Merchant ID و API Key</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="step-number">3</div>
                            <span>تكامل STC Pay SDK</span>
                        </div>
                        
                        <div class="code-block">
<pre><code>// مثال تكامل STC Pay
$stcpay_config = [
    'merchant_id' => 'YOUR_MERCHANT_ID',
    'api_key' => 'YOUR_API_KEY',
    'endpoint' => 'https://api.stcpay.com.sa/v1',
    'callback_url' => 'https://yourstore.com/stcpay/callback'
];

// إنشاء طلب دفع
$payment_request = [
    'MerchantId' => $stcpay_config['merchant_id'],
    'Amount' => 120.00,
    'Currency' => 'SAR',
    'MerchantNote' => 'Payment for Order #123',
    'CallBackUrl' => $stcpay_config['callback_url'],
    'MerchantReference' => 'ORDER_123'
];

$payment = stcpay_create_payment($stcpay_config, $payment_request);</code></pre>
                        </div>
                        
                        <a href="https://stcpay.com.sa" target="_blank" class="btn btn-dark">
                            <i class="fas fa-external-link-alt me-2"></i>موقع STC Pay الرسمي
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- نصائح مهمة -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>نصائح مهمة للتكامل</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🔒 الأمان:</h6>
                                <ul>
                                    <li>احفظ مفاتيح API في مكان آمن</li>
                                    <li>استخدم HTTPS دائماً</li>
                                    <li>تحقق من صحة البيانات المرسلة</li>
                                    <li>استخدم Webhook للتحقق من حالة الدفع</li>
                                </ul>
                                
                                <h6>🧪 الاختبار:</h6>
                                <ul>
                                    <li>ابدأ بالوضع التجريبي (Sandbox)</li>
                                    <li>اختبر جميع سيناريوهات الدفع</li>
                                    <li>تأكد من معالجة الأخطاء</li>
                                    <li>اختبر على أجهزة مختلفة</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>📱 تجربة المستخدم:</h6>
                                <ul>
                                    <li>اجعل عملية الدفع بسيطة</li>
                                    <li>وضح الرسوم والضرائب</li>
                                    <li>أضف خيارات دفع متنوعة</li>
                                    <li>أرسل إشعارات واضحة</li>
                                </ul>
                                
                                <h6>📊 المراقبة:</h6>
                                <ul>
                                    <li>راقب معدلات نجاح المعاملات</li>
                                    <li>تتبع الأخطاء والمشاكل</li>
                                    <li>احتفظ بسجلات مفصلة</li>
                                    <li>راجع التقارير بانتظام</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="row mt-4 mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-link me-2"></i>روابط مفيدة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>📚 الوثائق الرسمية:</h6>
                                <ul class="list-unstyled">
                                    <li><a href="https://developer.mada.com.sa" target="_blank">مدى - دليل المطورين</a></li>
                                    <li><a href="https://docs.tabby.ai" target="_blank">تابي - API Documentation</a></li>
                                    <li><a href="https://docs.tamara.co" target="_blank">تمارا - Developer Guide</a></li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>🛠️ أدوات التطوير:</h6>
                                <ul class="list-unstyled">
                                    <li><a href="https://docs.hyperpay.com" target="_blank">هايبر باي - Integration Guide</a></li>
                                    <li><a href="https://support.paytabs.com" target="_blank">باي تابس - Support Center</a></li>
                                    <li><a href="https://developer.stcpay.com.sa" target="_blank">STC Pay - Developer Portal</a></li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>🏛️ الجهات التنظيمية:</h6>
                                <ul class="list-unstyled">
                                    <li><a href="https://sama.gov.sa" target="_blank">البنك المركزي السعودي</a></li>
                                    <li><a href="https://mcit.gov.sa" target="_blank">وزارة الاتصالات</a></li>
                                    <li><a href="https://mci.gov.sa" target="_blank">وزارة التجارة</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
