/**
 * نظام إدارة المؤسسات غير الربحية
 * الملف الرئيسي للـ JavaScript
 */

// إعدادات عامة
const App = {
    config: {
        csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
        apiUrl: '/api/v1',
        locale: 'ar'
    },
    
    // تهيئة التطبيق
    init() {
        this.setupCSRF();
        this.setupAjax();
        this.setupFormValidation();
        this.setupTooltips();
        this.setupConfirmDialogs();
        this.setupFileUploads();
        this.setupDataTables();
        this.setupNotifications();
        this.setupAutoSave();
        
        console.log('تم تحميل نظام إدارة المؤسسات بنجاح');
    },
    
    // إعداد CSRF Token
    setupCSRF() {
        if (this.config.csrfToken) {
            // إضافة CSRF token لجميع طلبات AJAX
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': this.config.csrfToken
                }
            });
        }
    },
    
    // إعداد AJAX العام
    setupAjax() {
        // معالج الأخطاء العام
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            if (xhr.status === 401) {
                App.showAlert('انتهت جلستك. يرجى تسجيل الدخول مرة أخرى.', 'warning');
                setTimeout(() => {
                    window.location.href = '/auth/login';
                }, 2000);
            } else if (xhr.status === 403) {
                App.showAlert('ليس لديك صلاحية للقيام بهذا الإجراء.', 'danger');
            } else if (xhr.status === 419) {
                App.showAlert('انتهت صلاحية الصفحة. يرجى إعادة تحميلها.', 'warning');
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else if (xhr.status >= 500) {
                App.showAlert('حدث خطأ في الخادم. يرجى المحاولة مرة أخرى.', 'danger');
            }
        });
        
        // مؤشر التحميل للطلبات
        $(document).ajaxStart(function() {
            App.showLoading();
        }).ajaxStop(function() {
            App.hideLoading();
        });
    },
    
    // إعداد التحقق من صحة النماذج
    setupFormValidation() {
        // التحقق من النماذج عند الإرسال
        $('form[data-validate]').on('submit', function(e) {
            const form = $(this);
            const isValid = App.validateForm(form);
            
            if (!isValid) {
                e.preventDefault();
                return false;
            }
            
            // إضافة مؤشر التحميل للزر
            const submitBtn = form.find('button[type="submit"]');
            const originalText = submitBtn.html();
            submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...');
            submitBtn.prop('disabled', true);
            
            // استعادة النص الأصلي في حالة الخطأ
            setTimeout(() => {
                submitBtn.html(originalText);
                submitBtn.prop('disabled', false);
            }, 10000);
        });
        
        // التحقق الفوري من الحقول
        $('input, textarea, select').on('blur', function() {
            App.validateField($(this));
        });
    },
    
    // التحقق من صحة النموذج
    validateForm(form) {
        let isValid = true;
        
        form.find('input[required], textarea[required], select[required]').each(function() {
            if (!App.validateField($(this))) {
                isValid = false;
            }
        });
        
        return isValid;
    },
    
    // التحقق من صحة حقل واحد
    validateField(field) {
        const value = field.val().trim();
        const fieldType = field.attr('type');
        const isRequired = field.prop('required');
        let isValid = true;
        let errorMessage = '';
        
        // التحقق من الحقول المطلوبة
        if (isRequired && !value) {
            isValid = false;
            errorMessage = 'هذا الحقل مطلوب';
        }
        
        // التحقق من البريد الإلكتروني
        if (fieldType === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = 'يرجى إدخال بريد إلكتروني صحيح';
            }
        }
        
        // التحقق من رقم الجوال
        if (field.hasClass('phone-input') && value) {
            const phoneRegex = /^(\+966|0)?[5][0-9]{8}$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                errorMessage = 'يرجى إدخال رقم جوال صحيح';
            }
        }
        
        // التحقق من رقم الهوية
        if (field.hasClass('national-id-input') && value) {
            const nationalIdRegex = /^[12][0-9]{9}$/;
            if (!nationalIdRegex.test(value)) {
                isValid = false;
                errorMessage = 'يرجى إدخال رقم هوية صحيح';
            }
        }
        
        // عرض أو إخفاء رسالة الخطأ
        App.toggleFieldError(field, isValid, errorMessage);
        
        return isValid;
    },
    
    // عرض أو إخفاء خطأ الحقل
    toggleFieldError(field, isValid, errorMessage) {
        const feedbackElement = field.siblings('.invalid-feedback');
        
        if (isValid) {
            field.removeClass('is-invalid').addClass('is-valid');
            feedbackElement.hide();
        } else {
            field.removeClass('is-valid').addClass('is-invalid');
            if (feedbackElement.length === 0) {
                field.after(`<div class="invalid-feedback">${errorMessage}</div>`);
            } else {
                feedbackElement.text(errorMessage).show();
            }
        }
    },
    
    // إعداد التلميحات
    setupTooltips() {
        // تفعيل Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // إعداد مربعات التأكيد
    setupConfirmDialogs() {
        $('[data-confirm]').on('click', function(e) {
            e.preventDefault();
            const message = $(this).data('confirm');
            const href = $(this).attr('href') || $(this).data('href');
            
            if (confirm(message)) {
                if (href) {
                    window.location.href = href;
                } else {
                    $(this).closest('form').submit();
                }
            }
        });
    },
    
    // إعداد رفع الملفات
    setupFileUploads() {
        $('input[type="file"]').on('change', function() {
            const file = this.files[0];
            const maxSize = $(this).data('max-size') || 10485760; // 10MB
            const allowedTypes = $(this).data('allowed-types');
            
            if (file) {
                // التحقق من حجم الملف
                if (file.size > maxSize) {
                    App.showAlert('حجم الملف كبير جداً. الحد الأقصى المسموح: ' + App.formatFileSize(maxSize), 'warning');
                    $(this).val('');
                    return;
                }
                
                // التحقق من نوع الملف
                if (allowedTypes) {
                    const fileExtension = file.name.split('.').pop().toLowerCase();
                    const allowedTypesArray = allowedTypes.split(',');
                    
                    if (!allowedTypesArray.includes(fileExtension)) {
                        App.showAlert('نوع الملف غير مدعوم. الأنواع المسموحة: ' + allowedTypes, 'warning');
                        $(this).val('');
                        return;
                    }
                }
                
                // عرض معاينة للصور
                if (file.type.startsWith('image/')) {
                    App.previewImage(this, file);
                }
            }
        });
    },
    
    // معاينة الصورة
    previewImage(input, file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = $(input).siblings('.image-preview');
            if (preview.length === 0) {
                $(input).after(`<div class="image-preview mt-2">
                    <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                </div>`);
            } else {
                preview.find('img').attr('src', e.target.result);
            }
        };
        reader.readAsDataURL(file);
    },
    
    // إعداد جداول البيانات
    setupDataTables() {
        if ($.fn.DataTable) {
            $('.data-table').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: 'no-sort' }
                ]
            });
        }
    },
    
    // إعداد الإشعارات
    setupNotifications() {
        // تحديث الإشعارات كل 30 ثانية
        setInterval(() => {
            App.fetchNotifications();
        }, 30000);
        
        // تحديد الإشعارات كمقروءة عند النقر
        $(document).on('click', '.notification-item', function() {
            const notificationId = $(this).data('id');
            if (notificationId) {
                App.markNotificationAsRead(notificationId);
            }
        });
    },
    
    // جلب الإشعارات
    fetchNotifications() {
        $.get('/api/notifications')
            .done(function(data) {
                App.updateNotificationBadge(data.unread_count);
            })
            .fail(function() {
                // تجاهل الأخطاء في جلب الإشعارات
            });
    },
    
    // تحديث شارة الإشعارات
    updateNotificationBadge(count) {
        const badge = $('.notification-badge');
        if (count > 0) {
            badge.text(count).show();
        } else {
            badge.hide();
        }
    },
    
    // تحديد الإشعار كمقروء
    markNotificationAsRead(notificationId) {
        $.post(`/api/notifications/${notificationId}/read`)
            .done(function() {
                App.fetchNotifications();
            });
    },
    
    // إعداد الحفظ التلقائي
    setupAutoSave() {
        let autoSaveTimer;
        
        $('form[data-autosave]').on('input', 'input, textarea, select', function() {
            clearTimeout(autoSaveTimer);
            const form = $(this).closest('form');
            
            autoSaveTimer = setTimeout(() => {
                App.autoSaveForm(form);
            }, 5000); // حفظ تلقائي كل 5 ثوان
        });
    },
    
    // الحفظ التلقائي للنموذج
    autoSaveForm(form) {
        const formData = form.serialize();
        const saveUrl = form.data('autosave-url');
        
        if (saveUrl) {
            $.post(saveUrl, formData)
                .done(function() {
                    App.showToast('تم الحفظ تلقائياً', 'success');
                })
                .fail(function() {
                    App.showToast('فشل الحفظ التلقائي', 'warning');
                });
        }
    },
    
    // عرض تنبيه
    showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('.main-content').prepend(alertHtml);
        
        // إخفاء التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            $('.alert').first().alert('close');
        }, 5000);
    },
    
    // عرض رسالة منبثقة
    showToast(message, type = 'info') {
        // إنشاء عنصر Toast إذا لم يكن موجوداً
        if ($('#toast-container').length === 0) {
            $('body').append('<div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1055;"></div>');
        }
        
        const toastHtml = `
            <div class="toast" role="alert">
                <div class="toast-header">
                    <i class="fas fa-${this.getAlertIcon(type)} me-2 text-${type}"></i>
                    <strong class="me-auto">إشعار</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;
        
        const toastElement = $(toastHtml);
        $('#toast-container').append(toastElement);
        
        const toast = new bootstrap.Toast(toastElement[0]);
        toast.show();
        
        // إزالة العنصر بعد الإخفاء
        toastElement.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    },
    
    // الحصول على أيقونة التنبيه
    getAlertIcon(type) {
        const icons = {
            success: 'check-circle',
            danger: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    // عرض مؤشر التحميل
    showLoading() {
        if ($('#loading-overlay').length === 0) {
            $('body').append(`
                <div id="loading-overlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.5); z-index: 9999;">
                    <div class="spinner-border text-light" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            `);
        }
    },
    
    // إخفاء مؤشر التحميل
    hideLoading() {
        $('#loading-overlay').remove();
    },
    
    // تنسيق حجم الملف
    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // تنسيق التاريخ
    formatDate(date, format = 'YYYY-MM-DD') {
        // يمكن استخدام مكتبة moment.js أو date-fns هنا
        return new Date(date).toLocaleDateString('ar-SA');
    },
    
    // تنسيق الأرقام
    formatNumber(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    }
};

// تهيئة التطبيق عند تحميل الصفحة
$(document).ready(function() {
    App.init();
});

// تصدير الكائن للاستخدام العام
window.App = App;
