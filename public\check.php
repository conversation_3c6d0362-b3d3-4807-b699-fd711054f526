<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص سريع للنظام</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .ok { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 فحص سريع للنظام</h1>
        
        <h3>1. معلومات PHP:</h3>
        <p><strong>إصدار PHP:</strong> <?= PHP_VERSION ?></p>
        <p><strong>نظام التشغيل:</strong> <?= PHP_OS ?></p>
        
        <h3>2. فحص الإضافات:</h3>
        <?php if (extension_loaded('pdo')): ?>
            <p class="ok">✅ PDO متوفر</p>
        <?php else: ?>
            <p class="error">❌ PDO غير متوفر</p>
        <?php endif; ?>
        
        <?php if (extension_loaded('pdo_mysql')): ?>
            <p class="ok">✅ PDO MySQL متوفر</p>
        <?php else: ?>
            <p class="error">❌ PDO MySQL غير متوفر</p>
        <?php endif; ?>
        
        <h3>3. اختبار الاتصال بـ MySQL:</h3>
        <?php
        $hosts = ['localhost', '127.0.0.1'];
        $port = 3306;
        
        foreach ($hosts as $host) {
            echo "<h4>اختبار {$host}:{$port}</h4>";
            
            // اختبار المنفذ
            $connection = @fsockopen($host, $port, $errno, $errstr, 3);
            if ($connection) {
                echo "<p class='ok'>✅ المنفذ مفتوح</p>";
                fclose($connection);
                
                // اختبار MySQL
                try {
                    $pdo = new PDO("mysql:host={$host};port={$port}", 'root', '', [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_TIMEOUT => 3
                    ]);
                    echo "<p class='ok'>✅ نجح الاتصال بـ MySQL</p>";
                    
                    $version = $pdo->query('SELECT VERSION()')->fetchColumn();
                    echo "<p>إصدار MySQL: {$version}</p>";
                    
                } catch (PDOException $e) {
                    echo "<p class='error'>❌ فشل الاتصال: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p class='error'>❌ المنفذ مغلق - {$errstr}</p>";
            }
        }
        ?>
        
        <h3>4. فحص الملفات:</h3>
        <?php
        $files = [
            '../install.php' => 'ملف التثبيت',
            '../.env' => 'ملف الإعدادات',
            '../database/migrations/001_create_initial_tables.sql' => 'ملف قاعدة البيانات'
        ];
        
        foreach ($files as $file => $desc) {
            if (file_exists($file)) {
                echo "<p class='ok'>✅ {$desc} موجود</p>";
            } else {
                echo "<p class='warning'>⚠️ {$desc} غير موجود</p>";
            }
        }
        ?>
        
        <div class="info">
            <h4>💡 نصائح لحل المشاكل:</h4>
            <ul>
                <li>تأكد من تشغيل WAMP وأن الأيقونة خضراء</li>
                <li>جرب إعادة تشغيل خدمة MySQL من WAMP</li>
                <li>تحقق من أن المنفذ 3306 غير محجوب</li>
                <li>جرب استخدام 127.0.0.1 بدلاً من localhost</li>
            </ul>
        </div>
        
        <p>
            <a href="/install.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">العودة للتثبيت</a>
            <a href="/" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">الصفحة الرئيسية</a>
        </p>
    </div>
</body>
</html>
