# دليل البدء السريع - نظام إدارة المؤسسات غير الربحية

## 🚀 البدء السريع

### 1. تشغيل النظام
```bash
# تشغيل الخادم المحلي
php -S localhost:8000 -t public

# أو استخدام ملف التشغيل
serve.bat
```

### 2. تثبيت قاعدة البيانات
1. افتح المتصفح واذهب إلى: `http://localhost:8000/install.php`
2. أدخل بيانات قاعدة البيانات
3. اضغط "بدء التثبيت"

### 3. تسجيل الدخول
- الرابط: `http://localhost:8000/auth/login`
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `password`

## 📁 هيكل المشروع

```
CMS/
├── app/                    # تطبيق النظام
│   ├── Controllers/        # المتحكمات
│   ├── Models/            # النماذج
│   ├── Views/             # القوالب
│   ├── Middleware/        # الوسطاء
│   ├── Services/          # الخدمات
│   └── Helpers/           # المساعدات
├── core/                  # النواة الأساسية
│   ├── Database/          # قاعدة البيانات
│   ├── Router/            # التوجيه
│   ├── Auth/              # المصادقة
│   └── Validation/        # التحقق
├── public/                # الملفات العامة
│   ├── assets/            # الأصول (CSS, JS, Images)
│   ├── uploads/           # الملفات المرفوعة
│   └── index.php          # نقطة الدخول
├── config/                # ملفات التكوين
├── database/              # قاعدة البيانات
│   ├── migrations/        # الترحيلات
│   └── seeds/             # البيانات الأولية
├── storage/               # التخزين
│   ├── logs/              # السجلات
│   ├── cache/             # التخزين المؤقت
│   └── sessions/          # الجلسات
└── routes/                # التوجيهات
```

## 🔧 الإعدادات الأساسية

### متغيرات البيئة (.env)
```env
# إعدادات التطبيق
APP_NAME="نظام إدارة المؤسسات غير الربحية"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000

# قاعدة البيانات
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=nonprofit_cms
DB_USERNAME=root
DB_PASSWORD=

# الأمان
JWT_SECRET=your-secret-key
APP_KEY=your-app-key
```

## 📊 الوحدات الرئيسية

### 1. إدارة المستخدمين
- **المسار**: `/users`
- **الصلاحيات**: `users.view`, `users.create`, `users.edit`, `users.delete`
- **الوظائف**: إضافة، تعديل، حذف، تفعيل المستخدمين

### 2. إدارة المنظمات
- **المسار**: `/organizations`
- **الصلاحيات**: `organizations.view`, `organizations.create`, `organizations.edit`
- **الوظائف**: تسجيل المنظمات، إدارة التراخيص، الوثائق

### 3. الموارد البشرية
- **المسارات**: `/employees`, `/volunteers`
- **الوظائف**: إدارة الموظفين والمتطوعين، الرواتب، الحضور

### 4. إدارة المشاريع
- **المسار**: `/projects`
- **الوظائف**: إنشاء المشاريع، متابعة التقدم، إدارة المهام

### 5. الشؤون المالية
- **المسارات**: `/finance`, `/donations`, `/inventory`
- **الوظائف**: الحسابات المالية، التبرعات، المخزون

## 🔐 نظام الصلاحيات

### الأدوار الافتراضية
1. **مدير النظام الرئيسي** (`super_admin`)
2. **مدير النظام** (`admin`)
3. **مدير المنظمة** (`organization_admin`)
4. **مدير الموارد البشرية** (`hr_manager`)
5. **مدير مالي** (`finance_manager`)
6. **مدير مشاريع** (`project_manager`)
7. **موظف** (`employee`)
8. **متطوع** (`volunteer`)

### الصلاحيات الأساسية
- `*.view` - عرض
- `*.create` - إنشاء
- `*.edit` - تعديل
- `*.delete` - حذف
- `*.approve` - اعتماد

## 🛠️ التطوير

### إضافة كنترولر جديد
```php
<?php
namespace App\Controllers;

class NewController extends BaseController
{
    public function index(): void
    {
        $this->requireAuth();
        $this->view('new.index');
    }
}
```

### إضافة نموذج جديد
```php
<?php
namespace App\Models;

use Core\Database\Model;

class NewModel extends Model
{
    protected $table = 'new_table';
    protected $fillable = ['field1', 'field2'];
}
```

### إضافة مسار جديد
```php
// في routes/web.php
$router->get('/new-route', 'NewController@index');
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ 404 - الصفحة غير موجودة**
   - تأكد من تشغيل الخادم
   - تحقق من ملف `.htaccess`

2. **خطأ قاعدة البيانات**
   - تأكد من بيانات الاتصال في `.env`
   - تحقق من تشغيل MySQL

3. **خطأ الصلاحيات**
   - تأكد من صلاحيات مجلدات `storage/` و `public/uploads/`

### السجلات
- **سجل الأخطاء**: `storage/logs/error_YYYY-MM-DD.log`
- **سجل الوصول**: `storage/logs/access_YYYY-MM-DD.log`
- **سجل البريد**: `storage/logs/email_YYYY-MM-DD.log`

## 📱 واجهة API

### المصادقة
```bash
POST /api/v1/auth/login
{
    "email": "<EMAIL>",
    "password": "password"
}
```

### الاستعلامات
```bash
# قائمة المنظمات
GET /api/v1/organizations
Authorization: Bearer {token}

# قائمة المشاريع
GET /api/v1/projects
Authorization: Bearer {token}
```

## 🚀 النشر

### متطلبات الخادم
- PHP 8.0+
- MySQL 5.7+
- Apache/Nginx
- SSL Certificate

### خطوات النشر
1. رفع الملفات للخادم
2. إعداد قاعدة البيانات
3. تكوين ملف `.env`
4. إعداد صلاحيات المجلدات
5. تكوين Apache/Nginx

## 📞 الدعم

- **الوثائق**: راجع `README.md`
- **المشاكل**: استخدم GitHub Issues
- **البريد الإلكتروني**: <EMAIL>

---

**نصائح مهمة:**
- احرص على تحديث كلمة مرور المدير الافتراضية
- فعّل HTTPS في الإنتاج
- قم بعمل نسخ احتياطية دورية
- راقب سجلات النظام بانتظام
