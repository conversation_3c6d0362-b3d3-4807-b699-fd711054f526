<?php

namespace App\Controllers;

use Core\Auth\Auth;
use Core\Validation\Validator;

abstract class BaseController
{
    protected $auth;
    protected $data = [];
    protected $viewPath = 'app/Views/';

    public function __construct()
    {
        $this->auth = Auth::getInstance();
        $this->initializeGlobalData();
    }

    protected function view(string $view, array $data = []): void
    {
        $data = array_merge($this->data, $data);
        
        extract($data);
        
        $viewFile = $this->viewPath . str_replace('.', '/', $view) . '.php';
        
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new \Exception("View file not found: {$viewFile}");
        }
    }

    protected function json(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    protected function redirect(string $url): void
    {
        header("Location: {$url}");
        exit;
    }

    protected function back(): void
    {
        $referer = $_SERVER['HTTP_REFERER'] ?? '/';
        $this->redirect($referer);
    }

    protected function validate(array $data, array $rules, array $messages = []): array
    {
        $validator = Validator::make($data, $rules, $messages);
        
        if ($validator->fails()) {
            $this->handleValidationErrors($validator->errors());
        }
        
        return $data;
    }

    protected function handleValidationErrors(array $errors): void
    {
        if ($this->isAjaxRequest()) {
            $this->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $errors
            ], 422);
        } else {
            $_SESSION['validation_errors'] = $errors;
            $_SESSION['old_input'] = $_POST;
            $this->back();
        }
    }

    protected function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    protected function requireAuth(): void
    {
        if (!$this->auth->check()) {
            if ($this->isAjaxRequest()) {
                $this->json(['message' => 'غير مصرح'], 401);
            } else {
                $this->redirect('/login');
            }
        }
    }

    protected function requireRole(string $role): void
    {
        $this->requireAuth();
        
        if (!$this->auth->hasRole($role)) {
            if ($this->isAjaxRequest()) {
                $this->json(['message' => 'غير مصرح'], 403);
            } else {
                $this->redirect('/unauthorized');
            }
        }
    }

    protected function requirePermission(string $permission): void
    {
        $this->requireAuth();
        
        if (!$this->auth->hasPermission($permission)) {
            if ($this->isAjaxRequest()) {
                $this->json(['message' => 'غير مصرح'], 403);
            } else {
                $this->redirect('/unauthorized');
            }
        }
    }

    protected function getInput(string $key, $default = null)
    {
        return $_POST[$key] ?? $_GET[$key] ?? $default;
    }

    protected function getAllInput(): array
    {
        return array_merge($_GET, $_POST);
    }

    protected function hasInput(string $key): bool
    {
        return isset($_POST[$key]) || isset($_GET[$key]);
    }

    protected function flash(string $key, string $message): void
    {
        $_SESSION['flash'][$key] = $message;
    }

    protected function getFlash(string $key): ?string
    {
        $message = $_SESSION['flash'][$key] ?? null;
        unset($_SESSION['flash'][$key]);
        return $message;
    }

    protected function old(string $key, $default = null)
    {
        $value = $_SESSION['old_input'][$key] ?? $default;
        return $value;
    }

    protected function clearOldInput(): void
    {
        unset($_SESSION['old_input']);
    }

    protected function getValidationErrors(): array
    {
        $errors = $_SESSION['validation_errors'] ?? [];
        unset($_SESSION['validation_errors']);
        return $errors;
    }

    protected function uploadFile(array $file, string $directory = 'uploads'): ?string
    {
        if (!isset($file['tmp_name']) || $file['error'] !== UPLOAD_ERR_OK) {
            return null;
        }

        $uploadDir = "public/{$directory}/";
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '.' . $extension;
        $filepath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            return "{$directory}/{$filename}";
        }

        return null;
    }

    protected function deleteFile(string $filepath): bool
    {
        $fullPath = "public/{$filepath}";
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }
        return false;
    }

    protected function paginate(array $data, int $page = 1, int $perPage = 15): array
    {
        $total = count($data);
        $offset = ($page - 1) * $perPage;
        $items = array_slice($data, $offset, $perPage);

        return [
            'data' => $items,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $total,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total),
        ];
    }

    private function initializeGlobalData(): void
    {
        $this->data['auth'] = $this->auth;
        $this->data['user'] = $this->auth->user();
        $this->data['errors'] = $this->getValidationErrors();
        $this->data['success'] = $this->getFlash('success');
        $this->data['error'] = $this->getFlash('error');
        $this->data['info'] = $this->getFlash('info');
        $this->data['warning'] = $this->getFlash('warning');
    }
}
