<?php

namespace App\Middleware;

class ThrottleMiddleware
{
    private $maxAttempts;
    private $decayMinutes;
    
    public function __construct(int $maxAttempts = 60, int $decayMinutes = 1)
    {
        $this->maxAttempts = $maxAttempts;
        $this->decayMinutes = $decayMinutes;
    }
    
    public function handle(): bool
    {
        $key = $this->resolveRequestSignature();
        
        if ($this->tooManyAttempts($key)) {
            $this->handleTooManyAttempts();
            return false;
        }
        
        $this->hit($key);
        
        return true;
    }
    
    private function resolveRequestSignature(): string
    {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        
        return sha1($ip . '|' . $uri . '|' . $method);
    }
    
    private function tooManyAttempts(string $key): bool
    {
        $attempts = $this->getAttempts($key);
        return $attempts >= $this->maxAttempts;
    }
    
    private function getAttempts(string $key): int
    {
        $cacheFile = $this->getCacheFile($key);
        
        if (!file_exists($cacheFile)) {
            return 0;
        }
        
        $data = json_decode(file_get_contents($cacheFile), true);
        
        if (!$data || $data['expires_at'] < time()) {
            unlink($cacheFile);
            return 0;
        }
        
        return $data['attempts'];
    }
    
    private function hit(string $key): void
    {
        $cacheFile = $this->getCacheFile($key);
        $attempts = $this->getAttempts($key) + 1;
        $expiresAt = time() + ($this->decayMinutes * 60);
        
        $data = [
            'attempts' => $attempts,
            'expires_at' => $expiresAt
        ];
        
        file_put_contents($cacheFile, json_encode($data));
    }
    
    private function getCacheFile(string $key): string
    {
        $cacheDir = ROOT_PATH . '/storage/cache';
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }
        
        return $cacheDir . '/throttle_' . $key . '.json';
    }
    
    private function handleTooManyAttempts(): void
    {
        if ($this->isAjaxRequest()) {
            http_response_code(429);
            header('Content-Type: application/json');
            echo json_encode([
                'message' => 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة مرة أخرى لاحقاً.',
                'retry_after' => $this->decayMinutes * 60
            ]);
        } else {
            http_response_code(429);
            echo "<h1>429 - تم تجاوز الحد المسموح</h1>";
            echo "<p>تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة مرة أخرى بعد {$this->decayMinutes} دقيقة.</p>";
        }
        exit;
    }
    
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
