<?php

namespace Core\Router;

class Router
{
    private array $routes = [];
    private array $middleware = [];
    private string $currentPrefix = '';
    private array $currentMiddleware = [];

    public function get(string $uri, $action): self
    {
        return $this->addRoute('GET', $uri, $action);
    }

    public function post(string $uri, $action): self
    {
        return $this->addRoute('POST', $uri, $action);
    }

    public function put(string $uri, $action): self
    {
        return $this->addRoute('PUT', $uri, $action);
    }

    public function patch(string $uri, $action): self
    {
        return $this->addRoute('PATCH', $uri, $action);
    }

    public function delete(string $uri, $action): self
    {
        return $this->addRoute('DELETE', $uri, $action);
    }

    public function any(string $uri, $action): self
    {
        $methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
        foreach ($methods as $method) {
            $this->addRoute($method, $uri, $action);
        }
        return $this;
    }

    public function group(array $attributes, callable $callback): void
    {
        $previousPrefix = $this->currentPrefix;
        $previousMiddleware = $this->currentMiddleware;

        if (isset($attributes['prefix'])) {
            $this->currentPrefix .= '/' . trim($attributes['prefix'], '/');
        }

        if (isset($attributes['middleware'])) {
            $this->currentMiddleware = array_merge(
                $this->currentMiddleware,
                is_array($attributes['middleware']) ? $attributes['middleware'] : [$attributes['middleware']]
            );
        }

        $callback($this);

        $this->currentPrefix = $previousPrefix;
        $this->currentMiddleware = $previousMiddleware;
    }

    public function middleware($middleware): self
    {
        $lastRoute = end($this->routes);
        if ($lastRoute) {
            $key = key($this->routes);
            $this->routes[$key]['middleware'] = array_merge(
                $this->routes[$key]['middleware'] ?? [],
                is_array($middleware) ? $middleware : [$middleware]
            );
        }
        return $this;
    }

    private function addRoute(string $method, string $uri, $action): self
    {
        $uri = $this->currentPrefix . '/' . trim($uri, '/');
        $uri = '/' . trim($uri, '/');
        
        $this->routes[] = [
            'method' => $method,
            'uri' => $uri,
            'action' => $action,
            'middleware' => $this->currentMiddleware,
        ];

        return $this;
    }

    public function dispatch(string $requestUri, string $requestMethod): void
    {
        $requestUri = parse_url($requestUri, PHP_URL_PATH);
        $requestUri = '/' . trim($requestUri, '/');

        foreach ($this->routes as $route) {
            if ($this->matchRoute($route, $requestUri, $requestMethod)) {
                $this->executeRoute($route, $requestUri);
                return;
            }
        }

        $this->handleNotFound();
    }

    private function matchRoute(array $route, string $requestUri, string $requestMethod): bool
    {
        if ($route['method'] !== $requestMethod) {
            return false;
        }

        $pattern = $this->convertToRegex($route['uri']);
        return preg_match($pattern, $requestUri);
    }

    private function convertToRegex(string $uri): string
    {
        // Convert route parameters like {id} to regex patterns
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $uri);
        $pattern = str_replace('/', '\/', $pattern);
        return '/^' . $pattern . '$/';
    }

    private function executeRoute(array $route, string $requestUri): void
    {
        // Extract parameters from URI
        $params = $this->extractParameters($route['uri'], $requestUri);

        // Execute middleware
        if (!empty($route['middleware'])) {
            foreach ($route['middleware'] as $middleware) {
                if (!$this->executeMiddleware($middleware)) {
                    return;
                }
            }
        }

        // Execute action
        if (is_callable($route['action'])) {
            call_user_func_array($route['action'], $params);
        } elseif (is_string($route['action'])) {
            $this->executeControllerAction($route['action'], $params);
        }
    }

    private function extractParameters(string $routeUri, string $requestUri): array
    {
        $routeParts = explode('/', trim($routeUri, '/'));
        $requestParts = explode('/', trim($requestUri, '/'));
        $params = [];

        foreach ($routeParts as $index => $part) {
            if (preg_match('/\{([^}]+)\}/', $part, $matches)) {
                $paramName = $matches[1];
                $params[$paramName] = $requestParts[$index] ?? null;
            }
        }

        return array_values($params);
    }

    private function executeMiddleware(string $middleware): bool
    {
        $config = require __DIR__ . '/../../config/app.php';
        
        if (isset($config['middleware'][$middleware])) {
            $middlewareClass = $config['middleware'][$middleware];
            
            if (class_exists($middlewareClass)) {
                $middlewareInstance = new $middlewareClass();
                return $middlewareInstance->handle();
            }
        }

        return true;
    }

    private function executeControllerAction(string $action, array $params): void
    {
        if (strpos($action, '@') !== false) {
            [$controller, $method] = explode('@', $action);
            
            $controllerClass = "App\\Controllers\\{$controller}";
            
            if (class_exists($controllerClass)) {
                $controllerInstance = new $controllerClass();
                
                if (method_exists($controllerInstance, $method)) {
                    call_user_func_array([$controllerInstance, $method], $params);
                } else {
                    $this->handleNotFound();
                }
            } else {
                $this->handleNotFound();
            }
        }
    }

    private function handleNotFound(): void
    {
        http_response_code(404);
        echo "404 - Page Not Found";
    }

    public function getRoutes(): array
    {
        return $this->routes;
    }
}
