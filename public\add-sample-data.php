<?php
// إضافة بيانات تجريبية للمتجر
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    echo "<h2>🛒 إضافة بيانات تجريبية للمتجر...</h2>";
} catch (Exception $e) {
    die("<h2>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</h2>");
}

// إضافة فئات تجريبية
try {
    $categories = [
        ['name' => 'الإلكترونيات', 'description' => 'أجهزة إلكترونية متنوعة', 'sort_order' => 1],
        ['name' => 'الملابس', 'description' => 'ملابس رجالية ونسائية', 'sort_order' => 2],
        ['name' => 'الكتب', 'description' => 'كتب ومراجع متنوعة', 'sort_order' => 3],
        ['name' => 'المنزل والحديقة', 'description' => 'أدوات منزلية ومستلزمات الحديقة', 'sort_order' => 4],
        ['name' => 'الرياضة', 'description' => 'معدات ومستلزمات رياضية', 'sort_order' => 5],
        ['name' => 'الجمال والعناية', 'description' => 'منتجات التجميل والعناية الشخصية', 'sort_order' => 6]
    ];
    
    foreach ($categories as $category) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO product_categories (name, description, sort_order, is_active)
            VALUES (?, ?, ?, 1)
        ");
        $stmt->execute([$category['name'], $category['description'], $category['sort_order']]);
    }
    
    echo "<p>✅ تم إضافة الفئات التجريبية</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إضافة الفئات: " . $e->getMessage() . "</p>";
}

// جلب معرفات الفئات
$category_ids = [];
$categories_result = $pdo->query("SELECT id, name FROM product_categories");
while ($row = $categories_result->fetch()) {
    $category_ids[$row['name']] = $row['id'];
}

// إضافة منتجات تجريبية
try {
    $products = [
        // الإلكترونيات
        [
            'name' => 'هاتف ذكي سامسونج جالاكسي',
            'description' => 'هاتف ذكي متطور بكاميرا عالية الدقة وشاشة AMOLED',
            'short_description' => 'هاتف ذكي بمواصفات عالية',
            'sku' => 'PHONE-001',
            'price' => 2500.00,
            'sale_price' => 2200.00,
            'stock_quantity' => 50,
            'category_id' => $category_ids['الإلكترونيات'] ?? null,
            'brand' => 'Samsung',
            'featured' => 1
        ],
        [
            'name' => 'لابتوب ديل انسبايرون',
            'description' => 'لابتوب عالي الأداء مناسب للعمل والدراسة',
            'short_description' => 'لابتوب بمعالج قوي وذاكرة كبيرة',
            'sku' => 'LAPTOP-001',
            'price' => 3500.00,
            'stock_quantity' => 25,
            'category_id' => $category_ids['الإلكترونيات'] ?? null,
            'brand' => 'Dell',
            'featured' => 1
        ],
        [
            'name' => 'سماعات بلوتوث لاسلكية',
            'description' => 'سماعات عالية الجودة مع إلغاء الضوضاء',
            'short_description' => 'سماعات لاسلكية بجودة صوت ممتازة',
            'sku' => 'HEADPHONES-001',
            'price' => 450.00,
            'sale_price' => 380.00,
            'stock_quantity' => 100,
            'category_id' => $category_ids['الإلكترونيات'] ?? null,
            'brand' => 'Sony',
            'featured' => 0
        ],
        
        // الملابس
        [
            'name' => 'قميص قطني رجالي',
            'description' => 'قميص قطني عالي الجودة مناسب للمناسبات الرسمية',
            'short_description' => 'قميص قطني أنيق ومريح',
            'sku' => 'SHIRT-001',
            'price' => 120.00,
            'sale_price' => 95.00,
            'stock_quantity' => 200,
            'category_id' => $category_ids['الملابس'] ?? null,
            'brand' => 'Polo',
            'featured' => 1
        ],
        [
            'name' => 'فستان نسائي أنيق',
            'description' => 'فستان عصري مناسب للمناسبات الخاصة',
            'short_description' => 'فستان أنيق بتصميم عصري',
            'sku' => 'DRESS-001',
            'price' => 280.00,
            'stock_quantity' => 75,
            'category_id' => $category_ids['الملابس'] ?? null,
            'brand' => 'Zara',
            'featured' => 0
        ],
        
        // الكتب
        [
            'name' => 'كتاب البرمجة الحديثة',
            'description' => 'دليل شامل لتعلم البرمجة بلغات حديثة',
            'short_description' => 'كتاب تعليمي في البرمجة',
            'sku' => 'BOOK-001',
            'price' => 85.00,
            'stock_quantity' => 150,
            'category_id' => $category_ids['الكتب'] ?? null,
            'brand' => 'دار النشر العربية',
            'featured' => 1
        ],
        [
            'name' => 'رواية الأسود يليق بك',
            'description' => 'رواية رومانسية مشوقة من أفضل الكتاب',
            'short_description' => 'رواية رومانسية مميزة',
            'sku' => 'BOOK-002',
            'price' => 45.00,
            'sale_price' => 35.00,
            'stock_quantity' => 300,
            'category_id' => $category_ids['الكتب'] ?? null,
            'brand' => 'دار الآداب',
            'featured' => 0
        ],
        
        // المنزل والحديقة
        [
            'name' => 'مجموعة أواني طبخ',
            'description' => 'مجموعة أواني طبخ عالية الجودة مقاومة للالتصاق',
            'short_description' => 'أواني طبخ متكاملة وعملية',
            'sku' => 'COOKWARE-001',
            'price' => 650.00,
            'sale_price' => 520.00,
            'stock_quantity' => 40,
            'category_id' => $category_ids['المنزل والحديقة'] ?? null,
            'brand' => 'Tefal',
            'featured' => 1
        ],
        
        // الرياضة
        [
            'name' => 'حذاء رياضي للجري',
            'description' => 'حذاء رياضي مريح ومناسب للجري والتمارين',
            'short_description' => 'حذاء رياضي عالي الجودة',
            'sku' => 'SHOES-001',
            'price' => 320.00,
            'stock_quantity' => 80,
            'category_id' => $category_ids['الرياضة'] ?? null,
            'brand' => 'Nike',
            'featured' => 0
        ],
        
        // الجمال والعناية
        [
            'name' => 'كريم مرطب للوجه',
            'description' => 'كريم مرطب طبيعي مناسب لجميع أنواع البشرة',
            'short_description' => 'كريم مرطب طبيعي وفعال',
            'sku' => 'CREAM-001',
            'price' => 95.00,
            'sale_price' => 75.00,
            'stock_quantity' => 120,
            'category_id' => $category_ids['الجمال والعناية'] ?? null,
            'brand' => 'L\'Oreal',
            'featured' => 1
        ]
    ];
    
    foreach ($products as $product) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO products (name, description, short_description, sku, price, sale_price, 
                                       stock_quantity, category_id, brand, status, featured, tax_rate)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, 15.00)
        ");
        
        $stmt->execute([
            $product['name'],
            $product['description'],
            $product['short_description'],
            $product['sku'],
            $product['price'],
            $product['sale_price'] ?? null,
            $product['stock_quantity'],
            $product['category_id'],
            $product['brand'],
            $product['featured']
        ]);
    }
    
    echo "<p>✅ تم إضافة المنتجات التجريبية</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إضافة المنتجات: " . $e->getMessage() . "</p>";
}

// إضافة كوبونات تجريبية
try {
    $coupons = [
        [
            'code' => 'WELCOME20',
            'name' => 'خصم ترحيبي 20%',
            'description' => 'خصم 20% للعملاء الجدد',
            'type' => 'percentage',
            'value' => 20.00,
            'min_order_amount' => 100.00,
            'max_discount_amount' => 200.00,
            'usage_limit' => 100,
            'usage_limit_per_customer' => 1,
            'valid_until' => date('Y-m-d H:i:s', strtotime('+30 days'))
        ],
        [
            'code' => 'SAVE50',
            'name' => 'خصم 50 ريال',
            'description' => 'خصم 50 ريال على الطلبات أكثر من 300 ريال',
            'type' => 'fixed_amount',
            'value' => 50.00,
            'min_order_amount' => 300.00,
            'usage_limit' => 50,
            'usage_limit_per_customer' => 2,
            'valid_until' => date('Y-m-d H:i:s', strtotime('+60 days'))
        ],
        [
            'code' => 'FREESHIP',
            'name' => 'شحن مجاني',
            'description' => 'شحن مجاني على جميع الطلبات',
            'type' => 'free_shipping',
            'value' => 0.00,
            'min_order_amount' => 150.00,
            'usage_limit' => 200,
            'usage_limit_per_customer' => 3,
            'valid_until' => date('Y-m-d H:i:s', strtotime('+90 days'))
        ]
    ];
    
    foreach ($coupons as $coupon) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO discount_coupons (code, name, description, type, value, min_order_amount, 
                                                max_discount_amount, usage_limit, usage_limit_per_customer, 
                                                valid_until, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
        ");
        
        $stmt->execute([
            $coupon['code'],
            $coupon['name'],
            $coupon['description'],
            $coupon['type'],
            $coupon['value'],
            $coupon['min_order_amount'],
            $coupon['max_discount_amount'] ?? null,
            $coupon['usage_limit'],
            $coupon['usage_limit_per_customer'],
            $coupon['valid_until']
        ]);
    }
    
    echo "<p>✅ تم إضافة الكوبونات التجريبية</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إضافة الكوبونات: " . $e->getMessage() . "</p>";
}

// عرض الإحصائيات النهائية
echo "<hr>";
echo "<h3>📊 إحصائيات المتجر بعد إضافة البيانات:</h3>";

try {
    $stats = [
        'categories' => $pdo->query("SELECT COUNT(*) FROM product_categories")->fetchColumn(),
        'products' => $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn(),
        'featured_products' => $pdo->query("SELECT COUNT(*) FROM products WHERE featured = 1")->fetchColumn(),
        'coupons' => $pdo->query("SELECT COUNT(*) FROM discount_coupons")->fetchColumn(),
        'payment_gateways' => $pdo->query("SELECT COUNT(*) FROM payment_gateways")->fetchColumn()
    ];
    
    echo "<p>✅ <strong>الفئات:</strong> {$stats['categories']} فئة</p>";
    echo "<p>✅ <strong>المنتجات:</strong> {$stats['products']} منتج</p>";
    echo "<p>✅ <strong>المنتجات المميزة:</strong> {$stats['featured_products']} منتج</p>";
    echo "<p>✅ <strong>كوبونات الخصم:</strong> {$stats['coupons']} كوبون</p>";
    echo "<p>✅ <strong>بوابات الدفع:</strong> {$stats['payment_gateways']} بوابة</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في جلب الإحصائيات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🎉 تم إضافة البيانات التجريبية بنجاح!</h3>";
echo "<p>يمكنك الآن:</p>";
echo "<ul>";
echo "<li>✅ تصفح المتجر ومشاهدة المنتجات</li>";
echo "<li>✅ إضافة المنتجات للسلة</li>";
echo "<li>✅ اختبار كوبونات الخصم</li>";
echo "<li>✅ تجربة بوابات الدفع المختلفة</li>";
echo "<li>✅ إدارة المتجر من لوحة الإدارة</li>";
echo "</ul>";
echo "<p><a href='/store.php' class='btn btn-success'>عرض المتجر</a> ";
echo "<a href='/store-admin.php' class='btn btn-primary'>إدارة المتجر</a></p>";
echo "</div>";

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
ul { margin: 10px 0; padding-left: 20px; }
.btn { display: inline-block; padding: 8px 16px; margin: 5px; background: #007bff; color: white; border-radius: 5px; text-decoration: none; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
</style>";
?>
