<?php
/**
 * ملف التثبيت التلقائي لنظام إدارة المؤسسات غير الربحية
 */

// التحقق من متطلبات النظام
function checkRequirements() {
    $requirements = [
        'PHP Version >= 8.0' => version_compare(PHP_VERSION, '8.0.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'JSON Extension' => extension_loaded('json'),
        'OpenSSL Extension' => extension_loaded('openssl'),
        'Mbstring Extension' => extension_loaded('mbstring'),
        'Fileinfo Extension' => extension_loaded('fileinfo'),
    ];

    $allMet = true;
    echo "<h2>فحص متطلبات النظام:</h2>\n";
    echo "<ul>\n";

    foreach ($requirements as $requirement => $met) {
        $status = $met ? '✅ متوفر' : '❌ غير متوفر';
        echo "<li>{$requirement}: {$status}</li>\n";
        if (!$met) $allMet = false;
    }

    echo "</ul>\n";

    if (!$allMet) {
        echo "<p style='color: red;'>يرجى تثبيت المتطلبات المفقودة قبل المتابعة.</p>\n";
        exit;
    }

    echo "<p style='color: green;'>جميع المتطلبات متوفرة!</p>\n";
}

// إنشاء قاعدة البيانات والجداول
function setupDatabase($config) {
    try {
        // اختبار الاتصال أولاً
        echo "<p>🔄 جاري اختبار الاتصال بـ MySQL...</p>\n";
        flush();

        // الاتصال بـ MySQL بدون تحديد قاعدة البيانات
        $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
        echo "<p>📡 محاولة الاتصال بـ: {$dsn}</p>\n";
        flush();

        $pdo = new PDO(
            $dsn,
            $config['username'],
            $config['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 10
            ]
        );

        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p>✅ تم إنشاء قاعدة البيانات: {$config['database']}</p>\n";

        // الاتصال بقاعدة البيانات المحددة
        echo "<p>🔄 جاري الاتصال بقاعدة البيانات...</p>\n";
        flush();

        $pdo = new PDO(
            "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4",
            $config['username'],
            $config['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 10
            ]
        );

        echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح!</p>\n";
        flush();

        // تشغيل ملف إنشاء الجداول
        $migrationFile = __DIR__ . '/database/migrations/001_create_initial_tables.sql';
        if (file_exists($migrationFile)) {
            $sql = file_get_contents($migrationFile);
            $statements = explode(';', $sql);

            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
            echo "<p>✅ تم إنشاء الجداول الأساسية</p>\n";
        }

        // تشغيل ملف البيانات الأولية
        $seedFile = __DIR__ . '/database/seeds/001_initial_data.sql';
        if (file_exists($seedFile)) {
            $sql = file_get_contents($seedFile);
            $statements = explode(';', $sql);

            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
            echo "<p>✅ تم إدراج البيانات الأولية</p>\n";
        }

        return true;

    } catch (PDOException $e) {
        $errorCode = $e->getCode();
        $errorMessage = $e->getMessage();

        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<h4>❌ خطأ في الاتصال بقاعدة البيانات</h4>";

        if (strpos($errorMessage, 'Connection refused') !== false || $errorCode == 2002) {
            echo "<p><strong>المشكلة:</strong> لا يمكن الاتصال بخادم MySQL</p>";
            echo "<p><strong>الحلول المقترحة:</strong></p>";
            echo "<ul>";
            echo "<li>تأكد من تشغيل خادم MySQL في WAMP/XAMPP</li>";
            echo "<li>تحقق من أن المنفذ 3306 متاح</li>";
            echo "<li>جرب استخدام 127.0.0.1 بدلاً من localhost</li>";
            echo "<li>تأكد من تشغيل خدمة wampmysqld64 في WAMP</li>";
            echo "</ul>";
        } elseif (strpos($errorMessage, 'Access denied') !== false || $errorCode == 1045) {
            echo "<p><strong>المشكلة:</strong> خطأ في اسم المستخدم أو كلمة المرور</p>";
            echo "<p><strong>الحلول المقترحة:</strong></p>";
            echo "<ul>";
            echo "<li>تأكد من صحة اسم المستخدم (عادة 'root' في WAMP)</li>";
            echo "<li>تأكد من كلمة المرور (عادة فارغة في WAMP)</li>";
            echo "<li>تحقق من إعدادات MySQL في WAMP</li>";
            echo "</ul>";
        } else {
            echo "<p><strong>رسالة الخطأ:</strong> {$errorMessage}</p>";
            echo "<p><strong>رمز الخطأ:</strong> {$errorCode}</p>";
        }

        echo "<p><strong>خطوات استكشاف الأخطاء:</strong></p>";
        echo "<ol>";
        echo "<li>افتح WAMP وتأكد من أن الأيقونة خضراء</li>";
        echo "<li>اضغط على أيقونة WAMP → MySQL → Service → Start/Resume Service</li>";
        echo "<li>جرب الاتصال عبر phpMyAdmin</li>";
        echo "<li>تحقق من ملف my.ini في إعدادات MySQL</li>";
        echo "</ol>";

        echo "</div>";

        return false;
    }
}

// إنشاء ملف .env
function createEnvFile($config) {
    $envContent = "APP_NAME=\"نظام إدارة المؤسسات غير الربحية\"
APP_ENV=production
APP_DEBUG=false
APP_URL={$config['app_url']}
APP_KEY=" . base64_encode(random_bytes(32)) . "
APP_TIMEZONE=Asia/Riyadh
APP_LOCALE=ar

DB_CONNECTION=mysql
DB_HOST={$config['host']}
DB_PORT={$config['port']}
DB_DATABASE={$config['database']}
DB_USERNAME={$config['username']}
DB_PASSWORD={$config['password']}

SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_SECURE_COOKIE=false

CACHE_DRIVER=file

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=\"<EMAIL>\"
MAIL_FROM_NAME=\"نظام إدارة المؤسسات\"

UPLOAD_MAX_SIZE=10485760

JWT_SECRET=" . bin2hex(random_bytes(32)) . "
JWT_TTL=3600

PASSWORD_MIN_LENGTH=8
LOGIN_ATTEMPTS=5
LOGIN_LOCKOUT_DURATION=900";

    if (file_put_contents(__DIR__ . '/.env', $envContent)) {
        echo "<p>✅ تم إنشاء ملف .env</p>\n";
        return true;
    } else {
        echo "<p style='color: red;'>فشل في إنشاء ملف .env</p>\n";
        return false;
    }
}

// إعداد الصلاحيات
function setupPermissions() {
    $directories = [
        'storage/logs',
        'storage/cache',
        'storage/sessions',
        'public/uploads'
    ];

    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        chmod($dir, 0755);
    }

    echo "<p>✅ تم إعداد صلاحيات المجلدات</p>\n";
}

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $config = [
        'host' => $_POST['db_host'] ?? 'localhost',
        'port' => $_POST['db_port'] ?? '3306',
        'database' => $_POST['db_database'] ?? 'nonprofit_cms',
        'username' => $_POST['db_username'] ?? 'root',
        'password' => $_POST['db_password'] ?? '',
        'app_url' => $_POST['app_url'] ?? 'http://localhost:8000'
    ];

    echo "<h1>جاري تثبيت النظام...</h1>\n";

    checkRequirements();

    if (setupDatabase($config)) {
        createEnvFile($config);
        setupPermissions();

        echo "<h2 style='color: green;'>🎉 تم تثبيت النظام بنجاح!</h2>\n";
        echo "<p><strong>بيانات تسجيل الدخول الافتراضية:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>البريد الإلكتروني: <EMAIL></li>\n";
        echo "<li>كلمة المرور: password</li>\n";
        echo "</ul>\n";
        echo "<p><a href='/auth/login' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الانتقال إلى تسجيل الدخول</a></p>\n";
        echo "<p style='color: orange;'><strong>تنبيه:</strong> يرجى حذف ملف install.php بعد التثبيت لأسباب أمنية.</p>\n";

        exit;
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة المؤسسات غير الربحية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .install-container { max-width: 600px; margin: 50px auto; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class="container install-container">
        <div class="card">
            <div class="card-header text-center bg-primary text-white">
                <h3>🏢 تثبيت نظام إدارة المؤسسات غير الربحية</h3>
                <p class="mb-0">مرحباً بك في معالج التثبيت</p>
            </div>
            <div class="card-body p-4">
                <?php checkRequirements(); ?>

                <form method="POST" class="mt-4">
                    <h5>إعدادات قاعدة البيانات:</h5>

                    <div class="mb-3">
                        <label class="form-label">خادم قاعدة البيانات:</label>
                        <input type="text" name="db_host" class="form-control" value="localhost" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">منفذ قاعدة البيانات:</label>
                        <input type="text" name="db_port" class="form-control" value="3306" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم قاعدة البيانات:</label>
                        <input type="text" name="db_database" class="form-control" value="nonprofit_cms" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم:</label>
                        <input type="text" name="db_username" class="form-control" value="root" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">كلمة المرور:</label>
                        <input type="password" name="db_password" class="form-control">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">رابط الموقع:</label>
                        <input type="url" name="app_url" class="form-control" value="http://localhost:8000" required>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            🚀 بدء التثبيت
                        </button>
                        <a href="/mysql-check.php" class="btn btn-outline-info">
                            🔍 فحص حالة MySQL
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
