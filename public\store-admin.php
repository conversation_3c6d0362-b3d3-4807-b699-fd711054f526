<?php
session_start();

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];

    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: /login.php');
    exit;
}

$success = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'add_category') {
        try {
            $required_fields = ['name'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("اسم الفئة مطلوب");
                }
            }

            $stmt = $pdo->prepare("
                INSERT INTO product_categories (name, description, parent_id, sort_order, is_active)
                VALUES (?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $_POST['name'],
                $_POST['description'] ?? '',
                !empty($_POST['parent_id']) ? $_POST['parent_id'] : null,
                $_POST['sort_order'] ?? 0,
                isset($_POST['is_active']) ? 1 : 0
            ]);

            $success = "تم إضافة الفئة بنجاح";

        } catch (Exception $e) {
            $error = "خطأ في إضافة الفئة: " . $e->getMessage();
        }
    }

    elseif ($action === 'add_product') {
        try {
            $required_fields = ['name', 'price'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("اسم المنتج والسعر مطلوبان");
                }
            }

            // إنشاء SKU تلقائي إذا لم يتم توفيره
            $sku = $_POST['sku'] ?? 'PRD-' . time() . '-' . rand(100, 999);

            $stmt = $pdo->prepare("
                INSERT INTO products (name, description, short_description, sku, price, sale_price,
                                    stock_quantity, category_id, brand, status, featured, digital, tax_rate)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $_POST['name'],
                $_POST['description'] ?? '',
                $_POST['short_description'] ?? '',
                $sku,
                $_POST['price'],
                !empty($_POST['sale_price']) ? $_POST['sale_price'] : null,
                $_POST['stock_quantity'] ?? 0,
                !empty($_POST['category_id']) ? $_POST['category_id'] : null,
                $_POST['brand'] ?? '',
                $_POST['status'] ?? 'active',
                isset($_POST['featured']) ? 1 : 0,
                isset($_POST['digital']) ? 1 : 0,
                $_POST['tax_rate'] ?? 15.00
            ]);

            $success = "تم إضافة المنتج بنجاح";

        } catch (Exception $e) {
            $error = "خطأ في إضافة المنتج: " . $e->getMessage();
        }
    }

    elseif ($action === 'add_coupon') {
        try {
            $required_fields = ['code', 'name', 'type', 'value'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("جميع الحقول المطلوبة يجب ملؤها");
                }
            }

            $stmt = $pdo->prepare("
                INSERT INTO discount_coupons (code, name, description, type, value, min_order_amount,
                                            max_discount_amount, usage_limit, usage_limit_per_customer,
                                            valid_from, valid_until, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                strtoupper($_POST['code']),
                $_POST['name'],
                $_POST['description'] ?? '',
                $_POST['type'],
                $_POST['value'],
                $_POST['min_order_amount'] ?? 0,
                !empty($_POST['max_discount_amount']) ? $_POST['max_discount_amount'] : null,
                !empty($_POST['usage_limit']) ? $_POST['usage_limit'] : null,
                $_POST['usage_limit_per_customer'] ?? 1,
                !empty($_POST['valid_from']) ? $_POST['valid_from'] : null,
                !empty($_POST['valid_until']) ? $_POST['valid_until'] : null,
                isset($_POST['is_active']) ? 1 : 0
            ]);

            $success = "تم إضافة الكوبون بنجاح";

        } catch (Exception $e) {
            $error = "خطأ في إضافة الكوبون: " . $e->getMessage();
        }
    }
}

// جلب الإحصائيات
$stats = [
    'total_products' => $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn(),
    'active_products' => $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'")->fetchColumn(),
    'total_categories' => $pdo->query("SELECT COUNT(*) FROM product_categories")->fetchColumn(),
    'total_orders' => $pdo->query("SELECT COUNT(*) FROM orders")->fetchColumn(),
    'pending_orders' => $pdo->query("SELECT COUNT(*) FROM orders WHERE status = 'pending'")->fetchColumn(),
    'total_customers' => $pdo->query("SELECT COUNT(*) FROM customers")->fetchColumn(),
    'total_revenue' => $pdo->query("SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE payment_status = 'paid'")->fetchColumn(),
    'active_coupons' => $pdo->query("SELECT COUNT(*) FROM discount_coupons WHERE is_active = 1")->fetchColumn()
];

// جلب الفئات للنماذج
$categories = $pdo->query("SELECT * FROM product_categories ORDER BY name")->fetchAll();

// جلب المنتجات الحديثة
$recent_products = $pdo->query("
    SELECT p.*, c.name as category_name
    FROM products p
    LEFT JOIN product_categories c ON p.category_id = c.id
    ORDER BY p.created_at DESC
    LIMIT 10
")->fetchAll();

// جلب الطلبات الحديثة
$recent_orders = $pdo->query("
    SELECT o.*, c.first_name, c.last_name, c.email
    FROM orders o
    JOIN customers c ON o.customer_id = c.id
    ORDER BY o.created_at DESC
    LIMIT 10
")->fetchAll();

// جلب بوابات الدفع
$payment_gateways = $pdo->query("SELECT * FROM payment_gateways ORDER BY sort_order")->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المتجر الإلكتروني - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #e91e63, #9c27b0); }
        .stats-card { background: linear-gradient(45deg, #e91e63, #9c27b0); color: white; border-radius: 15px; }
        .admin-card { border-radius: 10px; transition: transform 0.2s; }
        .admin-card:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .gateway-card { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 10px 0; }
        .gateway-active { border-color: #28a745; background-color: #f8fff9; }
        .gateway-inactive { border-color: #dc3545; background-color: #fff8f8; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-store me-2"></i>إدارة المتجر الإلكتروني
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="/store.php" target="_blank">
                    <i class="fas fa-external-link-alt me-1"></i>عرض المتجر
                </a>
                <a class="nav-link" href="/users.php">
                    <i class="fas fa-users me-1"></i>المستخدمين
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-box fa-2x mb-2"></i>
                    <h4><?= $stats['total_products'] ?></h4>
                    <small>إجمالي المنتجات</small>
                    <div class="mt-1">
                        <small>(<?= $stats['active_products'] ?> نشط)</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                    <h4><?= $stats['total_orders'] ?></h4>
                    <small>إجمالي الطلبات</small>
                    <div class="mt-1">
                        <small>(<?= $stats['pending_orders'] ?> معلق)</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4><?= $stats['total_customers'] ?></h4>
                    <small>إجمالي العملاء</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                    <h4><?= number_format($stats['total_revenue'], 2) ?> ر.س</h4>
                    <small>إجمالي المبيعات</small>
                </div>
            </div>
        </div>

        <!-- التبويبات -->
        <ul class="nav nav-tabs" id="adminTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button">
                    <i class="fas fa-chart-line me-2"></i>نظرة عامة
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button">
                    <i class="fas fa-box me-2"></i>المنتجات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button">
                    <i class="fas fa-tags me-2"></i>الفئات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders" type="button">
                    <i class="fas fa-shopping-cart me-2"></i>الطلبات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="coupons-tab" data-bs-toggle="tab" data-bs-target="#coupons" type="button">
                    <i class="fas fa-ticket-alt me-2"></i>الكوبونات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments" type="button">
                    <i class="fas fa-credit-card me-2"></i>بوابات الدفع
                </button>
            </li>
        </ul>

        <div class="tab-content" id="adminTabsContent">
            <!-- تبويب النظرة العامة -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel">
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card admin-card">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-box me-2"></i>المنتجات الحديثة</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_products)): ?>
                                <p class="text-muted">لا توجد منتجات</p>
                                <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الفئة</th>
                                                <th>السعر</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_slice($recent_products, 0, 5) as $product): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($product['name']) ?></td>
                                                <td><?= htmlspecialchars($product['category_name'] ?? 'غير محدد') ?></td>
                                                <td><?= number_format($product['price'], 2) ?> ر.س</td>
                                                <td>
                                                    <span class="badge bg-<?= $product['status'] === 'active' ? 'success' : 'secondary' ?>">
                                                        <?= $product['status'] === 'active' ? 'نشط' : 'غير نشط' ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card admin-card">
                            <div class="card-header bg-success text-white">
                                <h5><i class="fas fa-shopping-cart me-2"></i>الطلبات الحديثة</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_orders)): ?>
                                <p class="text-muted">لا توجد طلبات</p>
                                <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>رقم الطلب</th>
                                                <th>العميل</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_slice($recent_orders, 0, 5) as $order): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($order['order_number']) ?></td>
                                                <td><?= htmlspecialchars($order['first_name'] . ' ' . $order['last_name']) ?></td>
                                                <td><?= number_format($order['total_amount'], 2) ?> ر.س</td>
                                                <td>
                                                    <span class="badge bg-<?=
                                                        $order['status'] === 'delivered' ? 'success' :
                                                        ($order['status'] === 'pending' ? 'warning' : 'info')
                                                    ?>">
                                                        <?php
                                                        $status_labels = [
                                                            'pending' => 'معلق',
                                                            'processing' => 'قيد المعالجة',
                                                            'shipped' => 'تم الشحن',
                                                            'delivered' => 'تم التسليم',
                                                            'cancelled' => 'ملغي'
                                                        ];
                                                        echo $status_labels[$order['status']] ?? $order['status'];
                                                        ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب المنتجات -->
            <div class="tab-pane fade" id="products" role="tabpanel">
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="card admin-card">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-plus me-2"></i>إضافة منتج جديد</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="add_product">

                                    <div class="mb-3">
                                        <label class="form-label">اسم المنتج *</label>
                                        <input type="text" name="name" class="form-control" required>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">الوصف المختصر</label>
                                        <textarea name="short_description" class="form-control" rows="2"></textarea>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">السعر *</label>
                                                <input type="number" name="price" class="form-control" step="0.01" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">سعر التخفيض</label>
                                                <input type="number" name="sale_price" class="form-control" step="0.01">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">الكمية</label>
                                                <input type="number" name="stock_quantity" class="form-control" value="0">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">رمز المنتج</label>
                                                <input type="text" name="sku" class="form-control" placeholder="تلقائي">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">الفئة</label>
                                        <select name="category_id" class="form-select">
                                            <option value="">اختر الفئة</option>
                                            <?php foreach ($categories as $cat): ?>
                                            <option value="<?= $cat['id'] ?>"><?= htmlspecialchars($cat['name']) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">العلامة التجارية</label>
                                        <input type="text" name="brand" class="form-control">
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" name="featured" class="form-check-input" id="featured">
                                            <label class="form-check-label" for="featured">منتج مميز</label>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-plus me-2"></i>إضافة المنتج
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8">
                        <div class="card admin-card">
                            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-list me-2"></i>قائمة المنتجات</h5>
                                <span class="badge bg-light text-dark"><?= $stats['total_products'] ?> منتج</span>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_products)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد منتجات</p>
                                    <p class="small text-muted">ابدأ بإضافة منتجك الأول من النموذج المجاور</p>
                                </div>
                                <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الفئة</th>
                                                <th>السعر</th>
                                                <th>المخزون</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_products as $product): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($product['name']) ?></strong><br>
                                                    <small class="text-muted"><?= htmlspecialchars($product['sku']) ?></small>
                                                </td>
                                                <td><?= htmlspecialchars($product['category_name'] ?? 'غير محدد') ?></td>
                                                <td>
                                                    <?php if ($product['sale_price']): ?>
                                                    <span class="text-decoration-line-through text-muted"><?= number_format($product['price'], 2) ?></span><br>
                                                    <span class="text-danger fw-bold"><?= number_format($product['sale_price'], 2) ?> ر.س</span>
                                                    <?php else: ?>
                                                    <?= number_format($product['price'], 2) ?> ر.س
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?= $product['stock_quantity'] > 0 ? 'success' : 'danger' ?>">
                                                        <?= $product['stock_quantity'] ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?= $product['status'] === 'active' ? 'success' : 'secondary' ?>">
                                                        <?= $product['status'] === 'active' ? 'نشط' : 'غير نشط' ?>
                                                    </span>
                                                    <?php if ($product['featured']): ?>
                                                    <br><span class="badge bg-warning mt-1">مميز</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب الفئات -->
            <div class="tab-pane fade" id="categories" role="tabpanel">
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="card admin-card">
                            <div class="card-header bg-success text-white">
                                <h5><i class="fas fa-plus me-2"></i>إضافة فئة جديدة</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="add_category">

                                    <div class="mb-3">
                                        <label class="form-label">اسم الفئة *</label>
                                        <input type="text" name="name" class="form-control" required>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">الوصف</label>
                                        <textarea name="description" class="form-control" rows="3"></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">الفئة الأب</label>
                                        <select name="parent_id" class="form-select">
                                            <option value="">فئة رئيسية</option>
                                            <?php foreach ($categories as $cat): ?>
                                            <option value="<?= $cat['id'] ?>"><?= htmlspecialchars($cat['name']) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">ترتيب العرض</label>
                                        <input type="number" name="sort_order" class="form-control" value="0">
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" name="is_active" class="form-check-input" id="cat_active" checked>
                                            <label class="form-check-label" for="cat_active">فئة نشطة</label>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-plus me-2"></i>إضافة الفئة
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8">
                        <div class="card admin-card">
                            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-tags me-2"></i>قائمة الفئات</h5>
                                <span class="badge bg-light text-dark"><?= $stats['total_categories'] ?> فئة</span>
                            </div>
                            <div class="card-body">
                                <?php if (empty($categories)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد فئات</p>
                                    <p class="small text-muted">ابدأ بإضافة فئة لتنظيم منتجاتك</p>
                                </div>
                                <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>الفئة</th>
                                                <th>الوصف</th>
                                                <th>الفئة الأب</th>
                                                <th>الترتيب</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($categories as $category): ?>
                                            <tr>
                                                <td><strong><?= htmlspecialchars($category['name']) ?></strong></td>
                                                <td><?= htmlspecialchars(substr($category['description'] ?? '', 0, 50)) ?></td>
                                                <td>
                                                    <?php if ($category['parent_id']): ?>
                                                    <?php
                                                    $parent = array_filter($categories, function($c) use ($category) {
                                                        return $c['id'] == $category['parent_id'];
                                                    });
                                                    $parent = reset($parent);
                                                    echo htmlspecialchars($parent['name'] ?? 'غير موجود');
                                                    ?>
                                                    <?php else: ?>
                                                    <span class="text-muted">فئة رئيسية</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= $category['sort_order'] ?></td>
                                                <td>
                                                    <span class="badge bg-<?= $category['is_active'] ? 'success' : 'secondary' ?>">
                                                        <?= $category['is_active'] ? 'نشط' : 'غير نشط' ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب الطلبات -->
            <div class="tab-pane fade" id="orders" role="tabpanel">
                <div class="card mt-3 admin-card">
                    <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-shopping-cart me-2"></i>إدارة الطلبات</h5>
                        <span class="badge bg-dark"><?= $stats['total_orders'] ?> طلب</span>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_orders)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد طلبات</p>
                            <p class="small text-muted">ستظهر الطلبات هنا عندما يبدأ العملاء بالشراء</p>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>حالة الطلب</th>
                                        <th>حالة الدفع</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_orders as $order): ?>
                                    <tr>
                                        <td><strong><?= htmlspecialchars($order['order_number']) ?></strong></td>
                                        <td>
                                            <?= htmlspecialchars($order['first_name'] . ' ' . $order['last_name']) ?><br>
                                            <small class="text-muted"><?= htmlspecialchars($order['email']) ?></small>
                                        </td>
                                        <td><?= number_format($order['total_amount'], 2) ?> ر.س</td>
                                        <td>
                                            <span class="badge bg-<?=
                                                $order['status'] === 'delivered' ? 'success' :
                                                ($order['status'] === 'pending' ? 'warning' :
                                                ($order['status'] === 'cancelled' ? 'danger' : 'info'))
                                            ?>">
                                                <?php
                                                $status_labels = [
                                                    'pending' => 'معلق',
                                                    'processing' => 'قيد المعالجة',
                                                    'shipped' => 'تم الشحن',
                                                    'delivered' => 'تم التسليم',
                                                    'cancelled' => 'ملغي',
                                                    'refunded' => 'مسترد'
                                                ];
                                                echo $status_labels[$order['status']] ?? $order['status'];
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?=
                                                $order['payment_status'] === 'paid' ? 'success' :
                                                ($order['payment_status'] === 'pending' ? 'warning' : 'danger')
                                            ?>">
                                                <?php
                                                $payment_labels = [
                                                    'pending' => 'معلق',
                                                    'paid' => 'مدفوع',
                                                    'failed' => 'فشل',
                                                    'refunded' => 'مسترد'
                                                ];
                                                echo $payment_labels[$order['payment_status']] ?? $order['payment_status'];
                                                ?>
                                            </span>
                                        </td>
                                        <td><?= date('Y/m/d', strtotime($order['created_at'])) ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewOrder(<?= $order['id'] ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" onclick="updateOrderStatus(<?= $order['id'] ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- تبويب الكوبونات -->
            <div class="tab-pane fade" id="coupons" role="tabpanel">
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="card admin-card">
                            <div class="card-header bg-warning text-dark">
                                <h5><i class="fas fa-plus me-2"></i>إضافة كوبون جديد</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="add_coupon">

                                    <div class="mb-3">
                                        <label class="form-label">كود الكوبون *</label>
                                        <input type="text" name="code" class="form-control" required placeholder="SAVE20" style="text-transform: uppercase;">
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">اسم الكوبون *</label>
                                        <input type="text" name="name" class="form-control" required placeholder="خصم 20%">
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">الوصف</label>
                                        <textarea name="description" class="form-control" rows="2" placeholder="وصف الكوبون"></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">نوع الخصم *</label>
                                        <select name="type" class="form-select" required>
                                            <option value="">اختر النوع</option>
                                            <option value="percentage">نسبة مئوية</option>
                                            <option value="fixed_amount">مبلغ ثابت</option>
                                            <option value="free_shipping">شحن مجاني</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">قيمة الخصم *</label>
                                        <input type="number" name="value" class="form-control" step="0.01" required>
                                        <small class="text-muted">للنسبة المئوية: أدخل الرقم بدون % (مثال: 20 للحصول على 20%)</small>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">الحد الأدنى للطلب</label>
                                        <input type="number" name="min_order_amount" class="form-control" step="0.01" value="0">
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">الحد الأقصى للخصم</label>
                                        <input type="number" name="max_discount_amount" class="form-control" step="0.01" placeholder="اختياري">
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">حد الاستخدام</label>
                                                <input type="number" name="usage_limit" class="form-control" placeholder="غير محدود">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">لكل عميل</label>
                                                <input type="number" name="usage_limit_per_customer" class="form-control" value="1">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">تاريخ البداية</label>
                                                <input type="datetime-local" name="valid_from" class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">تاريخ الانتهاء</label>
                                                <input type="datetime-local" name="valid_until" class="form-control">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" name="is_active" class="form-check-input" id="coupon_active" checked>
                                            <label class="form-check-label" for="coupon_active">كوبون نشط</label>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-warning w-100">
                                        <i class="fas fa-plus me-2"></i>إضافة الكوبون
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8">
                        <div class="card admin-card">
                            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-ticket-alt me-2"></i>قائمة الكوبونات</h5>
                                <span class="badge bg-light text-dark"><?= $stats['active_coupons'] ?> نشط</span>
                            </div>
                            <div class="card-body">
                                <div class="text-center py-4">
                                    <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد كوبونات</p>
                                    <p class="small text-muted">أضف كوبونات خصم لجذب العملاء</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب بوابات الدفع -->
            <div class="tab-pane fade" id="payments" role="tabpanel">
                <div class="card mt-3 admin-card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-credit-card me-2"></i>بوابات الدفع السعودية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($payment_gateways as $gateway): ?>
                            <div class="col-md-6 mb-3">
                                <div class="gateway-card <?= $gateway['is_active'] ? 'gateway-active' : 'gateway-inactive' ?>">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <i class="fas fa-credit-card me-2"></i>
                                                <?= htmlspecialchars($gateway['name']) ?>
                                            </h6>
                                            <p class="text-muted mb-2"><?= htmlspecialchars($gateway['description']) ?></p>

                                            <div class="small">
                                                <div><strong>الرسوم:</strong> <?= $gateway['fees_percentage'] ?>%</div>
                                                <div><strong>الحد الأدنى:</strong> <?= number_format($gateway['min_amount'], 2) ?> ر.س</div>
                                                <div><strong>الحد الأقصى:</strong> <?= number_format($gateway['max_amount'], 2) ?> ر.س</div>
                                                <div><strong>العملات المدعومة:</strong>
                                                    <?php
                                                    $currencies = json_decode($gateway['supported_currencies'], true);
                                                    echo implode(', ', $currencies ?? ['SAR']);
                                                    ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="text-end">
                                            <span class="badge bg-<?= $gateway['is_active'] ? 'success' : 'danger' ?> mb-2">
                                                <?= $gateway['is_active'] ? 'نشط' : 'غير نشط' ?>
                                            </span>
                                            <?php if ($gateway['is_sandbox']): ?>
                                            <br><span class="badge bg-warning">تجريبي</span>
                                            <?php endif; ?>

                                            <div class="mt-2">
                                                <button class="btn btn-sm btn-outline-primary" onclick="configureGateway('<?= $gateway['code'] ?>')">
                                                    <i class="fas fa-cog"></i> إعداد
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                            <ul class="mb-0">
                                <li>تحتاج لإعداد كل بوابة دفع بمفاتيح API الخاصة بك</li>
                                <li>ابدأ بالوضع التجريبي قبل التفعيل الفعلي</li>
                                <li>تأكد من اختبار جميع البوابات قبل إطلاق المتجر</li>
                                <li>مدى هي الأكثر استخداماً في السعودية</li>
                                <li>تابي وتمارا مناسبان للمبيعات الكبيرة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editProduct(productId) {
            alert('سيتم إضافة تعديل المنتج قريباً');
        }

        function deleteProduct(productId) {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                // سيتم تنفيذ الحذف
                alert('سيتم إضافة حذف المنتج قريباً');
            }
        }

        function editCategory(categoryId) {
            alert('سيتم إضافة تعديل الفئة قريباً');
        }

        function deleteCategory(categoryId) {
            if (confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
                alert('سيتم إضافة حذف الفئة قريباً');
            }
        }

        function viewOrder(orderId) {
            alert('سيتم إضافة عرض تفاصيل الطلب قريباً');
        }

        function updateOrderStatus(orderId) {
            alert('سيتم إضافة تحديث حالة الطلب قريباً');
        }

        function configureGateway(gatewayCode) {
            alert(`سيتم إضافة إعداد بوابة ${gatewayCode} قريباً`);
        }

        // تحويل كود الكوبون للأحرف الكبيرة
        document.querySelector('input[name="code"]').addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    </script>
</body>
</html>