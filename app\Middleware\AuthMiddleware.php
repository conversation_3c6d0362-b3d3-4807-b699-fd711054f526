<?php

namespace App\Middleware;

use Core\Auth\Auth;

class AuthMiddleware
{
    public function handle(): bool
    {
        $auth = Auth::getInstance();
        
        if (!$auth->check()) {
            // حفظ الصفحة المطلوبة للعودة إليها بعد تسجيل الدخول
            $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];
            
            // إعادة التوجيه إلى صفحة تسجيل الدخول
            if ($this->isAjaxRequest()) {
                http_response_code(401);
                header('Content-Type: application/json');
                echo json_encode(['message' => 'غير مصرح', 'redirect' => '/auth/login']);
                exit;
            } else {
                header('Location: /auth/login');
                exit;
            }
        }
        
        return true;
    }
    
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
