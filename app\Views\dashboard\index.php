<?php
$title = 'لوحة التحكم - نظام إدارة المؤسسات';
ob_start();
?>

<div class="dashboard-container">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم
                </h1>
                <p class="page-subtitle">مرحباً بك، <?= htmlspecialchars($user['name']) ?></p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card bg-primary">
                <div class="stats-content">
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-info">
                        <h3 class="stats-number"><?= number_format($stats['total_users'] ?? 0) ?></h3>
                        <p class="stats-label">إجمالي المستخدمين</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card bg-success">
                <div class="stats-content">
                    <div class="stats-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stats-info">
                        <h3 class="stats-number"><?= number_format($stats['total_organizations'] ?? 0) ?></h3>
                        <p class="stats-label">المنظمات المسجلة</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card bg-warning">
                <div class="stats-content">
                    <div class="stats-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="stats-info">
                        <h3 class="stats-number"><?= number_format($stats['active_projects'] ?? 0) ?></h3>
                        <p class="stats-label">المشاريع النشطة</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stats-card bg-info">
                <div class="stats-content">
                    <div class="stats-icon">
                        <i class="fas fa-hand-holding-heart"></i>
                    </div>
                    <div class="stats-info">
                        <h3 class="stats-number"><?= number_format($stats['total_donations'] ?? 0) ?></h3>
                        <p class="stats-label">إجمالي التبرعات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row">
        <!-- Recent Activities -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        الأنشطة الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($recentActivities)): ?>
                        <div class="activity-timeline">
                            <?php foreach (array_slice($recentActivities, 0, 10) as $activity): ?>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-<?= $this->getActivityIcon($activity['action']) ?>"></i>
                                </div>
                                <div class="activity-content">
                                    <p class="activity-text">
                                        <strong><?= htmlspecialchars($activity['user_name'] ?? 'مستخدم') ?></strong>
                                        <?= $this->getActivityDescription($activity['action']) ?>
                                        <?php if ($activity['table_name']): ?>
                                            في <span class="text-primary"><?= $this->getTableDisplayName($activity['table_name']) ?></span>
                                        <?php endif; ?>
                                    </p>
                                    <small class="activity-time text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?= $this->timeAgo($activity['created_at']) ?>
                                    </small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="/audit" class="btn btn-outline-primary">
                                عرض جميع الأنشطة
                                <i class="fas fa-arrow-left ms-1"></i>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد أنشطة حديثة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Notifications & Tasks -->
        <div class="col-lg-4">
            <!-- Notifications -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell me-2"></i>
                        الإشعارات
                        <?php if (!empty($notifications)): ?>
                        <span class="badge bg-danger ms-2"><?= count($notifications) ?></span>
                        <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($notifications)): ?>
                        <div class="notifications-list">
                            <?php foreach (array_slice($notifications, 0, 5) as $notification): ?>
                            <div class="notification-item">
                                <div class="notification-icon text-<?= $notification['type'] ?>">
                                    <i class="fas fa-<?= $this->getNotificationIcon($notification['type']) ?>"></i>
                                </div>
                                <div class="notification-content">
                                    <h6 class="notification-title"><?= htmlspecialchars($notification['title']) ?></h6>
                                    <p class="notification-text"><?= htmlspecialchars($notification['message']) ?></p>
                                    <small class="notification-time text-muted">
                                        <?= $this->timeAgo($notification['created_at']) ?>
                                    </small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="/notifications" class="btn btn-outline-primary btn-sm">
                                عرض جميع الإشعارات
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">لا توجد إشعارات جديدة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Pending Tasks -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tasks me-2"></i>
                        المهام المعلقة
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($pendingTasks)): ?>
                        <div class="tasks-list">
                            <?php foreach ($pendingTasks as $taskType => $tasks): ?>
                                <?php if (!empty($tasks)): ?>
                                <div class="task-group mb-3">
                                    <h6 class="task-group-title"><?= $this->getTaskGroupTitle($taskType) ?></h6>
                                    <?php foreach (array_slice($tasks, 0, 3) as $task): ?>
                                    <div class="task-item">
                                        <div class="task-content">
                                            <p class="task-text"><?= htmlspecialchars($task['title'] ?? $task['name']) ?></p>
                                            <small class="task-time text-muted">
                                                <?= $this->timeAgo($task['created_at']) ?>
                                            </small>
                                        </div>
                                        <div class="task-action">
                                            <a href="<?= $this->getTaskUrl($taskType, $task['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                عرض
                                            </a>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted mb-0">لا توجد مهام معلقة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <?php if ($auth->hasPermission('users.create')): ?>
                        <div class="col-md-3">
                            <a href="/users/create" class="quick-action-btn">
                                <i class="fas fa-user-plus"></i>
                                <span>إضافة مستخدم</span>
                            </a>
                        </div>
                        <?php endif; ?>

                        <?php if ($auth->hasPermission('organizations.create')): ?>
                        <div class="col-md-3">
                            <a href="/organizations/create" class="quick-action-btn">
                                <i class="fas fa-building"></i>
                                <span>تسجيل منظمة</span>
                            </a>
                        </div>
                        <?php endif; ?>

                        <?php if ($auth->hasPermission('projects.create')): ?>
                        <div class="col-md-3">
                            <a href="/projects/create" class="quick-action-btn">
                                <i class="fas fa-project-diagram"></i>
                                <span>إنشاء مشروع</span>
                            </a>
                        </div>
                        <?php endif; ?>

                        <?php if ($auth->hasPermission('reports.view')): ?>
                        <div class="col-md-3">
                            <a href="/reports" class="quick-action-btn">
                                <i class="fas fa-chart-bar"></i>
                                <span>التقارير</span>
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.dashboard-container {
    padding: 20px 0;
}

.page-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.stats-card {
    border-radius: 15px;
    padding: 1.5rem;
    color: white;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-content {
    display: flex;
    align-items: center;
}

.stats-icon {
    font-size: 3rem;
    opacity: 0.8;
    margin-left: 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.activity-timeline {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    color: #6c757d;
}

.activity-content {
    flex: 1;
}

.activity-text {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.activity-time {
    font-size: 0.875rem;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 0.875rem;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.notification-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.8rem;
}

.task-group-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.task-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.task-item:last-child {
    border-bottom: none;
}

.task-text {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.task-time {
    font-size: 0.8rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.quick-action-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.quick-action-btn span {
    font-weight: 500;
}

@media (max-width: 768px) {
    .stats-content {
        flex-direction: column;
        text-align: center;
    }
    
    .stats-icon {
        margin-left: 0;
        margin-bottom: 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .quick-action-btn {
        padding: 1.5rem 1rem;
    }
}
</style>

<?php
$content = ob_get_clean();

// Helper functions for the view
function getActivityIcon($action) {
    $icons = [
        'create' => 'plus',
        'update' => 'edit',
        'delete' => 'trash',
        'login' => 'sign-in-alt',
        'logout' => 'sign-out-alt',
    ];
    return $icons[$action] ?? 'circle';
}

function getActivityDescription($action) {
    $descriptions = [
        'create' => 'قام بإنشاء',
        'update' => 'قام بتحديث',
        'delete' => 'قام بحذف',
        'login' => 'سجل دخول',
        'logout' => 'سجل خروج',
    ];
    return $descriptions[$action] ?? 'قام بإجراء';
}

function getTableDisplayName($table) {
    $names = [
        'users' => 'المستخدمين',
        'organizations' => 'المنظمات',
        'projects' => 'المشاريع',
        'donations' => 'التبرعات',
    ];
    return $names[$table] ?? $table;
}

function getNotificationIcon($type) {
    $icons = [
        'info' => 'info-circle',
        'success' => 'check-circle',
        'warning' => 'exclamation-triangle',
        'error' => 'exclamation-circle',
    ];
    return $icons[$type] ?? 'bell';
}

function getTaskGroupTitle($taskType) {
    $titles = [
        'pending_user_approvals' => 'موافقات المستخدمين',
        'pending_organization_approvals' => 'موافقات المنظمات',
        'expiring_contracts' => 'عقود منتهية الصلاحية',
        'overdue_payments' => 'مدفوعات متأخرة',
    ];
    return $titles[$taskType] ?? 'مهام أخرى';
}

function getTaskUrl($taskType, $id) {
    $urls = [
        'pending_user_approvals' => "/users/{$id}",
        'pending_organization_approvals' => "/organizations/{$id}",
        'expiring_contracts' => "/contracts/{$id}",
        'overdue_payments' => "/finance/payments/{$id}",
    ];
    return $urls[$taskType] ?? '#';
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    
    return floor($time/31536000) . ' سنة';
}

include __DIR__ . '/../layouts/app.php';
?>
