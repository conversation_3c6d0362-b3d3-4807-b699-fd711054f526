<?php

use Core\Router\Router;

$router = new Router();

// الصفحة الرئيسية
$router->get('/', 'HomeController@index');

// مسارات المصادقة
$router->group(['prefix' => 'auth'], function($router) {
    // صفحات تسجيل الدخول
    $router->get('/login', 'AuthController@showLogin');
    $router->post('/login', 'AuthController@login');
    $router->post('/logout', 'AuthController@logout');
    
    // صفحات إعادة تعيين كلمة المرور
    $router->get('/forgot-password', 'AuthController@showForgotPassword');
    $router->post('/forgot-password', 'AuthController@forgotPassword');
    $router->get('/reset-password/{token}', 'AuthController@showResetPassword');
    $router->post('/reset-password', 'AuthController@resetPassword');
    
    // تأكيد البريد الإلكتروني
    $router->get('/verify-email/{token}', 'AuthController@verifyEmail');
});

// لوحة التحكم الرئيسية
$router->group(['prefix' => 'dashboard', 'middleware' => 'auth'], function($router) {
    $router->get('/', 'DashboardController@index');
    $router->get('/profile', 'DashboardController@profile');
    $router->post('/profile', 'DashboardController@updateProfile');
    $router->post('/change-password', 'DashboardController@changePassword');
});

// إدارة المستخدمين
$router->group(['prefix' => 'users', 'middleware' => ['auth', 'permission:users.view']], function($router) {
    $router->get('/', 'UserController@index');
    $router->get('/create', 'UserController@create')->middleware('permission:users.create');
    $router->post('/', 'UserController@store')->middleware('permission:users.create');
    $router->get('/{id}', 'UserController@show');
    $router->get('/{id}/edit', 'UserController@edit')->middleware('permission:users.edit');
    $router->post('/{id}', 'UserController@update')->middleware('permission:users.edit');
    $router->post('/{id}/delete', 'UserController@destroy')->middleware('permission:users.delete');
    $router->post('/{id}/activate', 'UserController@activate')->middleware('permission:users.activate');
    $router->post('/{id}/deactivate', 'UserController@deactivate')->middleware('permission:users.activate');
    $router->get('/search', 'UserController@search');
});

// إدارة الأدوار والصلاحيات
$router->group(['prefix' => 'roles', 'middleware' => ['auth', 'permission:roles.view']], function($router) {
    $router->get('/', 'RoleController@index');
    $router->get('/create', 'RoleController@create')->middleware('permission:roles.create');
    $router->post('/', 'RoleController@store')->middleware('permission:roles.create');
    $router->get('/{id}', 'RoleController@show');
    $router->get('/{id}/edit', 'RoleController@edit')->middleware('permission:roles.edit');
    $router->post('/{id}', 'RoleController@update')->middleware('permission:roles.edit');
    $router->post('/{id}/delete', 'RoleController@destroy')->middleware('permission:roles.delete');
    $router->get('/{id}/permissions', 'RoleController@permissions')->middleware('permission:permissions.assign');
    $router->post('/{id}/permissions', 'RoleController@updatePermissions')->middleware('permission:permissions.assign');
});

// إدارة المنظمات
$router->group(['prefix' => 'organizations', 'middleware' => ['auth', 'permission:organizations.view']], function($router) {
    $router->get('/', 'OrganizationController@index');
    $router->get('/create', 'OrganizationController@create')->middleware('permission:organizations.create');
    $router->post('/', 'OrganizationController@store')->middleware('permission:organizations.create');
    $router->get('/{id}', 'OrganizationController@show');
    $router->get('/{id}/edit', 'OrganizationController@edit')->middleware('permission:organizations.edit');
    $router->post('/{id}', 'OrganizationController@update')->middleware('permission:organizations.edit');
    $router->post('/{id}/delete', 'OrganizationController@destroy')->middleware('permission:organizations.delete');
    $router->post('/{id}/approve', 'OrganizationController@approve')->middleware('permission:organizations.approve');
    $router->get('/{id}/documents', 'OrganizationController@documents');
    $router->post('/{id}/upload-document', 'OrganizationController@uploadDocument');
});

// إدارة الموظفين
$router->group(['prefix' => 'employees', 'middleware' => ['auth', 'permission:employees.view']], function($router) {
    $router->get('/', 'EmployeeController@index');
    $router->get('/create', 'EmployeeController@create')->middleware('permission:employees.create');
    $router->post('/', 'EmployeeController@store')->middleware('permission:employees.create');
    $router->get('/{id}', 'EmployeeController@show');
    $router->get('/{id}/edit', 'EmployeeController@edit')->middleware('permission:employees.edit');
    $router->post('/{id}', 'EmployeeController@update')->middleware('permission:employees.edit');
    $router->post('/{id}/delete', 'EmployeeController@destroy')->middleware('permission:employees.delete');
    $router->get('/{id}/payroll', 'EmployeeController@payroll')->middleware('permission:employees.payroll');
    $router->post('/{id}/payroll', 'EmployeeController@updatePayroll')->middleware('permission:employees.payroll');
});

// إدارة المتطوعين
$router->group(['prefix' => 'volunteers', 'middleware' => ['auth', 'permission:volunteers.view']], function($router) {
    $router->get('/', 'VolunteerController@index');
    $router->get('/create', 'VolunteerController@create')->middleware('permission:volunteers.create');
    $router->post('/', 'VolunteerController@store')->middleware('permission:volunteers.create');
    $router->get('/{id}', 'VolunteerController@show');
    $router->get('/{id}/edit', 'VolunteerController@edit')->middleware('permission:volunteers.edit');
    $router->post('/{id}', 'VolunteerController@update')->middleware('permission:volunteers.edit');
    $router->post('/{id}/delete', 'VolunteerController@destroy')->middleware('permission:volunteers.delete');
    $router->post('/{id}/approve', 'VolunteerController@approve')->middleware('permission:volunteers.approve');
});

// إدارة المشاريع
$router->group(['prefix' => 'projects', 'middleware' => ['auth', 'permission:projects.view']], function($router) {
    $router->get('/', 'ProjectController@index');
    $router->get('/create', 'ProjectController@create')->middleware('permission:projects.create');
    $router->post('/', 'ProjectController@store')->middleware('permission:projects.create');
    $router->get('/{id}', 'ProjectController@show');
    $router->get('/{id}/edit', 'ProjectController@edit')->middleware('permission:projects.edit');
    $router->post('/{id}', 'ProjectController@update')->middleware('permission:projects.edit');
    $router->post('/{id}/delete', 'ProjectController@destroy')->middleware('permission:projects.delete');
    $router->post('/{id}/approve', 'ProjectController@approve')->middleware('permission:projects.approve');
    $router->get('/{id}/tasks', 'ProjectController@tasks');
    $router->post('/{id}/tasks', 'ProjectController@createTask');
});

// إدارة التبرعات
$router->group(['prefix' => 'donations', 'middleware' => ['auth', 'permission:donations.view']], function($router) {
    $router->get('/', 'DonationController@index');
    $router->get('/create', 'DonationController@create')->middleware('permission:donations.create');
    $router->post('/', 'DonationController@store')->middleware('permission:donations.create');
    $router->get('/{id}', 'DonationController@show');
    $router->get('/{id}/edit', 'DonationController@edit')->middleware('permission:donations.edit');
    $router->post('/{id}', 'DonationController@update')->middleware('permission:donations.edit');
    $router->post('/{id}/delete', 'DonationController@destroy')->middleware('permission:donations.delete');
    $router->post('/{id}/approve', 'DonationController@approve')->middleware('permission:donations.approve');
    $router->get('/reports', 'DonationController@reports');
});

// إدارة المخزون
$router->group(['prefix' => 'inventory', 'middleware' => ['auth', 'permission:inventory.view']], function($router) {
    $router->get('/', 'InventoryController@index');
    $router->get('/create', 'InventoryController@create')->middleware('permission:inventory.create');
    $router->post('/', 'InventoryController@store')->middleware('permission:inventory.create');
    $router->get('/{id}', 'InventoryController@show');
    $router->get('/{id}/edit', 'InventoryController@edit')->middleware('permission:inventory.edit');
    $router->post('/{id}', 'InventoryController@update')->middleware('permission:inventory.edit');
    $router->post('/{id}/delete', 'InventoryController@destroy')->middleware('permission:inventory.delete');
    $router->post('/{id}/transfer', 'InventoryController@transfer')->middleware('permission:inventory.transfer');
});

// إدارة الشؤون المالية
$router->group(['prefix' => 'finance', 'middleware' => ['auth', 'permission:finance.view']], function($router) {
    $router->get('/', 'FinanceController@index');
    $router->get('/accounts', 'FinanceController@accounts');
    $router->get('/transactions', 'FinanceController@transactions');
    $router->get('/transactions/create', 'FinanceController@createTransaction')->middleware('permission:finance.create');
    $router->post('/transactions', 'FinanceController@storeTransaction')->middleware('permission:finance.create');
    $router->get('/reports', 'FinanceController@reports')->middleware('permission:finance.reports');
    $router->get('/budget', 'FinanceController@budget');
});

// إدارة الاجتماعات
$router->group(['prefix' => 'meetings', 'middleware' => ['auth', 'permission:meetings.view']], function($router) {
    $router->get('/', 'MeetingController@index');
    $router->get('/create', 'MeetingController@create')->middleware('permission:meetings.create');
    $router->post('/', 'MeetingController@store')->middleware('permission:meetings.create');
    $router->get('/{id}', 'MeetingController@show');
    $router->get('/{id}/edit', 'MeetingController@edit')->middleware('permission:meetings.edit');
    $router->post('/{id}', 'MeetingController@update')->middleware('permission:meetings.edit');
    $router->post('/{id}/delete', 'MeetingController@destroy')->middleware('permission:meetings.delete');
    $router->get('/calendar', 'MeetingController@calendar');
});

// إدارة العقود
$router->group(['prefix' => 'contracts', 'middleware' => ['auth', 'permission:contracts.view']], function($router) {
    $router->get('/', 'ContractController@index');
    $router->get('/create', 'ContractController@create')->middleware('permission:contracts.create');
    $router->post('/', 'ContractController@store')->middleware('permission:contracts.create');
    $router->get('/{id}', 'ContractController@show');
    $router->get('/{id}/edit', 'ContractController@edit')->middleware('permission:contracts.edit');
    $router->post('/{id}', 'ContractController@update')->middleware('permission:contracts.edit');
    $router->post('/{id}/delete', 'ContractController@destroy')->middleware('permission:contracts.delete');
    $router->post('/{id}/approve', 'ContractController@approve')->middleware('permission:contracts.approve');
});

// التقارير
$router->group(['prefix' => 'reports', 'middleware' => ['auth', 'permission:reports.view']], function($router) {
    $router->get('/', 'ReportController@index');
    $router->get('/users', 'ReportController@users');
    $router->get('/organizations', 'ReportController@organizations');
    $router->get('/projects', 'ReportController@projects');
    $router->get('/finance', 'ReportController@finance');
    $router->get('/donations', 'ReportController@donations');
    $router->post('/export', 'ReportController@export')->middleware('permission:reports.export');
});

// الإعدادات
$router->group(['prefix' => 'settings', 'middleware' => ['auth', 'permission:settings.view']], function($router) {
    $router->get('/', 'SettingController@index');
    $router->post('/', 'SettingController@update')->middleware('permission:settings.edit');
    $router->get('/backup', 'SettingController@backup');
    $router->post('/backup', 'SettingController@createBackup');
});

// سجل التدقيق
$router->group(['prefix' => 'audit', 'middleware' => ['auth', 'permission:audit.view']], function($router) {
    $router->get('/', 'AuditController@index');
    $router->get('/{id}', 'AuditController@show');
});

// واجهة API
$router->group(['prefix' => 'api/v1'], function($router) {
    // مصادقة API
    $router->post('/auth/login', 'Api\AuthController@login');
    $router->post('/auth/refresh', 'Api\AuthController@refresh');
    $router->post('/auth/logout', 'Api\AuthController@logout');
    
    // API محمية
    $router->group(['middleware' => 'jwt'], function($router) {
        $router->get('/user', 'Api\UserController@profile');
        $router->get('/organizations', 'Api\OrganizationController@index');
        $router->get('/projects', 'Api\ProjectController@index');
        $router->get('/donations', 'Api\DonationController@index');
        $router->get('/notifications', 'Api\NotificationController@index');
    });
});

return $router;
