<?php
// نظام إدارة المؤسسات غير الربحية - لوحة التحكم

// تحديد مسار الجذر
define('ROOT_PATH', dirname(__DIR__));

// بدء الجلسة
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: /login.php');
    exit;
}

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// إعداد قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// معالجة تسجيل الخروج
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: /login.php');
    exit;
}

// جلب إحصائيات سريعة
try {
    $stats = [];

    // عدد المستخدمين
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    $stats['users'] = $stmt->fetch()['count'];

    // عدد المنظمات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM organizations WHERE status = 'active'");
    $stats['organizations'] = $stmt->fetch()['count'];

    // عدد المشاريع
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM projects");
    $stats['projects'] = $stmt->fetch()['count'];

    // إجمالي التبرعات
    $stmt = $pdo->query("SELECT SUM(amount) as total FROM donations WHERE status = 'completed'");
    $stats['donations'] = $stmt->fetch()['total'] ?? 0;

} catch (PDOException $e) {
    $stats = ['users' => 0, 'organizations' => 0, 'projects' => 0, 'donations' => 0];
}

$user_name = $_SESSION['user_name'] ?? 'المستخدم';
$user_role = $_SESSION['user_role'] ?? 'employee';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة المؤسسات غير الربحية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        .quick-action {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            display: block;
            margin-bottom: 15px;
        }
        .quick-action:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            color: #667eea;
        }
        .quick-action i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-hands-helping me-2"></i>
                نظام إدارة المؤسسات
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?= htmlspecialchars($user_name) ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="?logout=1"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Welcome Section -->
        <div class="welcome-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-tachometer-alt me-2"></i>مرحباً بك، <?= htmlspecialchars($user_name) ?>!</h2>
                    <p class="mb-0">نظام إدارة المؤسسات غير الربحية - لوحة التحكم الرئيسية</p>
                    <small>الدور: <?= htmlspecialchars($user_role) ?> | آخر دخول: <?= date('Y-m-d H:i') ?></small>
                </div>
                <div class="col-md-4 text-center">
                    <i class="fas fa-chart-line" style="font-size: 4rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-icon text-primary">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number text-primary"><?= number_format($stats['users']) ?></div>
                    <div class="text-muted">المستخدمون النشطون</div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-icon text-success">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stat-number text-success"><?= number_format($stats['organizations']) ?></div>
                    <div class="text-muted">المنظمات المسجلة</div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-icon text-warning">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="stat-number text-warning"><?= number_format($stats['projects']) ?></div>
                    <div class="text-muted">المشاريع الجارية</div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="stat-card text-center">
                    <div class="stat-icon text-info">
                        <i class="fas fa-hand-holding-heart"></i>
                    </div>
                    <div class="stat-number text-info"><?= number_format($stats['donations'], 2) ?> ر.س</div>
                    <div class="text-muted">إجمالي التبرعات</div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <h4><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h4>
            </div>
        </div>

        <!-- الصف الأول -->
        <div class="row">
            <div class="col-md-3">
                <a href="/governance-admin.php" class="quick-action">
                    <i class="fas fa-gavel text-primary"></i>
                    <strong>إدارة الحوكمة</strong>
                    <small class="d-block text-muted">معايير المركز الوطني للقطاع غير الربحي</small>
                </a>
            </div>

            <div class="col-md-3">
                <a href="/media-library.php" class="quick-action">
                    <i class="fas fa-photo-video text-success"></i>
                    <strong>مكتبة الوسائط</strong>
                    <small class="d-block text-muted">إدارة الصور والفيديوهات</small>
                </a>
            </div>

            <div class="col-md-3">
                <a href="/articles-admin.php" class="quick-action">
                    <i class="fas fa-file-alt text-info"></i>
                    <strong>إدارة المقالات</strong>
                    <small class="d-block text-muted">نظام المقالات المتقدم</small>
                </a>
            </div>

            <div class="col-md-3">
                <a href="/admin-news.php" class="quick-action">
                    <i class="fas fa-newspaper text-warning"></i>
                    <strong>إدارة الأخبار</strong>
                    <small class="d-block text-muted">تحرير وإدارة الأخبار</small>
                </a>
            </div>
        </div>

        <!-- الصف الثاني -->
        <div class="row mt-3">
            <div class="col-md-3">
                <a href="/governance.php" class="quick-action" target="_blank">
                    <i class="fas fa-eye text-secondary"></i>
                    <strong>عرض الحوكمة</strong>
                    <small class="d-block text-muted">مشاهدة وثائق الحوكمة للزوار</small>
                </a>
            </div>

            <div class="col-md-3">
                <a href="/media-gallery.php" class="quick-action" target="_blank">
                    <i class="fas fa-images text-secondary"></i>
                    <strong>معرض الوسائط</strong>
                    <small class="d-block text-muted">عرض الوسائط للزوار</small>
                </a>
            </div>

            <div class="col-md-3">
                <a href="/articles.php" class="quick-action" target="_blank">
                    <i class="fas fa-book-open text-secondary"></i>
                    <strong>عرض المقالات</strong>
                    <small class="d-block text-muted">مشاهدة المقالات للزوار</small>
                </a>
            </div>

            <div class="col-md-3">
                <a href="/advanced-cms-update.php" class="quick-action">
                    <i class="fas fa-rocket text-danger"></i>
                    <strong>التحديث المتقدم</strong>
                    <small class="d-block text-muted">إضافة الحوكمة والوسائط والمقالات</small>
                </a>
            </div>
        </div>

        <!-- نظام الموارد البشرية -->
        <div class="row mt-4">
            <div class="col-12">
                <h4><i class="fas fa-users-cog me-2"></i>نظام الموارد البشرية</h4>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3">
                <a href="/employees.php" class="quick-action">
                    <i class="fas fa-users text-primary"></i>
                    <strong>إدارة الموظفين</strong>
                    <small class="d-block text-muted">إضافة وإدارة بيانات الموظفين</small>
                </a>
            </div>

            <div class="col-md-3">
                <a href="/leave-requests.php" class="quick-action">
                    <i class="fas fa-calendar-alt text-info"></i>
                    <strong>إدارة الإجازات</strong>
                    <small class="d-block text-muted">طلبات الإجازات ورصيد الموظفين</small>
                </a>
            </div>

            <div class="col-md-3">
                <a href="/payroll.php" class="quick-action">
                    <i class="fas fa-money-bill-wave text-success"></i>
                    <strong>إدارة الرواتب</strong>
                    <small class="d-block text-muted">حساب ومعالجة رواتب الموظفين</small>
                </a>
            </div>

            <div class="col-md-3">
                <a href="/messages.php" class="quick-action">
                    <i class="fas fa-envelope text-purple"></i>
                    <strong>المراسلة الداخلية</strong>
                    <small class="d-block text-muted">التواصل بين الموظفين والإدارة</small>
                </a>
            </div>
        </div>

        <!-- إعداد نظام الموارد البشرية -->
        <div class="row mt-3">
            <div class="col-md-3">
                <a href="/setup-hr.php" class="quick-action" style="border: 2px dashed #28a745;">
                    <i class="fas fa-cogs text-success"></i>
                    <strong>إعداد نظام الموارد البشرية</strong>
                    <small class="d-block text-muted">تشغيل هذا أولاً لإنشاء الجداول</small>
                </a>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clock me-2"></i>النشاطات الأخيرة</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            مرحباً بك في نظام إدارة المؤسسات غير الربحية! هذا النظام جاهز للاستخدام.
                        </div>
                        <p class="text-muted">لا توجد نشاطات حديثة حالياً.</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bell me-2"></i>التنبيهات</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <small><i class="fas fa-exclamation-triangle me-1"></i>
                            يرجى تغيير كلمة مرور المدير الافتراضية</small>
                        </div>
                        <div class="alert alert-success">
                            <small><i class="fas fa-check-circle me-1"></i>
                            تم تثبيت النظام بنجاح</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
