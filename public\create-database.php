<?php
// إنشاء قاعدة البيانات
echo "<h2>🔧 إنشاء قاعدة البيانات...</h2>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'nonprofit_cms';
    
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ تم الاتصال بـ MySQL بنجاح</p>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ تم إنشاء قاعدة البيانات: $database</p>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    echo "<p>✅ تم الاتصال بقاعدة البيانات الجديدة</p>";
    
    // إنشاء الجداول الأساسية
    
    // جدول المستخدمين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'manager', 'employee') DEFAULT 'employee',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p>✅ تم إنشاء جدول المستخدمين</p>";
    
    // جدول الأقسام
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS departments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            manager_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p>✅ تم إنشاء جدول الأقسام</p>";
    
    // جدول المناصب
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS positions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            department_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
        )
    ");
    echo "<p>✅ تم إنشاء جدول المناصب</p>";
    
    // جدول الموظفين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS employees (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT UNIQUE,
            employee_id VARCHAR(50) UNIQUE NOT NULL,
            department_id INT,
            position_id INT,
            hire_date DATE,
            salary DECIMAL(10,2),
            vacation_balance INT DEFAULT 30,
            sick_leave_balance INT DEFAULT 15,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
            FOREIGN KEY (position_id) REFERENCES positions(id) ON DELETE SET NULL
        )
    ");
    echo "<p>✅ تم إنشاء جدول الموظفين</p>";
    
    // جدول طلبات الإجازات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS leave_requests (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_id INT NOT NULL,
            type ENUM('vacation', 'sick', 'emergency', 'maternity', 'other') NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            days_count INT NOT NULL,
            reason TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            approved_by INT NULL,
            approved_at TIMESTAMP NULL,
            comments TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
            FOREIGN KEY (approved_by) REFERENCES employees(id) ON DELETE SET NULL
        )
    ");
    echo "<p>✅ تم إنشاء جدول طلبات الإجازات</p>";
    
    // جدول الرسائل الداخلية
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS internal_messages (
            id INT PRIMARY KEY AUTO_INCREMENT,
            sender_id INT NOT NULL,
            subject VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
            signature TEXT,
            has_attachments BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sender_id) REFERENCES employees(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✅ تم إنشاء جدول الرسائل الداخلية</p>";
    
    // جدول مستقبلي الرسائل
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS message_recipients (
            id INT PRIMARY KEY AUTO_INCREMENT,
            message_id INT NOT NULL,
            recipient_id INT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            read_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (message_id) REFERENCES internal_messages(id) ON DELETE CASCADE,
            FOREIGN KEY (recipient_id) REFERENCES employees(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✅ تم إنشاء جدول مستقبلي الرسائل</p>";
    
    // جدول الإشعارات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS notifications (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_id INT NOT NULL,
            type VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            related_id INT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE
        )
    ");
    echo "<p>✅ تم إنشاء جدول الإشعارات</p>";
    
    // إدراج بيانات أساسية
    
    // إدراج أقسام افتراضية
    $pdo->exec("
        INSERT IGNORE INTO departments (id, name, description) VALUES
        (1, 'الإدارة العامة', 'الإدارة العليا والتخطيط الاستراتيجي'),
        (2, 'الموارد البشرية', 'إدارة شؤون الموظفين والتوظيف'),
        (3, 'المالية والمحاسبة', 'الشؤون المالية والمحاسبية'),
        (4, 'تقنية المعلومات', 'تطوير وصيانة الأنظمة التقنية'),
        (5, 'التسويق والعلاقات العامة', 'التسويق والتواصل مع الجمهور')
    ");
    echo "<p>✅ تم إدراج الأقسام الافتراضية</p>";
    
    // إدراج مناصب افتراضية
    $pdo->exec("
        INSERT IGNORE INTO positions (id, title, description, department_id) VALUES
        (1, 'المدير العام', 'المسؤول الأول عن المؤسسة', 1),
        (2, 'مدير الموارد البشرية', 'إدارة شؤون الموظفين', 2),
        (3, 'مدير مالي', 'إدارة الشؤون المالية', 3),
        (4, 'مدير تقنية المعلومات', 'إدارة الأنظمة التقنية', 4),
        (5, 'مدير التسويق', 'إدارة التسويق والعلاقات العامة', 5),
        (6, 'موظف موارد بشرية', 'تنفيذ مهام الموارد البشرية', 2),
        (7, 'محاسب', 'تنفيذ المهام المحاسبية', 3),
        (8, 'مطور برمجيات', 'تطوير الأنظمة والتطبيقات', 4),
        (9, 'أخصائي تسويق', 'تنفيذ الحملات التسويقية', 5)
    ");
    echo "<p>✅ تم إدراج المناصب الافتراضية</p>";
    
    // إدراج مستخدمين افتراضيين
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $manager_password = password_hash('123456', PASSWORD_DEFAULT);
    $employee_password = password_hash('123456', PASSWORD_DEFAULT);
    
    $pdo->exec("
        INSERT IGNORE INTO users (id, name, email, password, role, is_active) VALUES
        (1, 'المدير العام', '<EMAIL>', '$admin_password', 'admin', 1),
        (2, 'أحمد محمد', '<EMAIL>', '$manager_password', 'manager', 1),
        (3, 'فاطمة علي', '<EMAIL>', '$employee_password', 'employee', 1),
        (4, 'محمد سالم', '<EMAIL>', '$employee_password', 'employee', 1),
        (5, 'نورا أحمد', '<EMAIL>', '$employee_password', 'employee', 1)
    ");
    echo "<p>✅ تم إدراج المستخدمين الافتراضيين</p>";
    
    // إدراج موظفين افتراضيين
    $pdo->exec("
        INSERT IGNORE INTO employees (id, user_id, employee_id, department_id, position_id, hire_date, salary, vacation_balance, sick_leave_balance) VALUES
        (1, 1, 'EMP0001', 1, 1, '2020-01-01', 15000.00, 30, 15),
        (2, 2, 'EMP0002', 2, 2, '2021-03-15', 8000.00, 25, 12),
        (3, 3, 'EMP0003', 3, 7, '2022-06-01', 5500.00, 30, 15),
        (4, 4, 'EMP0004', 4, 8, '2022-09-10', 6000.00, 28, 14),
        (5, 5, 'EMP0005', 5, 9, '2023-01-20', 5000.00, 30, 15)
    ");
    echo "<p>✅ تم إدراج الموظفين الافتراضيين</p>";
    
    echo "<hr>";
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 تم إنشاء قاعدة البيانات بنجاح!</h3>";
    echo "<p>تم إنشاء:</p>";
    echo "<ul>";
    echo "<li>✅ قاعدة البيانات: nonprofit_cms</li>";
    echo "<li>✅ 8 جداول أساسية</li>";
    echo "<li>✅ 5 أقسام افتراضية</li>";
    echo "<li>✅ 9 مناصب افتراضية</li>";
    echo "<li>✅ 5 مستخدمين افتراضيين</li>";
    echo "<li>✅ 5 موظفين افتراضيين</li>";
    echo "</ul>";
    echo "<p><strong>حسابات تجريبية:</strong></p>";
    echo "<ul>";
    echo "<li>المدير العام: <EMAIL> / admin123</li>";
    echo "<li>مدير قسم: <EMAIL> / 123456</li>";
    echo "<li>موظف: <EMAIL> / 123456</li>";
    echo "</ul>";
    echo "<p><a href='/dashboard.php' class='btn btn-primary'>الذهاب للوحة التحكم</a> ";
    echo "<a href='/login.php' class='btn btn-success'>تسجيل الدخول</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في إنشاء قاعدة البيانات</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p><strong>تأكد من:</strong></p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "<li>وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
ul { margin: 10px 0; padding-left: 20px; }
.btn { display: inline-block; padding: 8px 16px; margin: 5px; background: #007bff; color: white; border-radius: 5px; text-decoration: none; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
</style>";
?>
