<?php
/**
 * صفحة اختبار النظام
 */

echo "<h1>اختبار النظام</h1>";

// اختبار PHP
echo "<h2>معلومات PHP:</h2>";
echo "<p>إصدار PHP: " . PHP_VERSION . "</p>";
echo "<p>المنطقة الزمنية: " . date_default_timezone_get() . "</p>";
echo "<p>الوقت الحالي: " . date('Y-m-d H:i:s') . "</p>";

// اختبار الملفات
echo "<h2>اختبار الملفات:</h2>";
$files_to_check = [
    '../autoload.php',
    '../config/app.php',
    '../config/database.php',
    '../core/Router/Router.php',
    '../core/Auth/Auth.php',
    '../app/Controllers/HomeController.php',
];

foreach ($files_to_check as $file) {
    $exists = file_exists($file);
    $status = $exists ? '✅ موجود' : '❌ غير موجود';
    echo "<p>{$file}: {$status}</p>";
}

// اختبار autoloader
echo "<h2>اختبار Autoloader:</h2>";
try {
    require_once '../autoload.php';
    echo "<p>✅ تم تحميل autoloader بنجاح</p>";
    
    // اختبار تحميل كلاس
    if (class_exists('Core\\Router\\Router')) {
        echo "<p>✅ تم تحميل Router بنجاح</p>";
    } else {
        echo "<p>❌ فشل في تحميل Router</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في autoloader: " . $e->getMessage() . "</p>";
}

// اختبار قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات:</h2>";
try {
    $config = include '../config/database.php';
    $dbConfig = $config['connections'][$config['default']];
    
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    echo "<p>✅ الاتصال بـ MySQL نجح</p>";
    
    // اختبار قاعدة البيانات
    $dsn_with_db = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset=utf8mb4";
    $pdo_db = new PDO($dsn_with_db, $dbConfig['username'], $dbConfig['password']);
    echo "<p>✅ الاتصال بقاعدة البيانات نجح</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p>💡 يرجى تشغيل معالج التثبيت: <a href='/install.php'>install.php</a></p>";
}

echo "<hr>";
echo "<p><a href='/'>العودة للصفحة الرئيسية</a> | <a href='/install.php'>معالج التثبيت</a></p>";
?>
