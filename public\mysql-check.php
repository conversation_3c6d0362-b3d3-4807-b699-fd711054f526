<?php
// أداة فحص MySQL
error_reporting(E_ALL);
ini_set('display_errors', 1);

// تحديد مسار الجذر
define('ROOT_PATH', dirname(__DIR__));

// بدء الجلسة
session_start();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص MySQL</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .check-container { max-width: 800px; margin: 50px auto; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container check-container">
        <div class="card">
            <div class="card-header text-center bg-info text-white">
                <h3>🔍 فحص حالة MySQL</h3>
                <p class="mb-0">تشخيص مشاكل الاتصال بقاعدة البيانات</p>
            </div>
            <div class="card-body p-4">

                <h5>1. فحص إضافة PDO MySQL:</h5>
                <?php if (extension_loaded('pdo_mysql')): ?>
                    <p class="status-ok">✅ إضافة PDO MySQL متوفرة</p>
                <?php else: ?>
                    <p class="status-error">❌ إضافة PDO MySQL غير متوفرة</p>
                    <div class="alert alert-danger">
                        يجب تفعيل إضافة pdo_mysql في PHP. تحقق من ملف php.ini وتأكد من إلغاء التعليق عن السطر:
                        <code>extension=pdo_mysql</code>
                    </div>
                <?php endif; ?>

                <h5>2. فحص الاتصال بـ localhost:</h5>
                <?php
                $hosts = ['localhost', '127.0.0.1'];
                $ports = [3306, 3307];

                foreach ($hosts as $host) {
                    foreach ($ports as $port) {
                        echo "<h6>اختبار {$host}:{$port}</h6>";

                        // اختبار الاتصال بالمنفذ
                        $connection = @fsockopen($host, $port, $errno, $errstr, 5);
                        if ($connection) {
                            echo "<p class='status-ok'>✅ المنفذ {$port} مفتوح على {$host}</p>";
                            fclose($connection);

                            // اختبار MySQL
                            try {
                                $pdo = new PDO("mysql:host={$host};port={$port}", 'root', '', [
                                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                                    PDO::ATTR_TIMEOUT => 5
                                ]);
                                echo "<p class='status-ok'>✅ نجح الاتصال بـ MySQL على {$host}:{$port}</p>";

                                // عرض معلومات MySQL
                                $version = $pdo->query('SELECT VERSION()')->fetchColumn();
                                echo "<p class='text-muted'>إصدار MySQL: {$version}</p>";

                            } catch (PDOException $e) {
                                echo "<p class='status-error'>❌ فشل الاتصال بـ MySQL: " . $e->getMessage() . "</p>";
                            }
                        } else {
                            echo "<p class='status-error'>❌ المنفذ {$port} مغلق على {$host}</p>";
                            echo "<p class='text-muted'>خطأ: {$errstr} ({$errno})</p>";
                        }
                        echo "<hr>";
                    }
                }
                ?>

                <h5>3. فحص خدمات Windows:</h5>
                <?php if (PHP_OS_FAMILY === 'Windows'): ?>
                    <?php
                    $services = ['wampmysqld64', 'MySQL80', 'MySQL57', 'MySQL'];
                    foreach ($services as $service) {
                        $output = [];
                        $return_var = 0;
                        exec("sc query \"{$service}\" 2>nul", $output, $return_var);

                        if ($return_var === 0) {
                            $status = implode("\n", $output);
                            if (strpos($status, 'RUNNING') !== false) {
                                echo "<p class='status-ok'>✅ خدمة {$service} تعمل</p>";
                            } else {
                                echo "<p class='status-warning'>⚠️ خدمة {$service} موجودة لكن لا تعمل</p>";
                            }
                        } else {
                            echo "<p class='status-error'>❌ خدمة {$service} غير موجودة</p>";
                        }
                    }
                    ?>
                <?php else: ?>
                    <p class='text-muted'>فحص الخدمات متاح فقط على Windows</p>
                <?php endif; ?>

                <h5>4. معلومات PHP:</h5>
                <ul>
                    <li><strong>إصدار PHP:</strong> <?= PHP_VERSION ?></li>
                    <li><strong>نظام التشغيل:</strong> <?= PHP_OS ?></li>
                    <li><strong>معمارية النظام:</strong> <?= php_uname('m') ?></li>
                </ul>

                <h5>5. الحلول المقترحة:</h5>
                <div class="alert alert-info">
                    <h6>إذا كان MySQL لا يعمل:</h6>
                    <ol>
                        <li>افتح WAMP وتأكد من أن الأيقونة خضراء</li>
                        <li>اضغط بالزر الأيمن على أيقونة WAMP → MySQL → Service → Start/Resume Service</li>
                        <li>إذا فشل التشغيل، اضغط على MySQL → Service → Install Service</li>
                        <li>تحقق من ملفات السجل في مجلد WAMP/logs/mysql</li>
                        <li>جرب إعادة تشغيل WAMP كمدير</li>
                    </ol>
                </div>

                <div class="text-center mt-4">
                    <a href="/install.php" class="btn btn-primary">العودة إلى التثبيت</a>
                    <a href="/" class="btn btn-secondary">الصفحة الرئيسية</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
