<?php
session_start();

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: /login.php');
    exit;
}

// التحقق من وجود معرف المرفق
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    die("معرف المرفق غير صحيح");
}

$attachment_id = $_GET['id'];

// جلب معلومات المرفق
$stmt = $pdo->prepare("
    SELECT ma.*, im.sender_id, mr.recipient_id
    FROM message_attachments ma
    JOIN internal_messages im ON ma.message_id = im.id
    LEFT JOIN message_recipients mr ON im.id = mr.message_id
    WHERE ma.id = ?
");

$stmt->execute([$attachment_id]);
$attachment = $stmt->fetch();

if (!$attachment) {
    die("المرفق غير موجود");
}

// جلب معلومات الموظف الحالي
$current_employee = null;
$emp_stmt = $pdo->prepare("SELECT * FROM employees WHERE user_id = ?");
$emp_stmt->execute([$_SESSION['user_id']]);
$current_employee = $emp_stmt->fetch();

if (!$current_employee) {
    die("غير مصرح لك بالوصول");
}

// التحقق من صلاحية الوصول للمرفق
$has_access = false;

// إذا كان المستخدم هو مرسل الرسالة
if ($attachment['sender_id'] == $current_employee['id']) {
    $has_access = true;
}

// إذا كان المستخدم من مستقبلي الرسالة
$recipient_check = $pdo->prepare("
    SELECT COUNT(*) FROM message_recipients 
    WHERE message_id = ? AND recipient_id = ?
");
$recipient_check->execute([$attachment['message_id'], $current_employee['id']]);
if ($recipient_check->fetchColumn() > 0) {
    $has_access = true;
}

if (!$has_access) {
    die("غير مصرح لك بتحميل هذا المرفق");
}

// التحقق من وجود الملف
$file_path = $attachment['file_path'];
if (!file_exists($file_path)) {
    die("الملف غير موجود على الخادم");
}

// تحديد نوع المحتوى
$file_type = $attachment['file_type'];
$content_types = [
    'pdf' => 'application/pdf',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls' => 'application/vnd.ms-excel',
    'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt' => 'application/vnd.ms-powerpoint',
    'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'txt' => 'text/plain',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif'
];

$file_ext = strtolower(pathinfo($attachment['original_name'], PATHINFO_EXTENSION));
$content_type = $content_types[$file_ext] ?? 'application/octet-stream';

// إرسال الملف للتحميل
header('Content-Type: ' . $content_type);
header('Content-Disposition: attachment; filename="' . $attachment['original_name'] . '"');
header('Content-Length: ' . $attachment['file_size']);
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

// قراءة وإرسال الملف
readfile($file_path);
exit;
?>
