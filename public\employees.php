<?php
session_start();

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];

    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: /login.php');
    exit;
}

$success = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'add_employee') {
        try {
            // التحقق من البيانات المطلوبة
            $required_fields = ['name', 'email', 'employee_id', 'department_id', 'position_id', 'hire_date', 'salary'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("جميع الحقول المطلوبة يجب ملؤها");
                }
            }

            $pdo->beginTransaction();

            // إنشاء حساب المستخدم أولاً
            $user_stmt = $pdo->prepare("
                INSERT INTO users (name, email, password, role, is_active)
                VALUES (?, ?, ?, 'employee', 1)
            ");
            $default_password = password_hash('123456', PASSWORD_DEFAULT);
            $user_stmt->execute([
                $_POST['name'],
                $_POST['email'],
                $default_password
            ]);

            $user_id = $pdo->lastInsertId();

            // إضافة بيانات الموظف
            $employee_stmt = $pdo->prepare("
                INSERT INTO employees (
                    user_id, employee_id, department_id, position_id, manager_id,
                    hire_date, employment_type, salary, phone, emergency_contact_name,
                    emergency_contact_phone, address, national_id, bank_account, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $employee_stmt->execute([
                $user_id,
                $_POST['employee_id'],
                $_POST['department_id'],
                $_POST['position_id'],
                $_POST['manager_id'] ?: null,
                $_POST['hire_date'],
                $_POST['employment_type'] ?? 'full_time',
                $_POST['salary'],
                $_POST['phone'] ?? null,
                $_POST['emergency_contact_name'] ?? null,
                $_POST['emergency_contact_phone'] ?? null,
                $_POST['address'] ?? null,
                $_POST['national_id'] ?? null,
                $_POST['bank_account'] ?? null,
                $_POST['notes'] ?? null
            ]);

            $pdo->commit();
            $success = "تم إضافة الموظف بنجاح";

        } catch (Exception $e) {
            $pdo->rollBack();
            $error = "خطأ في إضافة الموظف: " . $e->getMessage();
        }
    }

    elseif ($action === 'update_employee') {
        try {
            $employee_id = $_POST['employee_id'];

            $pdo->beginTransaction();

            // تحديث بيانات المستخدم
            $user_stmt = $pdo->prepare("
                UPDATE users u
                JOIN employees e ON u.id = e.user_id
                SET u.name = ?, u.email = ?
                WHERE e.id = ?
            ");
            $user_stmt->execute([
                $_POST['name'],
                $_POST['email'],
                $employee_id
            ]);

            // تحديث بيانات الموظف
            $employee_stmt = $pdo->prepare("
                UPDATE employees SET
                    department_id = ?, position_id = ?, manager_id = ?,
                    employment_type = ?, salary = ?, phone = ?,
                    emergency_contact_name = ?, emergency_contact_phone = ?,
                    address = ?, national_id = ?, bank_account = ?, notes = ?
                WHERE id = ?
            ");

            $employee_stmt->execute([
                $_POST['department_id'],
                $_POST['position_id'],
                $_POST['manager_id'] ?: null,
                $_POST['employment_type'],
                $_POST['salary'],
                $_POST['phone'],
                $_POST['emergency_contact_name'],
                $_POST['emergency_contact_phone'],
                $_POST['address'],
                $_POST['national_id'],
                $_POST['bank_account'],
                $_POST['notes'],
                $employee_id
            ]);

            $pdo->commit();
            $success = "تم تحديث بيانات الموظف بنجاح";

        } catch (Exception $e) {
            $pdo->rollBack();
            $error = "خطأ في تحديث الموظف: " . $e->getMessage();
        }
    }

    elseif ($action === 'delete_employee') {
        try {
            $employee_id = $_POST['employee_id'];

            // تحديث حالة الموظف بدلاً من الحذف
            $stmt = $pdo->prepare("UPDATE employees SET status = 'terminated' WHERE id = ?");
            $stmt->execute([$employee_id]);

            $success = "تم إنهاء خدمة الموظف بنجاح";

        } catch (Exception $e) {
            $error = "خطأ في إنهاء خدمة الموظف: " . $e->getMessage();
        }
    }
}

// جلب الأقسام
$departments = $pdo->query("SELECT * FROM departments WHERE is_active = 1 ORDER BY name")->fetchAll();

// جلب المناصب
$positions = $pdo->query("
    SELECT p.*, d.name as department_name
    FROM positions p
    JOIN departments d ON p.department_id = d.id
    WHERE p.is_active = 1
    ORDER BY d.name, p.title
")->fetchAll();

// جلب الموظفين
$filter = $_GET['filter'] ?? 'active';
$department_filter = $_GET['department'] ?? '';

$where_conditions = [];
$params = [];

if ($filter === 'active') {
    $where_conditions[] = "e.status = 'active'";
} elseif ($filter === 'inactive') {
    $where_conditions[] = "e.status IN ('inactive', 'terminated')";
}

if ($department_filter) {
    $where_conditions[] = "e.department_id = ?";
    $params[] = $department_filter;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

$employees_query = "
    SELECT e.*, u.name, u.email, d.name as department_name,
           p.title as position_title, m.employee_id as manager_employee_id,
           mu.name as manager_name
    FROM employees e
    JOIN users u ON e.user_id = u.id
    JOIN departments d ON e.department_id = d.id
    JOIN positions p ON e.position_id = p.id
    LEFT JOIN employees m ON e.manager_id = m.id
    LEFT JOIN users mu ON m.user_id = mu.id
    {$where_clause}
    ORDER BY u.name
";

$stmt = $pdo->prepare($employees_query);
$stmt->execute($params);
$employees = $stmt->fetchAll();

// إحصائيات
$stats = [
    'total_employees' => $pdo->query("SELECT COUNT(*) FROM employees WHERE status = 'active'")->fetchColumn(),
    'total_departments' => $pdo->query("SELECT COUNT(*) FROM departments WHERE is_active = 1")->fetchColumn(),
    'pending_leaves' => $pdo->query("SELECT COUNT(*) FROM leave_requests WHERE status = 'pending'")->fetchColumn(),
    'avg_salary' => $pdo->query("SELECT AVG(salary) FROM employees WHERE status = 'active'")->fetchColumn() ?: 0
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموظفين - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #007bff, #0056b3); }
        .stats-card { background: linear-gradient(45deg, #007bff, #0056b3); color: white; border-radius: 15px; }
        .employee-card { border-radius: 10px; transition: transform 0.2s; }
        .employee-card:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .status-badge { font-size: 0.8em; }
        .salary-amount { font-weight: bold; color: #28a745; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-users me-2"></i>إدارة الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="/leave-requests.php">
                    <i class="fas fa-calendar-alt me-1"></i>الإجازات
                </a>
                <a class="nav-link" href="/payroll.php">
                    <i class="fas fa-money-bill me-1"></i>الرواتب
                </a>
                <a class="nav-link" href="/messages.php">
                    <i class="fas fa-envelope me-1"></i>الرسائل
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div class="row">
            <!-- نموذج إضافة موظف جديد -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-user-plus me-2"></i>إضافة موظف جديد</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_employee">

                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل *</label>
                                <input type="text" name="name" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني *</label>
                                <input type="email" name="email" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">رقم الموظف *</label>
                                <input type="text" name="employee_id" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">القسم *</label>
                                <select name="department_id" class="form-select" required>
                                    <option value="">اختر القسم</option>
                                    <?php foreach ($departments as $dept): ?>
                                    <option value="<?= $dept['id'] ?>"><?= htmlspecialchars($dept['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">المنصب *</label>
                                <select name="position_id" class="form-select" required>
                                    <option value="">اختر المنصب</option>
                                    <?php foreach ($positions as $pos): ?>
                                    <option value="<?= $pos['id'] ?>" data-department="<?= $pos['department_id'] ?>">
                                        <?= htmlspecialchars($pos['title']) ?> - <?= htmlspecialchars($pos['department_name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">المدير المباشر</label>
                                <select name="manager_id" class="form-select">
                                    <option value="">بدون مدير مباشر</option>
                                    <?php foreach ($employees as $emp): ?>
                                    <option value="<?= $emp['id'] ?>">
                                        <?= htmlspecialchars($emp['name']) ?> - <?= htmlspecialchars($emp['position_title']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">تاريخ التوظيف *</label>
                                <input type="date" name="hire_date" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">نوع التوظيف</label>
                                <select name="employment_type" class="form-select">
                                    <option value="full_time">دوام كامل</option>
                                    <option value="part_time">دوام جزئي</option>
                                    <option value="contract">عقد</option>
                                    <option value="intern">متدرب</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الراتب الأساسي *</label>
                                <input type="number" name="salary" class="form-control" step="0.01" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" name="phone" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">جهة الاتصال في الطوارئ</label>
                                <input type="text" name="emergency_contact_name" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">هاتف الطوارئ</label>
                                <input type="tel" name="emergency_contact_phone" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea name="address" class="form-control" rows="2"></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">رقم الهوية</label>
                                <input type="text" name="national_id" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">رقم الحساب البنكي</label>
                                <input type="text" name="bank_account" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea name="notes" class="form-control" rows="2"></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>إضافة الموظف
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة الموظفين -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-users me-2"></i>قائمة الموظفين</h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" onchange="filterEmployees()" id="departmentFilter">
                                <option value="">جميع الأقسام</option>
                                <?php foreach ($departments as $dept): ?>
                                <option value="<?= $dept['id'] ?>" <?= $department_filter == $dept['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($dept['name']) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="btn-group">
                                <a href="?filter=active" class="btn btn-sm <?= $filter === 'active' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                    نشط
                                </a>
                                <a href="?filter=inactive" class="btn btn-sm <?= $filter === 'inactive' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                    غير نشط
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($employees)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا يوجد موظفين بعد</p>
                        </div>
                        <?php else: ?>
                        <div class="row">
                            <?php foreach ($employees as $employee): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card employee-card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0"><?= htmlspecialchars($employee['name']) ?></h6>
                                            <span class="badge status-badge <?= $employee['status'] === 'active' ? 'bg-success' : 'bg-secondary' ?>">
                                                <?= $employee['status'] === 'active' ? 'نشط' : 'غير نشط' ?>
                                            </span>
                                        </div>

                                        <div class="small text-muted mb-2">
                                            <div><i class="fas fa-id-badge me-1"></i><?= htmlspecialchars($employee['employee_id']) ?></div>
                                            <div><i class="fas fa-envelope me-1"></i><?= htmlspecialchars($employee['email']) ?></div>
                                            <div><i class="fas fa-building me-1"></i><?= htmlspecialchars($employee['department_name']) ?></div>
                                            <div><i class="fas fa-briefcase me-1"></i><?= htmlspecialchars($employee['position_title']) ?></div>
                                            <?php if ($employee['manager_name']): ?>
                                            <div><i class="fas fa-user-tie me-1"></i><?= htmlspecialchars($employee['manager_name']) ?></div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="salary-amount"><?= number_format($employee['salary'], 0) ?> ريال</span>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editEmployee(<?= $employee['id'] ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" onclick="viewEmployee(<?= $employee['id'] ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if ($employee['status'] === 'active'): ?>
                                                <button class="btn btn-outline-danger" onclick="terminateEmployee(<?= $employee['id'] ?>)">
                                                    <i class="fas fa-user-times"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function filterEmployees() {
            const department = document.getElementById('departmentFilter').value;
            const currentUrl = new URL(window.location);
            if (department) {
                currentUrl.searchParams.set('department', department);
            } else {
                currentUrl.searchParams.delete('department');
            }
            window.location.href = currentUrl.toString();
        }

        function editEmployee(id) {
            // TODO: فتح نموذج التعديل
            alert('سيتم إضافة نموذج التعديل قريباً');
        }

        function viewEmployee(id) {
            // TODO: عرض تفاصيل الموظف
            alert('سيتم إضافة صفحة التفاصيل قريباً');
        }

        function terminateEmployee(id) {
            if (confirm('هل أنت متأكد من إنهاء خدمة هذا الموظف؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_employee">
                    <input type="hidden" name="employee_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
