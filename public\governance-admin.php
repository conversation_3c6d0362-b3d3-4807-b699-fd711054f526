<?php
// إدارة الحوكمة - معايير المركز الوطني للقطاع غير الربحي
define('ROOT_PATH', dirname(__DIR__));
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: /login.php');
    exit;
}

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// إعداد قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

$success = '';
$error = '';

// معالجة رفع الملفات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'upload_document') {
        try {
            $title = trim($_POST['title']);
            $description = trim($_POST['description']);
            $category_id = (int)$_POST['category_id'];
            $effective_date = $_POST['effective_date'] ?: null;
            $expiry_date = $_POST['expiry_date'] ?: null;
            $version = trim($_POST['version']) ?: '1.0';
            $tags = trim($_POST['tags']);
            $is_public = isset($_POST['is_public']) ? 1 : 0;
            
            if (empty($title) || $category_id <= 0) {
                throw new Exception('يجب ملء العنوان واختيار التصنيف');
            }
            
            $file_path = null;
            $file_name = null;
            $file_size = 0;
            $file_type = null;
            
            // معالجة رفع الملف
            if (isset($_FILES['document']) && $_FILES['document']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = 'uploads/governance/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                $file_info = pathinfo($_FILES['document']['name']);
                $allowed_types = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
                
                if (!in_array(strtolower($file_info['extension']), $allowed_types)) {
                    throw new Exception('نوع الملف غير مدعوم. الأنواع المسموحة: ' . implode(', ', $allowed_types));
                }
                
                $file_name = uniqid() . '.' . $file_info['extension'];
                $file_path = $upload_dir . $file_name;
                
                if (!move_uploaded_file($_FILES['document']['tmp_name'], $file_path)) {
                    throw new Exception('فشل في رفع الملف');
                }
                
                $file_size = filesize($file_path);
                $file_type = $file_info['extension'];
            }
            
            // إنشاء slug
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
            
            // إدراج في قاعدة البيانات
            $stmt = $pdo->prepare("
                INSERT INTO governance_documents 
                (title, slug, description, category_id, file_path, file_name, file_size, file_type, 
                 version, effective_date, expiry_date, uploaded_by, is_public, tags) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $title, $slug, $description, $category_id, $file_path, $file_name, 
                $file_size, $file_type, $version, $effective_date, $expiry_date, 
                $_SESSION['user_id'], $is_public, $tags
            ]);
            
            $success = 'تم رفع الوثيقة بنجاح';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// جلب التصنيفات
$categories = $pdo->query("SELECT * FROM governance_categories ORDER BY sort_order, name")->fetchAll();

// جلب الوثائق
$documents_query = "
    SELECT d.*, c.name as category_name, c.color as category_color, u.name as uploader_name
    FROM governance_documents d
    LEFT JOIN governance_categories c ON d.category_id = c.id
    LEFT JOIN users u ON d.uploaded_by = u.id
    ORDER BY d.created_at DESC
";
$documents = $pdo->query($documents_query)->fetchAll();

// إحصائيات
$stats = [
    'total_documents' => $pdo->query("SELECT COUNT(*) FROM governance_documents")->fetchColumn(),
    'pending_approval' => $pdo->query("SELECT COUNT(*) FROM governance_documents WHERE approval_status = 'pending'")->fetchColumn(),
    'public_documents' => $pdo->query("SELECT COUNT(*) FROM governance_documents WHERE is_public = 1")->fetchColumn(),
    'total_downloads' => $pdo->query("SELECT SUM(download_count) FROM governance_documents")->fetchColumn() ?: 0
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحوكمة - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); }
        .stats-card { background: linear-gradient(45deg, #667eea, #764ba2); color: white; border-radius: 15px; }
        .document-card { border-radius: 10px; transition: transform 0.2s; }
        .document-card:hover { transform: translateY(-2px); }
        .category-badge { border-radius: 20px; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-gavel me-2"></i>إدارة الحوكمة
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="/governance.php" target="_blank">
                    <i class="fas fa-eye me-1"></i>عرض للزوار
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <h4><?= $stats['total_documents'] ?></h4>
                    <small>إجمالي الوثائق</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4><?= $stats['pending_approval'] ?></h4>
                    <small>في انتظار الموافقة</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-globe fa-2x mb-2"></i>
                    <h4><?= $stats['public_documents'] ?></h4>
                    <small>وثائق عامة</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-download fa-2x mb-2"></i>
                    <h4><?= $stats['total_downloads'] ?></h4>
                    <small>إجمالي التحميلات</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- نموذج رفع وثيقة جديدة -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-plus me-2"></i>رفع وثيقة جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="upload_document">
                            
                            <div class="mb-3">
                                <label class="form-label">عنوان الوثيقة *</label>
                                <input type="text" name="title" class="form-control" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">التصنيف *</label>
                                <select name="category_id" class="form-select" required>
                                    <option value="">اختر التصنيف</option>
                                    <?php foreach ($categories as $cat): ?>
                                    <option value="<?= $cat['id'] ?>">
                                        <?= htmlspecialchars($cat['name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea name="description" class="form-control" rows="3"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الملف</label>
                                <input type="file" name="document" class="form-control" 
                                       accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx">
                                <small class="text-muted">PDF, Word, Excel, PowerPoint</small>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ السريان</label>
                                    <input type="date" name="effective_date" class="form-control">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الانتهاء</label>
                                    <input type="date" name="expiry_date" class="form-control">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">رقم الإصدار</label>
                                <input type="text" name="version" class="form-control" value="1.0">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الكلمات المفتاحية</label>
                                <input type="text" name="tags" class="form-control" 
                                       placeholder="مفصولة بفواصل">
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" name="is_public" class="form-check-input" id="is_public" checked>
                                    <label class="form-check-label" for="is_public">
                                        متاح للعامة
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-upload me-2"></i>رفع الوثيقة
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة الوثائق -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>الوثائق المرفوعة</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($documents)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد وثائق مرفوعة بعد</p>
                        </div>
                        <?php else: ?>
                        <div class="row">
                            <?php foreach ($documents as $doc): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card document-card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title"><?= htmlspecialchars($doc['title']) ?></h6>
                                            <span class="badge category-badge" 
                                                  style="background-color: <?= $doc['category_color'] ?>">
                                                <?= htmlspecialchars($doc['category_name']) ?>
                                            </span>
                                        </div>
                                        
                                        <?php if ($doc['description']): ?>
                                        <p class="card-text small text-muted">
                                            <?= htmlspecialchars(substr($doc['description'], 0, 100)) ?>...
                                        </p>
                                        <?php endif; ?>
                                        
                                        <div class="small text-muted mb-2">
                                            <i class="fas fa-user me-1"></i><?= htmlspecialchars($doc['uploader_name']) ?>
                                            <br>
                                            <i class="fas fa-calendar me-1"></i><?= date('Y-m-d', strtotime($doc['created_at'])) ?>
                                            <?php if ($doc['file_size']): ?>
                                            <br>
                                            <i class="fas fa-file me-1"></i><?= number_format($doc['file_size']/1024, 1) ?> KB
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <?php if ($doc['file_path']): ?>
                                                <a href="/<?= $doc['file_path'] ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <?php endif; ?>
                                                <button class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-download me-1"></i><?= $doc['download_count'] ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
