<?php

namespace App\Middleware;

use Core\Auth\Auth;

class GuestMiddleware
{
    public function handle(): bool
    {
        $auth = Auth::getInstance();
        
        if ($auth->check()) {
            // إعادة التوجيه إلى لوحة التحكم إذا كان المستخدم مسجل دخول
            if ($this->isAjaxRequest()) {
                http_response_code(302);
                header('Content-Type: application/json');
                echo json_encode(['redirect' => '/dashboard']);
                exit;
            } else {
                header('Location: /dashboard');
                exit;
            }
        }
        
        return true;
    }
    
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
