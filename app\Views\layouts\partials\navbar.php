<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <!-- Brand -->
        <a class="navbar-brand" href="/dashboard">
            <i class="fas fa-hands-helping me-2"></i>
            نظام إدارة المؤسسات
        </a>

        <!-- Mobile Toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Links -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        لوحة التحكم
                    </a>
                </li>

                <!-- Users Management -->
                <?php if ($auth->hasPermission('users.view')): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-users me-1"></i>
                        إدارة المستخدمين
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/users">قائمة المستخدمين</a></li>
                        <?php if ($auth->hasPermission('users.create')): ?>
                        <li><a class="dropdown-item" href="/users/create">إضافة مستخدم</a></li>
                        <?php endif; ?>
                        <?php if ($auth->hasPermission('roles.view')): ?>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/roles">الأدوار والصلاحيات</a></li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- Organizations -->
                <?php if ($auth->hasPermission('organizations.view')): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-building me-1"></i>
                        المنظمات
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/organizations">قائمة المنظمات</a></li>
                        <?php if ($auth->hasPermission('organizations.create')): ?>
                        <li><a class="dropdown-item" href="/organizations/create">إضافة منظمة</a></li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- HR Management -->
                <?php if ($auth->hasPermission('employees.view') || $auth->hasPermission('volunteers.view')): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-tie me-1"></i>
                        الموارد البشرية
                    </a>
                    <ul class="dropdown-menu">
                        <?php if ($auth->hasPermission('employees.view')): ?>
                        <li><a class="dropdown-item" href="/employees">الموظفين</a></li>
                        <?php endif; ?>
                        <?php if ($auth->hasPermission('volunteers.view')): ?>
                        <li><a class="dropdown-item" href="/volunteers">المتطوعين</a></li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- Projects -->
                <?php if ($auth->hasPermission('projects.view')): ?>
                <li class="nav-item">
                    <a class="nav-link" href="/projects">
                        <i class="fas fa-project-diagram me-1"></i>
                        المشاريع
                    </a>
                </li>
                <?php endif; ?>

                <!-- Financial Management -->
                <?php if ($auth->hasPermission('finance.view') || $auth->hasPermission('donations.view')): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-chart-line me-1"></i>
                        الشؤون المالية
                    </a>
                    <ul class="dropdown-menu">
                        <?php if ($auth->hasPermission('finance.view')): ?>
                        <li><a class="dropdown-item" href="/finance">الحسابات المالية</a></li>
                        <li><a class="dropdown-item" href="/finance/transactions">المعاملات</a></li>
                        <li><a class="dropdown-item" href="/finance/budget">الميزانية</a></li>
                        <?php endif; ?>
                        <?php if ($auth->hasPermission('donations.view')): ?>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/donations">التبرعات</a></li>
                        <?php endif; ?>
                        <?php if ($auth->hasPermission('inventory.view')): ?>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/inventory">المخزون</a></li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- Meetings & Contracts -->
                <?php if ($auth->hasPermission('meetings.view') || $auth->hasPermission('contracts.view')): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-handshake me-1"></i>
                        الإدارة
                    </a>
                    <ul class="dropdown-menu">
                        <?php if ($auth->hasPermission('meetings.view')): ?>
                        <li><a class="dropdown-item" href="/meetings">الاجتماعات</a></li>
                        <li><a class="dropdown-item" href="/meetings/calendar">التقويم</a></li>
                        <?php endif; ?>
                        <?php if ($auth->hasPermission('contracts.view')): ?>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/contracts">العقود</a></li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- Reports -->
                <?php if ($auth->hasPermission('reports.view')): ?>
                <li class="nav-item">
                    <a class="nav-link" href="/reports">
                        <i class="fas fa-chart-bar me-1"></i>
                        التقارير
                    </a>
                </li>
                <?php endif; ?>
            </ul>

            <!-- Right Side Navigation -->
            <ul class="navbar-nav">
                <!-- Notifications -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle position-relative" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <?php if (isset($notifications) && count($notifications) > 0): ?>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            <?= count($notifications) ?>
                        </span>
                        <?php endif; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                        <li><h6 class="dropdown-header">الإشعارات</h6></li>
                        <?php if (isset($notifications) && count($notifications) > 0): ?>
                            <?php foreach ($notifications as $notification): ?>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <div class="notification-item">
                                        <strong><?= htmlspecialchars($notification['title']) ?></strong>
                                        <p class="mb-1"><?= htmlspecialchars($notification['message']) ?></p>
                                        <small class="text-muted"><?= date('Y-m-d H:i', strtotime($notification['created_at'])) ?></small>
                                    </div>
                                </a>
                            </li>
                            <?php endforeach; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="/notifications">عرض جميع الإشعارات</a></li>
                        <?php else: ?>
                            <li><span class="dropdown-item-text">لا توجد إشعارات جديدة</span></li>
                        <?php endif; ?>
                    </ul>
                </li>

                <!-- User Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <?php if ($user['avatar']): ?>
                        <img src="/<?= htmlspecialchars($user['avatar']) ?>" alt="الصورة الشخصية" class="rounded-circle me-1" width="24" height="24">
                        <?php else: ?>
                        <i class="fas fa-user-circle me-1"></i>
                        <?php endif; ?>
                        <?= htmlspecialchars($user['name']) ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/dashboard/profile">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="/dashboard/settings">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a></li>
                        <?php if ($auth->hasPermission('settings.view')): ?>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/settings">
                            <i class="fas fa-tools me-2"></i>إعدادات النظام
                        </a></li>
                        <?php endif; ?>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="/auth/logout" class="d-inline">
                                <?= \App\Middleware\CsrfMiddleware::getTokenField() ?>
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </button>
                            </form>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>
