<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وضع الصيانة - نظام إدارة المؤسسات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Tajawal', sans-serif;
        }
        
        .maintenance-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 2rem;
        }
        
        .maintenance-icon {
            font-size: 8rem;
            margin-bottom: 2rem;
            opacity: 0.8;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .maintenance-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .maintenance-subtitle {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .maintenance-message {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }
        
        .countdown {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .countdown-item {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 1rem;
            min-width: 80px;
        }
        
        .countdown-number {
            font-size: 2rem;
            font-weight: 700;
            display: block;
        }
        
        .countdown-label {
            font-size: 0.875rem;
            opacity: 0.8;
        }
        
        .contact-info {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .contact-item {
            display: inline-block;
            margin: 0 1rem;
            opacity: 0.8;
        }
        
        .progress-bar {
            background: rgba(255, 255, 255, 0.2);
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
            margin: 2rem 0;
        }
        
        .progress-fill {
            background: #28a745;
            height: 100%;
            width: 75%;
            border-radius: 3px;
            animation: progress 3s ease-in-out infinite;
        }
        
        @keyframes progress {
            0%, 100% { width: 75%; }
            50% { width: 85%; }
        }
        
        @media (max-width: 768px) {
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-icon {
                font-size: 5rem;
            }
            
            .countdown {
                flex-wrap: wrap;
            }
            
            .countdown-item {
                min-width: 60px;
                padding: 0.75rem;
            }
            
            .countdown-number {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <!-- أيقونة الصيانة -->
        <div class="maintenance-icon">
            <i class="fas fa-tools"></i>
        </div>
        
        <!-- العنوان الرئيسي -->
        <h1 class="maintenance-title">وضع الصيانة</h1>
        <p class="maintenance-subtitle">نعمل على تحسين النظام لخدمتك بشكل أفضل</p>
        
        <!-- رسالة الصيانة -->
        <div class="maintenance-message">
            <h4><i class="fas fa-info-circle me-2"></i>ما يحدث الآن؟</h4>
            <p>نقوم حالياً بتطبيق تحديثات مهمة على النظام لتحسين الأداء والأمان. سيكون النظام متاحاً قريباً بمميزات جديدة ومحسنة.</p>
            
            <!-- شريط التقدم -->
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <small>جاري العمل... 75% مكتمل</small>
        </div>
        
        <!-- العد التنازلي -->
        <div class="countdown" id="countdown">
            <div class="countdown-item">
                <span class="countdown-number" id="hours">02</span>
                <span class="countdown-label">ساعة</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="minutes">30</span>
                <span class="countdown-label">دقيقة</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="seconds">45</span>
                <span class="countdown-label">ثانية</span>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="maintenance-message">
            <h5><i class="fas fa-rocket me-2"></i>ما الجديد؟</h5>
            <ul class="text-start">
                <li>تحسينات في الأداء والسرعة</li>
                <li>واجهة مستخدم محدثة</li>
                <li>ميزات أمان إضافية</li>
                <li>تقارير جديدة ومحسنة</li>
                <li>إصلاحات للمشاكل المعروفة</li>
            </ul>
        </div>
        
        <!-- معلومات الاتصال -->
        <div class="contact-info">
            <h6><i class="fas fa-headset me-2"></i>هل تحتاج مساعدة؟</h6>
            <div class="contact-item">
                <i class="fas fa-envelope me-1"></i>
                <EMAIL>
            </div>
            <div class="contact-item">
                <i class="fas fa-phone me-1"></i>
                +966 11 123 4567
            </div>
            <div class="contact-item">
                <i class="fas fa-globe me-1"></i>
                www.nonprofit-cms.com
            </div>
        </div>
        
        <!-- روابط التواصل الاجتماعي -->
        <div class="mt-4">
            <a href="#" class="text-white me-3" style="font-size: 1.5rem;">
                <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="text-white me-3" style="font-size: 1.5rem;">
                <i class="fab fa-linkedin"></i>
            </a>
            <a href="#" class="text-white" style="font-size: 1.5rem;">
                <i class="fab fa-github"></i>
            </a>
        </div>
        
        <!-- حقوق النشر -->
        <div class="mt-4 pt-3" style="border-top: 1px solid rgba(255, 255, 255, 0.2); opacity: 0.7;">
            <small>&copy; 2024 نظام إدارة المؤسسات غير الربحية. جميع الحقوق محفوظة.</small>
        </div>
    </div>

    <script>
        // العد التنازلي
        function updateCountdown() {
            // تحديد وقت انتهاء الصيانة (2.5 ساعة من الآن)
            const endTime = new Date().getTime() + (2.5 * 60 * 60 * 1000);
            
            function update() {
                const now = new Date().getTime();
                const timeLeft = endTime - now;
                
                if (timeLeft > 0) {
                    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                    
                    document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                    document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                    document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
                } else {
                    // انتهت الصيانة، إعادة تحميل الصفحة
                    location.reload();
                }
            }
            
            update();
            setInterval(update, 1000);
        }
        
        // بدء العد التنازلي
        updateCountdown();
        
        // تحديث الصفحة كل 5 دقائق للتحقق من انتهاء الصيانة
        setInterval(() => {
            fetch(window.location.href)
                .then(response => {
                    if (response.status !== 503) {
                        location.reload();
                    }
                })
                .catch(() => {
                    // تجاهل الأخطاء
                });
        }, 5 * 60 * 1000);
        
        // تأثيرات بصرية إضافية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الجسيمات المتحركة
            createParticles();
        });
        
        function createParticles() {
            const particleCount = 50;
            const particles = [];
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'fixed';
                particle.style.width = '2px';
                particle.style.height = '2px';
                particle.style.background = 'rgba(255, 255, 255, 0.5)';
                particle.style.borderRadius = '50%';
                particle.style.pointerEvents = 'none';
                particle.style.zIndex = '-1';
                
                document.body.appendChild(particle);
                particles.push({
                    element: particle,
                    x: Math.random() * window.innerWidth,
                    y: Math.random() * window.innerHeight,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2
                });
            }
            
            function animateParticles() {
                particles.forEach(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    
                    if (particle.x < 0 || particle.x > window.innerWidth) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > window.innerHeight) particle.vy *= -1;
                    
                    particle.element.style.left = particle.x + 'px';
                    particle.element.style.top = particle.y + 'px';
                });
                
                requestAnimationFrame(animateParticles);
            }
            
            animateParticles();
        }
    </script>
</body>
</html>
