<?php

namespace App\Controllers;

use App\Models\User;
use App\Models\Organization;

class DashboardController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->requireAuth();
    }

    public function index(): void
    {
        $user = $this->auth->user();
        
        // إحصائيات عامة
        $stats = $this->getDashboardStats();
        
        // الأنشطة الأخيرة
        $recentActivities = $this->getRecentActivities();
        
        // الإشعارات
        $notifications = $this->getUnreadNotifications();
        
        // المهام المعلقة
        $pendingTasks = $this->getPendingTasks();
        
        $this->view('dashboard.index', [
            'stats' => $stats,
            'recentActivities' => $recentActivities,
            'notifications' => $notifications,
            'pendingTasks' => $pendingTasks,
        ]);
    }

    public function profile(): void
    {
        $user = $this->auth->user();
        $this->view('dashboard.profile', ['user' => $user]);
    }

    public function updateProfile(): void
    {
        $user = $this->auth->user();
        
        $data = $this->validate($_POST, [
            'name' => 'required|string|min:2|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'phone',
            'department' => 'string|max:100',
            'position' => 'string|max:100',
        ]);

        // التحقق من عدم تكرار البريد الإلكتروني
        $userModel = new User();
        $existingUser = $userModel->findByEmail($data['email']);
        
        if ($existingUser && $existingUser['id'] != $user['id']) {
            $this->flash('error', 'البريد الإلكتروني مستخدم من قبل مستخدم آخر');
            $this->back();
        }

        // رفع الصورة الشخصية إذا تم اختيارها
        if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
            $avatarPath = $this->uploadFile($_FILES['avatar'], 'avatars');
            if ($avatarPath) {
                // حذف الصورة القديمة
                if ($user['avatar']) {
                    $this->deleteFile($user['avatar']);
                }
                $data['avatar'] = $avatarPath;
            }
        }

        $userModel->update($user['id'], $data);
        
        $this->flash('success', 'تم تحديث الملف الشخصي بنجاح');
        $this->redirect('/dashboard/profile');
    }

    public function changePassword(): void
    {
        $user = $this->auth->user();
        
        $data = $this->validate($_POST, [
            'current_password' => 'required',
            'new_password' => 'required|min:8|confirmed',
        ]);

        $userModel = new User();
        
        // التحقق من كلمة المرور الحالية
        if (!$userModel->validatePassword($data['current_password'], $user['password'])) {
            $this->flash('error', 'كلمة المرور الحالية غير صحيحة');
            $this->back();
        }

        $userModel->updatePassword($user['id'], $data['new_password']);
        
        $this->flash('success', 'تم تغيير كلمة المرور بنجاح');
        $this->redirect('/dashboard/profile');
    }

    private function getDashboardStats(): array
    {
        $userModel = new User();
        $organizationModel = new Organization();
        
        $stats = [
            'total_users' => $userModel->count(),
            'active_users' => count($userModel->getActiveUsers()),
            'total_organizations' => $organizationModel->count(),
            'active_organizations' => count($organizationModel->getActiveOrganizations()),
        ];

        // إحصائيات إضافية حسب دور المستخدم
        $user = $this->auth->user();
        
        if ($this->auth->hasRole('super_admin') || $this->auth->hasRole('admin')) {
            // إحصائيات شاملة للمديرين
            $stats['new_users_this_month'] = $this->getNewUsersThisMonth();
            $stats['pending_approvals'] = $this->getPendingApprovalsCount();
        } elseif ($this->auth->hasRole('organization_admin')) {
            // إحصائيات المنظمة
            $stats['organization_employees'] = $this->getOrganizationEmployeesCount($user['organization_id']);
            $stats['organization_projects'] = $this->getOrganizationProjectsCount($user['organization_id']);
        }

        return $stats;
    }

    private function getRecentActivities(): array
    {
        // جلب الأنشطة الأخيرة من سجل التدقيق
        $sql = "SELECT al.*, u.name as user_name 
                FROM audit_logs al 
                LEFT JOIN users u ON al.user_id = u.id 
                ORDER BY al.created_at DESC 
                LIMIT 10";
        
        $connection = \Core\Database\Connection::getInstance();
        return $connection->fetchAll($sql);
    }

    private function getUnreadNotifications(): array
    {
        $user = $this->auth->user();
        
        $sql = "SELECT * FROM notifications 
                WHERE user_id = :user_id AND is_read = 0 
                ORDER BY created_at DESC 
                LIMIT 5";
        
        $connection = \Core\Database\Connection::getInstance();
        return $connection->fetchAll($sql, ['user_id' => $user['id']]);
    }

    private function getPendingTasks(): array
    {
        $user = $this->auth->user();
        $tasks = [];

        // المهام حسب دور المستخدم
        if ($this->auth->hasRole('super_admin') || $this->auth->hasRole('admin')) {
            // مهام المديرين
            $tasks = array_merge($tasks, [
                'pending_user_approvals' => $this->getPendingUserApprovals(),
                'pending_organization_approvals' => $this->getPendingOrganizationApprovals(),
            ]);
        }

        if ($this->auth->hasRole('hr_manager')) {
            // مهام مدير الموارد البشرية
            $tasks = array_merge($tasks, [
                'pending_employee_requests' => $this->getPendingEmployeeRequests(),
                'expiring_contracts' => $this->getExpiringContracts(),
            ]);
        }

        if ($this->auth->hasRole('finance_manager')) {
            // مهام المدير المالي
            $tasks = array_merge($tasks, [
                'pending_financial_approvals' => $this->getPendingFinancialApprovals(),
                'overdue_payments' => $this->getOverduePayments(),
            ]);
        }

        return $tasks;
    }

    private function getNewUsersThisMonth(): int
    {
        $sql = "SELECT COUNT(*) as count FROM users 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
        
        $connection = \Core\Database\Connection::getInstance();
        $result = $connection->fetch($sql);
        return $result['count'] ?? 0;
    }

    private function getPendingApprovalsCount(): int
    {
        // عدد الموافقات المعلقة من جميع الوحدات
        $count = 0;
        
        // موافقات المستخدمين
        $sql = "SELECT COUNT(*) as count FROM users WHERE is_active = 0";
        $connection = \Core\Database\Connection::getInstance();
        $result = $connection->fetch($sql);
        $count += $result['count'] ?? 0;
        
        // موافقات المنظمات
        $sql = "SELECT COUNT(*) as count FROM organizations WHERE legal_status = 'pending'";
        $result = $connection->fetch($sql);
        $count += $result['count'] ?? 0;
        
        return $count;
    }

    private function getOrganizationEmployeesCount(int $organizationId): int
    {
        $sql = "SELECT COUNT(*) as count FROM users 
                WHERE organization_id = :org_id AND is_active = 1";
        
        $connection = \Core\Database\Connection::getInstance();
        $result = $connection->fetch($sql, ['org_id' => $organizationId]);
        return $result['count'] ?? 0;
    }

    private function getOrganizationProjectsCount(int $organizationId): int
    {
        $sql = "SELECT COUNT(*) as count FROM projects 
                WHERE organization_id = :org_id AND status = 'active'";
        
        $connection = \Core\Database\Connection::getInstance();
        $result = $connection->fetch($sql, ['org_id' => $organizationId]);
        return $result['count'] ?? 0;
    }

    private function getPendingUserApprovals(): array
    {
        $sql = "SELECT id, name, email, created_at FROM users 
                WHERE is_active = 0 
                ORDER BY created_at DESC 
                LIMIT 5";
        
        $connection = \Core\Database\Connection::getInstance();
        return $connection->fetchAll($sql);
    }

    private function getPendingOrganizationApprovals(): array
    {
        $sql = "SELECT id, name, license_number, created_at FROM organizations 
                WHERE legal_status = 'pending' 
                ORDER BY created_at DESC 
                LIMIT 5";
        
        $connection = \Core\Database\Connection::getInstance();
        return $connection->fetchAll($sql);
    }

    private function getPendingEmployeeRequests(): array
    {
        // تنفيذ جلب طلبات الموظفين المعلقة
        return [];
    }

    private function getExpiringContracts(): array
    {
        // تنفيذ جلب العقود منتهية الصلاحية
        return [];
    }

    private function getPendingFinancialApprovals(): array
    {
        // تنفيذ جلب الموافقات المالية المعلقة
        return [];
    }

    private function getOverduePayments(): array
    {
        // تنفيذ جلب المدفوعات المتأخرة
        return [];
    }
}
