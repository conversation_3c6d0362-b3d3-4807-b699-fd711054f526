<?php
session_start();

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];

    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: /login.php');
    exit;
}

// التحقق من وجود اسم المستخدم في الجلسة
if (!isset($_SESSION['user_name'])) {
    $user_stmt = $pdo->prepare("SELECT name FROM users WHERE id = ?");
    $user_stmt->execute([$_SESSION['user_id']]);
    $user_data = $user_stmt->fetch();
    if ($user_data) {
        $_SESSION['user_name'] = $user_data['name'];
    }
}

$success = '';
$error = '';

// جلب معلومات الموظف الحالي
$current_employee = null;
$stmt = $pdo->prepare("SELECT * FROM employees WHERE user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$current_employee = $stmt->fetch();

// إذا لم يكن المستخدم مرتبط بموظف، إنشاء موظف افتراضي
if (!$current_employee) {
    $user_stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $user_stmt->execute([$_SESSION['user_id']]);
    $user = $user_stmt->fetch();

    if ($user) {
        $default_dept = $pdo->query("SELECT id FROM departments LIMIT 1")->fetchColumn();
        $default_pos = $pdo->query("SELECT id FROM positions LIMIT 1")->fetchColumn();

        $create_employee = $pdo->prepare("
            INSERT INTO employees (user_id, employee_id, department_id, position_id, hire_date, salary, vacation_balance, sick_leave_balance)
            VALUES (?, ?, ?, ?, CURDATE(), 5000, 30, 15)
        ");

        $employee_id = 'EMP' . str_pad($_SESSION['user_id'], 4, '0', STR_PAD_LEFT);
        $create_employee->execute([$_SESSION['user_id'], $employee_id, $default_dept, $default_pos]);

        $stmt->execute([$_SESSION['user_id']]);
        $current_employee = $stmt->fetch();
    }
}

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'send_message') {
        try {
            // التحقق من البيانات المطلوبة
            $required_fields = ['subject', 'message'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("جميع الحقول المطلوبة يجب ملؤها");
                }
            }

            $pdo->beginTransaction();

            // جلب التوقيع الافتراضي للمستخدم
            $signature_stmt = $pdo->prepare("SELECT signature_content FROM user_signatures WHERE user_id = ? AND is_default = 1");
            $signature_stmt->execute([$_SESSION['user_id']]);
            $signature = $signature_stmt->fetchColumn();

            // إضافة التوقيع للرسالة إذا كان مفعلاً
            $message_content = $_POST['message'];
            if (!empty($_POST['add_signature']) && $signature) {
                $message_content .= "\n\n" . $signature;
            }

            // إدراج الرسالة
            $stmt = $pdo->prepare("
                INSERT INTO internal_messages (sender_id, subject, message, priority, signature, has_attachments, created_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");

            $has_attachments = !empty($_FILES['attachments']['name'][0]);

            $stmt->execute([
                $current_employee['id'],
                $_POST['subject'],
                $message_content,
                $_POST['priority'] ?? 'normal',
                $signature,
                $has_attachments
            ]);

            $message_id = $pdo->lastInsertId();

            // معالجة المرفقات
            if ($has_attachments) {
                $upload_dir = '../uploads/attachments/';

                foreach ($_FILES['attachments']['name'] as $key => $filename) {
                    if (!empty($filename)) {
                        $file_tmp = $_FILES['attachments']['tmp_name'][$key];
                        $file_size = $_FILES['attachments']['size'][$key];
                        $file_type = $_FILES['attachments']['type'][$key];

                        // التحقق من نوع الملف
                        $allowed_types = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png', 'gif'];
                        $file_ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

                        if (!in_array($file_ext, $allowed_types)) {
                            throw new Exception("نوع الملف غير مسموح: $filename");
                        }

                        // التحقق من حجم الملف (5MB كحد أقصى)
                        if ($file_size > 5 * 1024 * 1024) {
                            throw new Exception("حجم الملف كبير جداً: $filename");
                        }

                        // إنشاء اسم فريد للملف
                        $stored_name = uniqid() . '_' . time() . '.' . $file_ext;
                        $file_path = $upload_dir . $stored_name;

                        if (move_uploaded_file($file_tmp, $file_path)) {
                            // حفظ معلومات المرفق في قاعدة البيانات
                            $attachment_stmt = $pdo->prepare("
                                INSERT INTO message_attachments (message_id, original_name, stored_name, file_path, file_size, file_type)
                                VALUES (?, ?, ?, ?, ?, ?)
                            ");

                            $attachment_stmt->execute([
                                $message_id,
                                $filename,
                                $stored_name,
                                $file_path,
                                $file_size,
                                $file_type
                            ]);
                        }
                    }
                }
            }

            // إضافة المستقبلين
            if (!empty($_POST['recipients'])) {
                $recipients = is_array($_POST['recipients']) ? $_POST['recipients'] : explode(',', $_POST['recipients']);
                $recipient_stmt = $pdo->prepare("
                    INSERT INTO message_recipients (message_id, recipient_id)
                    VALUES (?, ?)
                ");

                foreach ($recipients as $recipient_id) {
                    $recipient_id = trim($recipient_id);
                    if (!empty($recipient_id)) {
                        $recipient_stmt->execute([$message_id, $recipient_id]);

                        // إضافة إشعار للمستقبل
                        $notification_stmt = $pdo->prepare("
                            INSERT INTO notifications (employee_id, type, title, message, related_id)
                            VALUES (?, 'message', ?, ?, ?)
                        ");

                        $notification_stmt->execute([
                            $recipient_id,
                            'رسالة جديدة',
                            'تم استلام رسالة جديدة من ' . $_SESSION['user_name'],
                            $message_id
                        ]);
                    }
                }
            }

            $pdo->commit();
            $success = "تم إرسال الرسالة بنجاح";

        } catch (Exception $e) {
            $pdo->rollBack();
            $error = "خطأ في إرسال الرسالة: " . $e->getMessage();
        }
    }

    elseif ($action === 'mark_read') {
        try {
            $message_id = $_POST['message_id'];
            $stmt = $pdo->prepare("
                UPDATE message_recipients
                SET is_read = 1, read_at = NOW()
                WHERE message_id = ? AND recipient_id = ?
            ");
            $stmt->execute([$message_id, $current_employee['id']]);

            echo json_encode(['success' => true]);
            exit;
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            exit;
        }
    }

    elseif ($action === 'create_signature') {
        try {
            $required_fields = ['signature_name', 'signature_content'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("جميع الحقول المطلوبة يجب ملؤها");
                }
            }

            $pdo->beginTransaction();

            // إذا كان هذا التوقيع افتراضي، إلغاء الافتراضي من التوقيعات الأخرى
            if (!empty($_POST['is_default'])) {
                $pdo->prepare("UPDATE user_signatures SET is_default = 0 WHERE user_id = ?")->execute([$_SESSION['user_id']]);
            }

            $stmt = $pdo->prepare("
                INSERT INTO user_signatures (user_id, signature_name, signature_content, is_default)
                VALUES (?, ?, ?, ?)
            ");

            $stmt->execute([
                $_SESSION['user_id'],
                $_POST['signature_name'],
                $_POST['signature_content'],
                !empty($_POST['is_default']) ? 1 : 0
            ]);

            $pdo->commit();
            $success = "تم إنشاء التوقيع بنجاح";

        } catch (Exception $e) {
            $pdo->rollBack();
            $error = "خطأ في إنشاء التوقيع: " . $e->getMessage();
        }
    }
}

// جلب الرسائل الواردة للموظف الحالي
$inbox_query = "
    SELECT im.*, u.name as sender_name, mr.is_read, mr.read_at,
           (SELECT COUNT(*) FROM message_attachments WHERE message_id = im.id) as attachments_count
    FROM internal_messages im
    JOIN message_recipients mr ON im.id = mr.message_id
    JOIN employees e ON im.sender_id = e.id
    JOIN users u ON e.user_id = u.id
    WHERE mr.recipient_id = ?
    ORDER BY im.created_at DESC
";

$inbox_messages = [];
if ($current_employee) {
    $stmt = $pdo->prepare($inbox_query);
    $stmt->execute([$current_employee['id']]);
    $inbox_messages = $stmt->fetchAll();
}

// جلب الرسائل المرسلة
$sent_query = "
    SELECT im.*, u.name as sender_name,
           (SELECT COUNT(*) FROM message_attachments WHERE message_id = im.id) as attachments_count,
           (SELECT COUNT(*) FROM message_recipients WHERE message_id = im.id) as recipients_count,
           (SELECT COUNT(*) FROM message_recipients WHERE message_id = im.id AND is_read = 1) as read_count
    FROM internal_messages im
    JOIN employees e ON im.sender_id = e.id
    JOIN users u ON e.user_id = u.id
    WHERE im.sender_id = ?
    ORDER BY im.created_at DESC
";

$sent_messages = [];
if ($current_employee) {
    $stmt = $pdo->prepare($sent_query);
    $stmt->execute([$current_employee['id']]);
    $sent_messages = $stmt->fetchAll();
}

// جلب جميع الموظفين للمستقبلين
$employees_query = "
    SELECT e.id, u.name, d.name as department_name, p.title as position_title
    FROM employees e
    JOIN users u ON e.user_id = u.id
    LEFT JOIN departments d ON e.department_id = d.id
    LEFT JOIN positions p ON e.position_id = p.id
    WHERE e.id != ?
    ORDER BY u.name
";

$employees = [];
if ($current_employee) {
    $stmt = $pdo->prepare($employees_query);
    $stmt->execute([$current_employee['id']]);
    $employees = $stmt->fetchAll();
}

// جلب التوقيعات المتاحة للمستخدم
$signatures_query = "SELECT * FROM user_signatures WHERE user_id = ? ORDER BY is_default DESC, signature_name";
$stmt = $pdo->prepare($signatures_query);
$stmt->execute([$_SESSION['user_id']]);
$signatures = $stmt->fetchAll();

// إحصائيات
$stats = [
    'unread_messages' => 0,
    'total_inbox' => count($inbox_messages),
    'total_sent' => count($sent_messages),
    'attachments_received' => 0
];

foreach ($inbox_messages as $msg) {
    if (!$msg['is_read']) {
        $stats['unread_messages']++;
    }
    if ($msg['attachments_count'] > 0) {
        $stats['attachments_received'] += $msg['attachments_count'];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الرسائل المتطور - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #17a2b8, #138496); }
        .stats-card { background: linear-gradient(45deg, #17a2b8, #138496); color: white; border-radius: 15px; }
        .message-card { border-radius: 10px; transition: transform 0.2s; }
        .message-card:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .message-unread { border-left: 4px solid #17a2b8; background-color: #f8f9fa; }
        .message-read { opacity: 0.8; }
        .priority-high { border-left: 4px solid #dc3545; }
        .priority-urgent { border-left: 4px solid #fd7e14; animation: pulse 2s infinite; }
        .priority-normal { border-left: 4px solid #28a745; }
        .priority-low { border-left: 4px solid #6c757d; }
        @keyframes pulse { 0% { opacity: 1; } 50% { opacity: 0.5; } 100% { opacity: 1; } }
        .attachment-icon { color: #17a2b8; }
        .signature-preview { background: #f8f9fa; border: 1px dashed #dee2e6; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-envelope me-2"></i>نظام الرسائل المتطور
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="/users.php">
                    <i class="fas fa-users me-1"></i>المستخدمين
                </a>
                <a class="nav-link" href="/employees.php">
                    <i class="fas fa-user-tie me-1"></i>الموظفين
                </a>
                <a class="nav-link" href="/leave-requests.php">
                    <i class="fas fa-calendar-alt me-1"></i>الإجازات
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-envelope fa-2x mb-2"></i>
                    <h4><?= $stats['unread_messages'] ?></h4>
                    <small>رسائل غير مقروءة</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <h4><?= $stats['total_inbox'] ?></h4>
                    <small>إجمالي الواردة</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-paper-plane fa-2x mb-2"></i>
                    <h4><?= $stats['total_sent'] ?></h4>
                    <small>الرسائل المرسلة</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-paperclip fa-2x mb-2"></i>
                    <h4><?= $stats['attachments_received'] ?></h4>
                    <small>المرفقات المستلمة</small>
                </div>
            </div>
        </div>

        <!-- التبويبات -->
        <ul class="nav nav-tabs" id="messagesTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="compose-tab" data-bs-toggle="tab" data-bs-target="#compose" type="button">
                    <i class="fas fa-edit me-2"></i>إنشاء رسالة
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox" type="button">
                    <i class="fas fa-inbox me-2"></i>الواردة
                    <?php if ($stats['unread_messages'] > 0): ?>
                    <span class="badge bg-danger"><?= $stats['unread_messages'] ?></span>
                    <?php endif; ?>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent" type="button">
                    <i class="fas fa-paper-plane me-2"></i>المرسلة
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="signatures-tab" data-bs-toggle="tab" data-bs-target="#signatures" type="button">
                    <i class="fas fa-signature me-2"></i>التوقيعات
                </button>
            </li>
        </ul>

        <div class="tab-content" id="messagesTabsContent">
            <!-- تبويب إنشاء رسالة -->
            <div class="tab-pane fade show active" id="compose" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-edit me-2"></i>إنشاء رسالة جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="send_message">

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label class="form-label">المستقبلين *</label>
                                        <select name="recipients[]" class="form-select" multiple required size="4">
                                            <?php foreach ($employees as $emp): ?>
                                            <option value="<?= $emp['id'] ?>">
                                                <?= htmlspecialchars($emp['name']) ?> - <?= htmlspecialchars($emp['department_name'] ?? 'غير محدد') ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <small class="text-muted">اضغط Ctrl لاختيار عدة مستقبلين</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">الأولوية</label>
                                        <select name="priority" class="form-select">
                                            <option value="low">منخفضة</option>
                                            <option value="normal" selected>عادية</option>
                                            <option value="high">عالية</option>
                                            <option value="urgent">عاجلة</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الموضوع *</label>
                                <input type="text" name="subject" class="form-control" required placeholder="أدخل موضوع الرسالة">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">نص الرسالة *</label>
                                <textarea name="message" class="form-control" rows="6" required placeholder="اكتب رسالتك هنا..."></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">المرفقات</label>
                                <input type="file" name="attachments[]" class="form-control" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif">
                                <small class="text-muted">يمكنك إرفاق عدة ملفات (الحد الأقصى 5MB لكل ملف)</small>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" name="add_signature" class="form-check-input" id="add_signature" checked>
                                <label class="form-check-label" for="add_signature">إضافة التوقيع الافتراضي</label>
                            </div>

                            <?php if (!empty($signatures)): ?>
                            <div class="mb-3" id="signature_preview">
                                <label class="form-label">معاينة التوقيع:</label>
                                <div class="signature-preview">
                                    <?= $signatures[0]['signature_content'] ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-paper-plane me-2"></i>إرسال الرسالة
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- تبويب الرسائل الواردة -->
            <div class="tab-pane fade" id="inbox" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-inbox me-2"></i>الرسائل الواردة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($inbox_messages)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد رسائل واردة</p>
                        </div>
                        <?php else: ?>
                        <div class="row">
                            <?php foreach ($inbox_messages as $message): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card message-card h-100 <?= $message['is_read'] ? 'message-read' : 'message-unread' ?> priority-<?= $message['priority'] ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0"><?= htmlspecialchars($message['subject']) ?></h6>
                                            <div class="d-flex gap-1">
                                                <?php if ($message['attachments_count'] > 0): ?>
                                                <i class="fas fa-paperclip attachment-icon" title="<?= $message['attachments_count'] ?> مرفقات"></i>
                                                <?php endif; ?>
                                                <?php if (!$message['is_read']): ?>
                                                <span class="badge bg-primary">جديد</span>
                                                <?php endif; ?>
                                                <span class="badge bg-<?=
                                                    $message['priority'] === 'urgent' ? 'danger' :
                                                    ($message['priority'] === 'high' ? 'warning' :
                                                    ($message['priority'] === 'low' ? 'secondary' : 'success'))
                                                ?>">
                                                    <?php
                                                    $priority_labels = [
                                                        'low' => 'منخفضة',
                                                        'normal' => 'عادية',
                                                        'high' => 'عالية',
                                                        'urgent' => 'عاجلة'
                                                    ];
                                                    echo $priority_labels[$message['priority']] ?? $message['priority'];
                                                    ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="small text-muted mb-2">
                                            <div><i class="fas fa-user me-1"></i>من: <?= htmlspecialchars($message['sender_name']) ?></div>
                                            <div><i class="fas fa-calendar me-1"></i><?= date('Y/m/d H:i', strtotime($message['created_at'])) ?></div>
                                            <?php if ($message['is_read']): ?>
                                            <div><i class="fas fa-eye me-1"></i>قُرئت في: <?= date('Y/m/d H:i', strtotime($message['read_at'])) ?></div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="message-content mb-3">
                                            <?= nl2br(htmlspecialchars(substr($message['message'], 0, 150))) ?>
                                            <?php if (strlen($message['message']) > 150): ?>
                                            <span class="text-muted">...</span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="d-flex gap-2">
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewMessage(<?= $message['id'] ?>)">
                                                <i class="fas fa-eye me-1"></i>عرض
                                            </button>
                                            <?php if (!$message['is_read']): ?>
                                            <button class="btn btn-sm btn-outline-success" onclick="markAsRead(<?= $message['id'] ?>)">
                                                <i class="fas fa-check me-1"></i>تحديد كمقروء
                                            </button>
                                            <?php endif; ?>
                                            <?php if ($message['attachments_count'] > 0): ?>
                                            <button class="btn btn-sm btn-outline-info" onclick="viewAttachments(<?= $message['id'] ?>)">
                                                <i class="fas fa-paperclip me-1"></i>المرفقات
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- تبويب الرسائل المرسلة -->
            <div class="tab-pane fade" id="sent" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-paper-plane me-2"></i>الرسائل المرسلة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($sent_messages)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد رسائل مرسلة</p>
                        </div>
                        <?php else: ?>
                        <div class="row">
                            <?php foreach ($sent_messages as $message): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card message-card h-100 priority-<?= $message['priority'] ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0"><?= htmlspecialchars($message['subject']) ?></h6>
                                            <div class="d-flex gap-1">
                                                <?php if ($message['attachments_count'] > 0): ?>
                                                <i class="fas fa-paperclip attachment-icon" title="<?= $message['attachments_count'] ?> مرفقات"></i>
                                                <?php endif; ?>
                                                <span class="badge bg-<?=
                                                    $message['priority'] === 'urgent' ? 'danger' :
                                                    ($message['priority'] === 'high' ? 'warning' :
                                                    ($message['priority'] === 'low' ? 'secondary' : 'success'))
                                                ?>">
                                                    <?php
                                                    $priority_labels = [
                                                        'low' => 'منخفضة',
                                                        'normal' => 'عادية',
                                                        'high' => 'عالية',
                                                        'urgent' => 'عاجلة'
                                                    ];
                                                    echo $priority_labels[$message['priority']] ?? $message['priority'];
                                                    ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="small text-muted mb-2">
                                            <div><i class="fas fa-calendar me-1"></i>أُرسلت في: <?= date('Y/m/d H:i', strtotime($message['created_at'])) ?></div>
                                            <div><i class="fas fa-users me-1"></i>المستقبلين: <?= $message['recipients_count'] ?></div>
                                            <div><i class="fas fa-eye me-1"></i>قُرئت من: <?= $message['read_count'] ?> / <?= $message['recipients_count'] ?></div>
                                        </div>

                                        <div class="message-content mb-3">
                                            <?= nl2br(htmlspecialchars(substr($message['message'], 0, 150))) ?>
                                            <?php if (strlen($message['message']) > 150): ?>
                                            <span class="text-muted">...</span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="d-flex gap-2">
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewMessage(<?= $message['id'] ?>)">
                                                <i class="fas fa-eye me-1"></i>عرض
                                            </button>
                                            <?php if ($message['attachments_count'] > 0): ?>
                                            <button class="btn btn-sm btn-outline-info" onclick="viewAttachments(<?= $message['id'] ?>)">
                                                <i class="fas fa-paperclip me-1"></i>المرفقات
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- تبويب التوقيعات -->
            <div class="tab-pane fade" id="signatures" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-signature me-2"></i>إدارة التوقيعات</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>التوقيعات المحفوظة:</h6>
                                <?php if (empty($signatures)): ?>
                                <p class="text-muted">لا توجد توقيعات محفوظة</p>
                                <?php else: ?>
                                <div class="list-group">
                                    <?php foreach ($signatures as $sig): ?>
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1"><?= htmlspecialchars($sig['signature_name']) ?></h6>
                                                <?php if ($sig['is_default']): ?>
                                                <span class="badge bg-primary">افتراضي</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="previewSignature(<?= $sig['id'] ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" onclick="setDefaultSignature(<?= $sig['id'] ?>)">
                                                    <i class="fas fa-star"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="signature-content mt-2" style="display: none;" id="signature-<?= $sig['id'] ?>">
                                            <?= $sig['signature_content'] ?>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-6">
                                <h6>إنشاء توقيع جديد:</h6>
                                <form method="POST">
                                    <input type="hidden" name="action" value="create_signature">

                                    <div class="mb-3">
                                        <label class="form-label">اسم التوقيع</label>
                                        <input type="text" name="signature_name" class="form-control" required>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">محتوى التوقيع</label>
                                        <textarea name="signature_content" class="form-control" rows="6" required placeholder="أدخل محتوى التوقيع..."></textarea>
                                        <small class="text-muted">يمكنك استخدام HTML للتنسيق</small>
                                    </div>

                                    <div class="mb-3 form-check">
                                        <input type="checkbox" name="is_default" class="form-check-input" id="is_default_sig">
                                        <label class="form-check-label" for="is_default_sig">جعله التوقيع الافتراضي</label>
                                    </div>

                                    <button type="submit" class="btn btn-secondary">
                                        <i class="fas fa-save me-2"></i>حفظ التوقيع
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لعرض الرسالة -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">عرض الرسالة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                    <!-- سيتم تحميل محتوى الرسالة هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal للمرفقات -->
    <div class="modal fade" id="attachmentsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">المرفقات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="attachmentsModalBody">
                    <!-- سيتم تحميل المرفقات هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديد الرسالة كمقروءة
        function markAsRead(messageId) {
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=mark_read&message_id=${messageId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('خطأ في تحديث الرسالة');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('خطأ في الاتصال');
            });
        }

        // عرض الرسالة
        function viewMessage(messageId) {
            window.open(`/view-message.php?id=${messageId}`, '_blank');
        }

        // عرض المرفقات
        function viewAttachments(messageId) {
            fetch(`/get-attachments.php?message_id=${messageId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let attachmentsHtml = '';
                        data.attachments.forEach(attachment => {
                            attachmentsHtml += `
                                <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                                    <div>
                                        <i class="fas fa-file me-2"></i>
                                        <strong>${attachment.original_name}</strong><br>
                                        <small class="text-muted">الحجم: ${(attachment.file_size / 1024).toFixed(2)} KB</small>
                                    </div>
                                    <a href="/download-attachment.php?id=${attachment.id}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-download me-1"></i>تحميل
                                    </a>
                                </div>
                            `;
                        });

                        document.getElementById('attachmentsModalBody').innerHTML = attachmentsHtml;
                        new bootstrap.Modal(document.getElementById('attachmentsModal')).show();
                    } else {
                        alert('خطأ في جلب المرفقات');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطأ في الاتصال');
                });
        }

        // معاينة التوقيع
        function previewSignature(signatureId) {
            const signatureElement = document.getElementById(`signature-${signatureId}`);
            if (signatureElement.style.display === 'none') {
                signatureElement.style.display = 'block';
            } else {
                signatureElement.style.display = 'none';
            }
        }

        // تعيين التوقيع الافتراضي
        function setDefaultSignature(signatureId) {
            // سيتم تنفيذ هذا لاحقاً
            alert('سيتم إضافة تعيين التوقيع الافتراضي قريباً');
        }

        // إظهار/إخفاء معاينة التوقيع
        document.getElementById('add_signature').addEventListener('change', function() {
            const signaturePreview = document.getElementById('signature_preview');
            if (signaturePreview) {
                signaturePreview.style.display = this.checked ? 'block' : 'none';
            }
        });

        // تحديث عداد الملفات المرفقة
        document.querySelector('input[name="attachments[]"]').addEventListener('change', function() {
            const fileCount = this.files.length;
            const label = this.nextElementSibling;
            if (fileCount > 0) {
                label.textContent = `تم اختيار ${fileCount} ملف(ات)`;
                label.classList.add('text-success');
            } else {
                label.textContent = 'يمكنك إرفاق عدة ملفات (الحد الأقصى 5MB لكل ملف)';
                label.classList.remove('text-success');
            }
        });

        // تحديث الصفحة كل 30 ثانية للرسائل الجديدة
        setInterval(function() {
            // فقط إذا كان المستخدم في تبويب الواردة
            const inboxTab = document.getElementById('inbox-tab');
            if (inboxTab && inboxTab.classList.contains('active')) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>