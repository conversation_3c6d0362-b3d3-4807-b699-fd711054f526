<?php
// إدارة المقالات - منفصل عن الأخبار
define('ROOT_PATH', dirname(__DIR__));
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: /login.php');
    exit;
}

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// إعداد قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

$success = '';
$error = '';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'add_article') {
        try {
            $title = trim($_POST['title']);
            $excerpt = trim($_POST['excerpt']);
            $content = trim($_POST['content']);
            $category_id = (int)$_POST['category_id'];
            $status = $_POST['status'];
            $is_featured = isset($_POST['is_featured']) ? 1 : 0;
            $is_sticky = isset($_POST['is_sticky']) ? 1 : 0;
            $meta_title = trim($_POST['meta_title']);
            $meta_description = trim($_POST['meta_description']);
            $tags = trim($_POST['tags']);
            
            if (empty($title) || empty($content)) {
                throw new Exception('يجب ملء العنوان والمحتوى');
            }
            
            // إنشاء slug
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
            
            // معالجة رفع الصورة المميزة
            $featured_image = null;
            if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = 'uploads/articles/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                $file_info = pathinfo($_FILES['featured_image']['name']);
                $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                
                if (in_array(strtolower($file_info['extension']), $allowed_types)) {
                    $file_name = uniqid() . '.' . $file_info['extension'];
                    $file_path = $upload_dir . $file_name;
                    
                    if (move_uploaded_file($_FILES['featured_image']['tmp_name'], $file_path)) {
                        $featured_image = 'uploads/articles/' . $file_name;
                    }
                }
            }
            
            // حساب وقت القراءة (تقريبي)
            $word_count = str_word_count(strip_tags($content));
            $reading_time = max(1, ceil($word_count / 200)); // 200 كلمة في الدقيقة
            
            $published_at = ($status === 'published') ? date('Y-m-d H:i:s') : null;
            
            // إدراج المقال
            $stmt = $pdo->prepare("
                INSERT INTO articles 
                (title, slug, excerpt, content, featured_image, category_id, author_id, status, 
                 is_featured, is_sticky, reading_time, meta_title, meta_description, tags, published_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $title, $slug, $excerpt, $content, $featured_image, $category_id, 
                $_SESSION['user_id'], $status, $is_featured, $is_sticky, $reading_time, 
                $meta_title, $meta_description, $tags, $published_at
            ]);
            
            $success = 'تم إضافة المقال بنجاح';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// جلب التصنيفات
$categories = $pdo->query("SELECT * FROM article_categories WHERE status = 'active' ORDER BY sort_order, name")->fetchAll();

// جلب المقالات
$articles_query = "
    SELECT a.*, c.name as category_name, c.color as category_color, u.name as author_name
    FROM articles a
    LEFT JOIN article_categories c ON a.category_id = c.id
    LEFT JOIN users u ON a.author_id = u.id
    ORDER BY a.created_at DESC
    LIMIT 20
";
$articles = $pdo->query($articles_query)->fetchAll();

// إحصائيات
$stats = [
    'total_articles' => $pdo->query("SELECT COUNT(*) FROM articles")->fetchColumn(),
    'published_articles' => $pdo->query("SELECT COUNT(*) FROM articles WHERE status = 'published'")->fetchColumn(),
    'draft_articles' => $pdo->query("SELECT COUNT(*) FROM articles WHERE status = 'draft'")->fetchColumn(),
    'total_views' => $pdo->query("SELECT SUM(views_count) FROM articles")->fetchColumn() ?: 0
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المقالات - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #17a2b8, #6f42c1); }
        .stats-card { background: linear-gradient(45deg, #17a2b8, #6f42c1); color: white; border-radius: 15px; }
        .article-card { border-radius: 10px; transition: transform 0.2s; }
        .article-card:hover { transform: translateY(-2px); }
        .category-badge { border-radius: 20px; }
        .editor-toolbar { background: #f8f9fa; border-radius: 5px; padding: 10px; margin-bottom: 10px; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-file-alt me-2"></i>إدارة المقالات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="/articles.php" target="_blank">
                    <i class="fas fa-eye me-1"></i>عرض المقالات
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <h4><?= $stats['total_articles'] ?></h4>
                    <small>إجمالي المقالات</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4><?= $stats['published_articles'] ?></h4>
                    <small>منشورة</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-edit fa-2x mb-2"></i>
                    <h4><?= $stats['draft_articles'] ?></h4>
                    <small>مسودات</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-eye fa-2x mb-2"></i>
                    <h4><?= number_format($stats['total_views']) ?></h4>
                    <small>إجمالي المشاهدات</small>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addArticleModal">
                            <i class="fas fa-plus me-2"></i>إضافة مقال جديد
                        </button>
                        <a href="/article-categories.php" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-tags me-2"></i>إدارة التصنيفات
                        </a>
                        <a href="/articles.php" class="btn btn-outline-info" target="_blank">
                            <i class="fas fa-eye me-2"></i>عرض المقالات للزوار
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة المقالات -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-list me-2"></i>المقالات الحديثة</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
            <div class="card-body">
                <?php if (empty($articles)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد مقالات بعد</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addArticleModal">
                        <i class="fas fa-plus me-2"></i>إضافة أول مقال
                    </button>
                </div>
                <?php else: ?>
                <div class="row">
                    <?php foreach ($articles as $article): ?>
                    <div class="col-md-6 mb-3">
                        <div class="card article-card h-100">
                            <?php if ($article['featured_image']): ?>
                            <img src="/<?= $article['featured_image'] ?>" class="card-img-top" style="height: 200px; object-fit: cover;">
                            <?php endif; ?>
                            
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title"><?= htmlspecialchars($article['title']) ?></h6>
                                    <div>
                                        <?php if ($article['is_featured']): ?>
                                        <span class="badge bg-warning me-1">
                                            <i class="fas fa-star"></i>
                                        </span>
                                        <?php endif; ?>
                                        <?php if ($article['is_sticky']): ?>
                                        <span class="badge bg-info me-1">
                                            <i class="fas fa-thumbtack"></i>
                                        </span>
                                        <?php endif; ?>
                                        <span class="badge" style="background-color: <?= $article['category_color'] ?>">
                                            <?= htmlspecialchars($article['category_name']) ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <?php if ($article['excerpt']): ?>
                                <p class="card-text small text-muted">
                                    <?= htmlspecialchars(substr($article['excerpt'], 0, 100)) ?>...
                                </p>
                                <?php endif; ?>
                                
                                <div class="small text-muted mb-2">
                                    <div><i class="fas fa-user me-1"></i><?= htmlspecialchars($article['author_name']) ?></div>
                                    <div><i class="fas fa-calendar me-1"></i><?= date('Y-m-d', strtotime($article['created_at'])) ?></div>
                                    <div><i class="fas fa-clock me-1"></i><?= $article['reading_time'] ?> دقيقة قراءة</div>
                                    <div><i class="fas fa-eye me-1"></i><?= number_format($article['views_count']) ?> مشاهدة</div>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="btn-group btn-group-sm">
                                        <a href="/article.php?id=<?= $article['id'] ?>" class="btn btn-outline-primary" target="_blank">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    <span class="badge bg-<?= $article['status'] === 'published' ? 'success' : 'secondary' ?>">
                                        <?= $article['status'] === 'published' ? 'منشور' : 'مسودة' ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal إضافة مقال -->
    <div class="modal fade" id="addArticleModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>إضافة مقال جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="add_article">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">عنوان المقال *</label>
                                    <input type="text" name="title" class="form-control" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">المقتطف</label>
                                    <textarea name="excerpt" class="form-control" rows="3" 
                                              placeholder="ملخص قصير عن المقال"></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">محتوى المقال *</label>
                                    <div class="editor-toolbar">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="formatText('bold')">
                                            <i class="fas fa-bold"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="formatText('italic')">
                                            <i class="fas fa-italic"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="formatText('underline')">
                                            <i class="fas fa-underline"></i>
                                        </button>
                                    </div>
                                    <textarea name="content" class="form-control" rows="15" required 
                                              placeholder="اكتب محتوى المقال هنا..."></textarea>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">التصنيف *</label>
                                    <select name="category_id" class="form-select" required>
                                        <option value="">اختر التصنيف</option>
                                        <?php foreach ($categories as $cat): ?>
                                        <option value="<?= $cat['id'] ?>">
                                            <?= htmlspecialchars($cat['name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">الصورة المميزة</label>
                                    <input type="file" name="featured_image" class="form-control" accept="image/*">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">حالة النشر</label>
                                    <select name="status" class="form-select">
                                        <option value="draft">مسودة</option>
                                        <option value="published">نشر</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" name="is_featured" class="form-check-input" id="is_featured">
                                        <label class="form-check-label" for="is_featured">مقال مميز</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="is_sticky" class="form-check-input" id="is_sticky">
                                        <label class="form-check-label" for="is_sticky">مقال مثبت</label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">عنوان SEO</label>
                                    <input type="text" name="meta_title" class="form-control" 
                                           placeholder="عنوان محسن لمحركات البحث">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">وصف SEO</label>
                                    <textarea name="meta_description" class="form-control" rows="3" 
                                              placeholder="وصف المقال لمحركات البحث"></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">الكلمات المفتاحية</label>
                                    <input type="text" name="tags" class="form-control" 
                                           placeholder="مفصولة بفواصل">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ المقال
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function formatText(command) {
            document.execCommand(command, false, null);
        }
    </script>
</body>
</html>
