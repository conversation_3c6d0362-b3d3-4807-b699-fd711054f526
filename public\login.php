<?php
// نظام إدارة المؤسسات غير الربحية - صفحة تسجيل الدخول

// تحديد مسار الجذر
define('ROOT_PATH', dirname(__DIR__));

// بدء الجلسة
session_start();

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// التحقق من وجود النظام
if (!file_exists(ROOT_PATH . '/.env')) {
    header('Location: /setup.php');
    exit;
}

// إعداد قاعدة البيانات
try {
    // محاولة استخدام ملف التكوين الجديد أولاً
    if (file_exists(ROOT_PATH . '/config/database.php')) {
        $config = require_once ROOT_PATH . '/config/database.php';
        $db_config = $config['connections']['mysql'];

        $pdo = new PDO(
            "mysql:host={$db_config['host']};dbname={$db_config['database']};charset=utf8mb4",
            $db_config['username'],
            $db_config['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]
        );
    } else {
        // استخدام متغيرات البيئة كبديل
        $pdo = new PDO(
            "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
            $_ENV['DB_USERNAME'],
            $_ENV['DB_PASSWORD'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]
        );
    }
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

$error = '';
$success = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($email) || empty($password)) {
        $error = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    } else {
        try {
            $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND is_active = 1");
            $stmt->execute([$email]);
            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // تسجيل دخول ناجح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_name'] = $user['name'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['logged_in'] = true;

                // إعادة توجيه إلى لوحة التحكم
                header('Location: /dashboard.php');
                exit;
            } else {
                $error = 'بيانات تسجيل الدخول غير صحيحة';
            }
        } catch (PDOException $e) {
            $error = 'خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}

// التحقق من تسجيل الدخول المسبق
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']) {
    header('Location: /dashboard.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المؤسسات غير الربحية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 20px;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            max-width: 450px;
            width: 100%;
        }
        .logo-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        .form-control {
            border-radius: 10px;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .demo-accounts {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        .demo-btn {
            font-size: 0.8rem;
            padding: 5px 10px;
            margin: 2px;
        }
    </style>
</head>
<body>
    <div class="container auth-container">
        <div class="row justify-content-center w-100">
            <div class="col-md-6 col-lg-4">
                <div class="login-card">
                    <!-- Logo -->
                    <div class="text-center">
                        <div class="logo-icon">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <h3 class="mb-2">نظام إدارة المؤسسات</h3>
                        <p class="text-muted mb-4">تسجيل الدخول إلى حسابك</p>
                    </div>

                    <!-- Error/Success Messages -->
                    <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= htmlspecialchars($success) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>

                    <!-- Login Form -->
                    <form method="POST" action="/login.php">
                        <!-- Email -->
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email"
                                       class="form-control"
                                       id="email"
                                       name="email"
                                       value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
                                       required
                                       autofocus>
                            </div>
                        </div>

                        <!-- Password -->
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password"
                                       class="form-control"
                                       id="password"
                                       name="password"
                                       required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Remember Me -->
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                تذكرني
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-login btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </div>
                    </form>

                    <!-- Demo Accounts -->
                    <div class="demo-accounts">
                        <h6 class="text-center text-muted mb-3">حسابات تجريبية</h6>
                        <div class="text-center">
                            <button type="button" class="btn btn-outline-primary demo-btn" onclick="fillDemo('<EMAIL>', 'admin123')">
                                <i class="fas fa-user-shield me-1"></i>
                                المدير العام
                            </button>
                            <button type="button" class="btn btn-outline-success demo-btn" onclick="fillDemo('<EMAIL>', '123456')">
                                <i class="fas fa-user-tie me-1"></i>
                                مدير قسم
                            </button>
                            <button type="button" class="btn btn-outline-info demo-btn" onclick="fillDemo('<EMAIL>', '123456')">
                                <i class="fas fa-user me-1"></i>
                                موظف
                            </button>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                انقر على أي حساب لملء البيانات تلقائياً
                            </small>
                        </div>
                    </div>

                    <!-- Additional Links -->
                    <div class="text-center mt-4">
                        <a href="/" class="text-decoration-none me-3">
                            <i class="fas fa-home me-1"></i>
                            الصفحة الرئيسية
                        </a>
                        <a href="/welcome.php" class="text-decoration-none">
                            <i class="fas fa-info-circle me-1"></i>
                            معلومات النظام
                        </a>
                    </div>
                </div>

                <!-- System Info -->
                <div class="text-center mt-3">
                    <small class="text-white-50">
                        نظام إدارة مؤسسات القطاع غير الربحي v1.0
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');

            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Demo account filler
        function fillDemo(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
