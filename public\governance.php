<?php
// صفحة عرض وثائق الحوكمة للزوار
define('ROOT_PATH', dirname(__DIR__));

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// إعداد قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// جلب التصنيفات مع عدد الوثائق
$categories_query = "
    SELECT c.*, COUNT(d.id) as documents_count
    FROM governance_categories c
    LEFT JOIN governance_documents d ON c.id = d.category_id AND d.is_public = 1 AND d.approval_status = 'approved'
    WHERE c.status = 'active'
    GROUP BY c.id
    ORDER BY c.sort_order, c.name
";
$categories = $pdo->query($categories_query)->fetchAll();

// جلب الوثائق العامة المعتمدة
$selected_category = $_GET['category'] ?? '';
$where_clause = "WHERE d.is_public = 1 AND d.approval_status = 'approved'";
$params = [];

if ($selected_category) {
    $where_clause .= " AND c.slug = ?";
    $params[] = $selected_category;
}

$documents_query = "
    SELECT d.*, c.name as category_name, c.color as category_color, c.icon as category_icon
    FROM governance_documents d
    LEFT JOIN governance_categories c ON d.category_id = c.id
    {$where_clause}
    ORDER BY d.effective_date DESC, d.created_at DESC
";

$stmt = $pdo->prepare($documents_query);
$stmt->execute($params);
$documents = $stmt->fetchAll();

// تحديث عداد التحميل عند تحميل ملف
if (isset($_GET['download']) && is_numeric($_GET['download'])) {
    $doc_id = (int)$_GET['download'];
    $pdo->prepare("UPDATE governance_documents SET download_count = download_count + 1 WHERE id = ?")->execute([$doc_id]);
    
    // جلب مسار الملف
    $doc = $pdo->prepare("SELECT file_path, file_name FROM governance_documents WHERE id = ? AND is_public = 1")->execute([$doc_id]);
    $doc = $pdo->fetch();
    
    if ($doc && file_exists($doc['file_path'])) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $doc['file_name'] . '"');
        readfile($doc['file_path']);
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحوكمة والشفافية - معايير المركز الوطني للقطاع غير الربحي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        .category-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            border-left: 5px solid;
        }
        .category-card:hover {
            transform: translateY(-5px);
        }
        .document-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .document-card:hover {
            transform: translateY(-2px);
        }
        .download-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 25px;
            padding: 10px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .download-btn:hover {
            transform: scale(1.05);
            color: white;
        }
        .stats-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <h1 class="display-4 mb-4">
                <i class="fas fa-gavel me-3"></i>
                الحوكمة والشفافية
            </h1>
            <p class="lead">
                وثائق الحوكمة والشفافية وفقاً لمعايير المركز الوطني لتنمية القطاع غير الربحي
            </p>
            <div class="mt-4">
                <a href="/dashboard.php" class="btn btn-light btn-lg me-3">
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                </a>
                <a href="#documents" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-download me-2"></i>تصفح الوثائق
                </a>
            </div>
        </div>
    </div>

    <div class="container mt-5">
        <!-- إحصائيات سريعة -->
        <div class="stats-section">
            <div class="row text-center">
                <div class="col-md-3">
                    <h3 class="text-primary"><?= count($categories) ?></h3>
                    <p class="text-muted">تصنيفات الحوكمة</p>
                </div>
                <div class="col-md-3">
                    <h3 class="text-success"><?= count($documents) ?></h3>
                    <p class="text-muted">الوثائق المتاحة</p>
                </div>
                <div class="col-md-3">
                    <h3 class="text-info"><?= array_sum(array_column($documents, 'download_count')) ?></h3>
                    <p class="text-muted">إجمالي التحميلات</p>
                </div>
                <div class="col-md-3">
                    <h3 class="text-warning">100%</h3>
                    <p class="text-muted">الامتثال للمعايير</p>
                </div>
            </div>
        </div>

        <!-- تصنيفات الحوكمة -->
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-list me-2"></i>
                    تصنيفات الحوكمة
                </h2>
            </div>
        </div>

        <div class="row">
            <?php foreach ($categories as $category): ?>
            <div class="col-md-6 col-lg-4">
                <div class="category-card" style="border-left-color: <?= $category['color'] ?>">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3" style="color: <?= $category['color'] ?>">
                            <i class="<?= $category['icon'] ?> fa-2x"></i>
                        </div>
                        <div>
                            <h5 class="mb-1"><?= htmlspecialchars($category['name']) ?></h5>
                            <small class="text-muted"><?= $category['documents_count'] ?> وثيقة</small>
                        </div>
                    </div>
                    
                    <p class="text-muted mb-3"><?= htmlspecialchars($category['description']) ?></p>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="?category=<?= $category['slug'] ?>#documents" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>عرض الوثائق
                        </a>
                        <?php if ($category['is_required']): ?>
                        <span class="badge bg-danger">مطلوب</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- الوثائق -->
        <div class="row mt-5" id="documents">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-file-alt me-2"></i>
                        الوثائق المتاحة
                        <?php if ($selected_category): ?>
                        <small class="text-muted">- <?= htmlspecialchars($selected_category) ?></small>
                        <?php endif; ?>
                    </h2>
                    
                    <?php if ($selected_category): ?>
                    <a href="/governance.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>إلغاء التصفية
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if (empty($documents)): ?>
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد وثائق متاحة</h4>
                    <p class="text-muted">لم يتم رفع أي وثائق في هذا التصنيف بعد</p>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="row">
            <?php foreach ($documents as $doc): ?>
            <div class="col-md-6 col-lg-4">
                <div class="document-card">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h6 class="mb-0"><?= htmlspecialchars($doc['title']) ?></h6>
                        <span class="badge" style="background-color: <?= $doc['category_color'] ?>">
                            <?= htmlspecialchars($doc['category_name']) ?>
                        </span>
                    </div>
                    
                    <?php if ($doc['description']): ?>
                    <p class="text-muted small mb-3">
                        <?= htmlspecialchars(substr($doc['description'], 0, 100)) ?>...
                    </p>
                    <?php endif; ?>
                    
                    <div class="small text-muted mb-3">
                        <?php if ($doc['version']): ?>
                        <div><i class="fas fa-code-branch me-1"></i>الإصدار: <?= htmlspecialchars($doc['version']) ?></div>
                        <?php endif; ?>
                        
                        <?php if ($doc['effective_date']): ?>
                        <div><i class="fas fa-calendar me-1"></i>تاريخ السريان: <?= date('Y-m-d', strtotime($doc['effective_date'])) ?></div>
                        <?php endif; ?>
                        
                        <?php if ($doc['file_size']): ?>
                        <div><i class="fas fa-file me-1"></i>الحجم: <?= number_format($doc['file_size']/1024, 1) ?> KB</div>
                        <?php endif; ?>
                        
                        <div><i class="fas fa-download me-1"></i>التحميلات: <?= number_format($doc['download_count']) ?></div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <?php if ($doc['file_path']): ?>
                        <a href="?download=<?= $doc['id'] ?>" class="download-btn">
                            <i class="fas fa-download me-1"></i>تحميل
                        </a>
                        <?php else: ?>
                        <span class="text-muted small">لا يوجد ملف</span>
                        <?php endif; ?>
                        
                        <small class="text-muted">
                            <?= date('Y-m-d', strtotime($doc['created_at'])) ?>
                        </small>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- معلومات إضافية -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="stats-section">
                    <h3 class="mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        حول معايير الحوكمة
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>المركز الوطني لتنمية القطاع غير الربحي</h5>
                            <p class="text-muted">
                                يهدف المركز إلى تطوير القطاع غير الربحي في المملكة العربية السعودية من خلال وضع معايير الحوكمة والشفافية التي تضمن الممارسات الجيدة والامتثال للأنظمة.
                            </p>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>أهمية الحوكمة</h5>
                            <ul class="text-muted">
                                <li>تعزيز الشفافية والمساءلة</li>
                                <li>ضمان الامتثال للأنظمة واللوائح</li>
                                <li>تحسين الأداء المؤسسي</li>
                                <li>بناء الثقة مع المانحين والمستفيدين</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-4 mt-5">
        <div class="container">
            <p class="mb-0">
                &copy; 2024 نظام إدارة المؤسسات غير الربحية - جميع الحقوق محفوظة
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
