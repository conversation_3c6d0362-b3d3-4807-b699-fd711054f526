<?php
// صفحة عرض الخبر الواحد
define('ROOT_PATH', dirname(__DIR__));

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// إعداد قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('Location: /news.php');
    exit;
}

// جلب الخبر
try {
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.color as category_color, u.name as author_name 
        FROM news_articles a 
        LEFT JOIN news_categories c ON a.category_id = c.id 
        LEFT JOIN users u ON a.author_id = u.id 
        WHERE a.slug = ? AND a.status = 'published'
    ");
    $stmt->execute([$slug]);
    $article = $stmt->fetch();
    
    if (!$article) {
        header('HTTP/1.0 404 Not Found');
        include '404.php';
        exit;
    }
    
    // زيادة عدد المشاهدات
    $stmt = $pdo->prepare("UPDATE news_articles SET views_count = views_count + 1 WHERE id = ?");
    $stmt->execute([$article['id']]);
    
} catch (PDOException $e) {
    header('Location: /news.php');
    exit;
}

// جلب الأخبار ذات الصلة
try {
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.color as category_color 
        FROM news_articles a 
        LEFT JOIN news_categories c ON a.category_id = c.id 
        WHERE a.status = 'published' AND a.id != ? AND a.category_id = ? 
        ORDER BY a.published_at DESC 
        LIMIT 3
    ");
    $stmt->execute([$article['id'], $article['category_id']]);
    $related_articles = $stmt->fetchAll();
} catch (PDOException $e) {
    $related_articles = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($article['title']) ?> - نظام إدارة المؤسسات</title>
    <meta name="description" content="<?= htmlspecialchars($article['excerpt'] ?? '') ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            line-height: 1.8;
        }
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .article-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 50px;
        }
        .article-content {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
        .article-meta {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .category-badge {
            font-size: 0.9rem;
            padding: 6px 15px;
            border-radius: 20px;
            color: white;
            text-decoration: none;
        }
        .featured-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            font-weight: bold;
        }
        .related-article {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .related-article:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .share-buttons .btn {
            margin: 5px;
            border-radius: 25px;
        }
        .content-text {
            font-size: 1.1rem;
            color: #333;
        }
        .content-text h1, .content-text h2, .content-text h3 {
            color: #667eea;
            margin-top: 30px;
            margin-bottom: 20px;
        }
        .content-text p {
            margin-bottom: 20px;
        }
        .content-text ul, .content-text ol {
            margin-bottom: 20px;
            padding-right: 30px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand text-primary fw-bold" href="/">
                <i class="fas fa-hands-helping me-2"></i>
                نظام إدارة المؤسسات
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/news.php">الأخبار</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/login.php">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            تسجيل الدخول
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Article Header -->
    <section class="article-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <div class="mb-3">
                        <?php if ($article['category_name']): ?>
                        <a href="/news.php?category=<?= $article['category_id'] ?>" 
                           class="category-badge me-2" style="background-color: <?= $article['category_color'] ?>">
                            <?= htmlspecialchars($article['category_name']) ?>
                        </a>
                        <?php endif; ?>
                        
                        <?php if ($article['is_featured']): ?>
                        <span class="badge featured-badge">
                            <i class="fas fa-star me-1"></i>مميز
                        </span>
                        <?php endif; ?>
                    </div>
                    
                    <h1 class="display-5 fw-bold mb-4">
                        <?= htmlspecialchars($article['title']) ?>
                    </h1>
                    
                    <?php if ($article['excerpt']): ?>
                    <p class="lead mb-4">
                        <?= htmlspecialchars($article['excerpt']) ?>
                    </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Article Meta -->
                <div class="article-meta">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user-circle fa-2x text-primary me-3"></i>
                                <div>
                                    <strong><?= htmlspecialchars($article['author_name']) ?></strong>
                                    <div class="small text-muted">الكاتب</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="small text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?= date('d F Y', strtotime($article['published_at'])) ?>
                                <br>
                                <i class="fas fa-eye me-1"></i>
                                <?= number_format($article['views_count'] + 1) ?> مشاهدة
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Article Content -->
                <div class="article-content">
                    <div class="content-text">
                        <?= nl2br(htmlspecialchars($article['content'])) ?>
                    </div>
                </div>

                <!-- Share Buttons -->
                <div class="article-content">
                    <h5 class="mb-3">
                        <i class="fas fa-share-alt me-2"></i>
                        شارك هذا الخبر
                    </h5>
                    <div class="share-buttons">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode($_SERVER['REQUEST_URI']) ?>" 
                           target="_blank" class="btn btn-primary">
                            <i class="fab fa-facebook-f me-1"></i>
                            فيسبوك
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?= urlencode($_SERVER['REQUEST_URI']) ?>&text=<?= urlencode($article['title']) ?>" 
                           target="_blank" class="btn btn-info">
                            <i class="fab fa-twitter me-1"></i>
                            تويتر
                        </a>
                        <a href="https://wa.me/?text=<?= urlencode($article['title'] . ' ' . $_SERVER['REQUEST_URI']) ?>" 
                           target="_blank" class="btn btn-success">
                            <i class="fab fa-whatsapp me-1"></i>
                            واتساب
                        </a>
                        <button type="button" class="btn btn-secondary" onclick="copyLink()">
                            <i class="fas fa-link me-1"></i>
                            نسخ الرابط
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Related Articles -->
                <?php if (!empty($related_articles)): ?>
                <div class="mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-newspaper me-2"></i>
                        أخبار ذات صلة
                    </h5>
                    
                    <?php foreach ($related_articles as $related): ?>
                    <div class="related-article">
                        <h6 class="mb-2">
                            <a href="/news-single.php?slug=<?= urlencode($related['slug']) ?>" 
                               class="text-decoration-none text-dark">
                                <?= htmlspecialchars($related['title']) ?>
                            </a>
                        </h6>
                        <p class="text-muted small mb-2">
                            <?= htmlspecialchars(substr($related['excerpt'] ?? '', 0, 80)) ?>...
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?= date('Y-m-d', strtotime($related['published_at'])) ?>
                            </small>
                            <a href="/news-single.php?slug=<?= urlencode($related['slug']) ?>" 
                               class="btn btn-outline-primary btn-sm">
                                اقرأ
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <!-- Back to News -->
                <div class="text-center">
                    <a href="/news.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة لجميع الأخبار
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-4 mt-5">
        <div class="container">
            <p class="mb-0">
                &copy; 2024 نظام إدارة المؤسسات غير الربحية. جميع الحقوق محفوظة.
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyLink() {
            navigator.clipboard.writeText(window.location.href).then(function() {
                alert('تم نسخ الرابط بنجاح!');
            });
        }
    </script>
</body>
</html>
