<?php
// مكتبة الوسائط - إدارة الصور والفيديوهات
define('ROOT_PATH', dirname(__DIR__));
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: /login.php');
    exit;
}

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// إعداد قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

$success = '';
$error = '';

// معالجة رفع الملفات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'upload_media') {
        try {
            $title = trim($_POST['title']);
            $description = trim($_POST['description']);
            $category = trim($_POST['category']) ?: 'general';
            $alt_text = trim($_POST['alt_text']);
            $caption = trim($_POST['caption']);
            $tags = trim($_POST['tags']);
            $is_featured = isset($_POST['is_featured']) ? 1 : 0;

            if (empty($title)) {
                throw new Exception('يجب ملء عنوان الملف');
            }

            // معالجة رفع الملفات المتعددة
            if (isset($_FILES['media_files']) && !empty($_FILES['media_files']['name'][0])) {
                $upload_dir = 'uploads/media/';
                $thumb_dir = 'uploads/thumbnails/';

                if (!is_dir($upload_dir)) mkdir($upload_dir, 0755, true);
                if (!is_dir($thumb_dir)) mkdir($thumb_dir, 0755, true);

                $allowed_images = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                $allowed_videos = ['mp4', 'avi', 'mov', 'wmv', 'flv'];
                $allowed_types = array_merge($allowed_images, $allowed_videos);

                $uploaded_count = 0;

                for ($i = 0; $i < count($_FILES['media_files']['name']); $i++) {
                    if ($_FILES['media_files']['error'][$i] === UPLOAD_ERR_OK) {
                        $file_info = pathinfo($_FILES['media_files']['name'][$i]);
                        $extension = strtolower($file_info['extension']);

                        if (!in_array($extension, $allowed_types)) {
                            continue; // تخطي الملفات غير المدعومة
                        }

                        $file_name = uniqid() . '.' . $extension;
                        $file_path = $upload_dir . $file_name;

                        if (move_uploaded_file($_FILES['media_files']['tmp_name'][$i], $file_path)) {
                            $file_size = filesize($file_path);
                            $mime_type = mime_content_type($file_path);
                            $file_type = in_array($extension, $allowed_images) ? 'image' : 'video';

                            $dimensions = '';
                            $thumbnail_path = null;

                            // معالجة الصور
                            if ($file_type === 'image') {
                                $image_info = getimagesize($file_path);
                                if ($image_info) {
                                    $dimensions = $image_info[0] . 'x' . $image_info[1];

                                    // إنشاء صورة مصغرة
                                    $thumbnail_name = 'thumb_' . $file_name;
                                    $thumbnail_path = $thumb_dir . $thumbnail_name;

                                    if (createThumbnail($file_path, $thumbnail_path, 300, 300)) {
                                        $thumbnail_path = 'uploads/thumbnails/' . $thumbnail_name;
                                    } else {
                                        $thumbnail_path = null;
                                    }
                                }
                            }

                            // إدراج في قاعدة البيانات
                            $stmt = $pdo->prepare("
                                INSERT INTO media_library
                                (title, description, file_path, file_name, original_name, file_size,
                                 file_type, mime_type, dimensions, thumbnail_path, alt_text, caption,
                                 tags, category, uploaded_by, is_featured)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ");

                            $stmt->execute([
                                $title . ($i > 0 ? " ($i)" : ''), $description, 'uploads/media/' . $file_name,
                                $file_name, $_FILES['media_files']['name'][$i], $file_size, $file_type,
                                $mime_type, $dimensions, $thumbnail_path, $alt_text, $caption,
                                $tags, $category, $_SESSION['user_id'], $is_featured
                            ]);

                            $uploaded_count++;
                        }
                    }
                }

                if ($uploaded_count > 0) {
                    $success = "تم رفع {$uploaded_count} ملف بنجاح";
                } else {
                    $error = 'لم يتم رفع أي ملف';
                }
            } else {
                $error = 'يجب اختيار ملف واحد على الأقل';
            }

        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// دالة إنشاء الصورة المصغرة
function createThumbnail($source, $destination, $max_width, $max_height) {
    // التحقق من وجود الملف المصدر
    if (!file_exists($source)) return false;

    $image_info = getimagesize($source);
    if (!$image_info) return false;

    $width = $image_info[0];
    $height = $image_info[1];
    $type = $image_info[2];

    // التحقق من وجود مجلد الوجهة
    $destination_dir = dirname($destination);
    if (!is_dir($destination_dir)) {
        mkdir($destination_dir, 0755, true);
    }

    // حساب الأبعاد الجديدة
    $ratio = min((float)$max_width / (float)$width, (float)$max_height / (float)$height);
    $new_width = (int)round((float)$width * $ratio);
    $new_height = (int)round((float)$height * $ratio);

    // إنشاء الصورة المصدر
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $source_image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }

    // إنشاء الصورة المصغرة
    $thumb = imagecreatetruecolor($new_width, $new_height);

    // الحفاظ على الشفافية للـ PNG
    if ($type == IMAGETYPE_PNG) {
        imagealphablending($thumb, false);
        imagesavealpha($thumb, true);
    }

    imagecopyresampled($thumb, $source_image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

    // حفظ الصورة المصغرة
    $result = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($thumb, $destination, 85);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($thumb, $destination);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($thumb, $destination);
            break;
    }

    imagedestroy($source_image);
    imagedestroy($thumb);

    return $result;
}

// جلب الوسائط
$filter = $_GET['filter'] ?? 'all';
$category = $_GET['category'] ?? '';

$where_conditions = [];
$params = [];

if ($filter === 'images') {
    $where_conditions[] = "file_type = 'image'";
} elseif ($filter === 'videos') {
    $where_conditions[] = "file_type = 'video'";
}

if ($category) {
    $where_conditions[] = "category = ?";
    $params[] = $category;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

$media_query = "
    SELECT m.*, u.name as uploader_name
    FROM media_library m
    LEFT JOIN users u ON m.uploaded_by = u.id
    {$where_clause}
    ORDER BY m.created_at DESC
";

$stmt = $pdo->prepare($media_query);
$stmt->execute($params);
$media_files = $stmt->fetchAll();

// إحصائيات
$stats = [
    'total_files' => $pdo->query("SELECT COUNT(*) FROM media_library")->fetchColumn(),
    'total_images' => $pdo->query("SELECT COUNT(*) FROM media_library WHERE file_type = 'image'")->fetchColumn(),
    'total_videos' => $pdo->query("SELECT COUNT(*) FROM media_library WHERE file_type = 'video'")->fetchColumn(),
    'total_size' => $pdo->query("SELECT SUM(file_size) FROM media_library")->fetchColumn() ?: 0
];

// التصنيفات المتاحة
$categories = $pdo->query("SELECT DISTINCT category FROM media_library WHERE category IS NOT NULL ORDER BY category")->fetchAll(PDO::FETCH_COLUMN);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مكتبة الوسائط - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #28a745, #20c997); }
        .stats-card { background: linear-gradient(45deg, #28a745, #20c997); color: white; border-radius: 15px; }
        .media-card { border-radius: 10px; transition: transform 0.2s; overflow: hidden; }
        .media-card:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .media-thumbnail { width: 100%; height: 200px; object-fit: cover; }
        .upload-area { border: 2px dashed #28a745; border-radius: 10px; padding: 40px; text-align: center; }
        .upload-area.dragover { background-color: #e8f5e8; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-photo-video me-2"></i>مكتبة الوسائط
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="/media-gallery.php" target="_blank">
                    <i class="fas fa-eye me-1"></i>معرض الوسائط
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-file fa-2x mb-2"></i>
                    <h4><?= $stats['total_files'] ?></h4>
                    <small>إجمالي الملفات</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-image fa-2x mb-2"></i>
                    <h4><?= $stats['total_images'] ?></h4>
                    <small>الصور</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-video fa-2x mb-2"></i>
                    <h4><?= $stats['total_videos'] ?></h4>
                    <small>الفيديوهات</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-hdd fa-2x mb-2"></i>
                    <h4><?= number_format($stats['total_size']/1024/1024, 1) ?> MB</h4>
                    <small>المساحة المستخدمة</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- نموذج رفع الملفات -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-cloud-upload-alt me-2"></i>رفع ملفات جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="upload_media">

                            <div class="mb-3">
                                <label class="form-label">العنوان *</label>
                                <input type="text" name="title" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea name="description" class="form-control" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الملفات *</label>
                                <div class="upload-area" id="uploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-success mb-3"></i>
                                    <p>اسحب الملفات هنا أو اضغط للاختيار</p>
                                    <input type="file" name="media_files[]" class="form-control"
                                           multiple accept="image/*,video/*" id="fileInput" style="display: none;">
                                </div>
                                <small class="text-muted">الصور: JPG, PNG, GIF, WebP | الفيديو: MP4, AVI, MOV</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">التصنيف</label>
                                <select name="category" class="form-select">
                                    <option value="general">عام</option>
                                    <option value="events">فعاليات</option>
                                    <option value="projects">مشاريع</option>
                                    <option value="team">الفريق</option>
                                    <option value="gallery">معرض الصور</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">النص البديل</label>
                                <input type="text" name="alt_text" class="form-control"
                                       placeholder="وصف للصور (مهم لمحركات البحث)">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">التسمية التوضيحية</label>
                                <input type="text" name="caption" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الكلمات المفتاحية</label>
                                <input type="text" name="tags" class="form-control"
                                       placeholder="مفصولة بفواصل">
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" name="is_featured" class="form-check-input" id="is_featured">
                                    <label class="form-check-label" for="is_featured">
                                        ملف مميز
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-upload me-2"></i>رفع الملفات
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- مكتبة الوسائط -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-images me-2"></i>مكتبة الوسائط</h5>
                        <div class="btn-group">
                            <a href="?filter=all" class="btn btn-sm <?= $filter === 'all' ? 'btn-success' : 'btn-outline-success' ?>">
                                الكل
                            </a>
                            <a href="?filter=images" class="btn btn-sm <?= $filter === 'images' ? 'btn-success' : 'btn-outline-success' ?>">
                                صور
                            </a>
                            <a href="?filter=videos" class="btn btn-sm <?= $filter === 'videos' ? 'btn-success' : 'btn-outline-success' ?>">
                                فيديو
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($media_files)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد ملفات وسائط بعد</p>
                        </div>
                        <?php else: ?>
                        <div class="row">
                            <?php foreach ($media_files as $media): ?>
                            <div class="col-md-4 mb-3">
                                <div class="card media-card h-100">
                                    <div class="position-relative">
                                        <?php if ($media['file_type'] === 'image'): ?>
                                            <img src="/<?= $media['thumbnail_path'] ?: $media['file_path'] ?>"
                                                 class="media-thumbnail" alt="<?= htmlspecialchars($media['alt_text']) ?>">
                                        <?php else: ?>
                                            <div class="media-thumbnail d-flex align-items-center justify-content-center bg-dark">
                                                <i class="fas fa-play-circle fa-3x text-white"></i>
                                            </div>
                                        <?php endif; ?>

                                        <?php if ($media['is_featured']): ?>
                                        <span class="position-absolute top-0 end-0 badge bg-warning m-2">
                                            <i class="fas fa-star"></i>
                                        </span>
                                        <?php endif; ?>
                                    </div>

                                    <div class="card-body p-2">
                                        <h6 class="card-title small"><?= htmlspecialchars($media['title']) ?></h6>

                                        <div class="small text-muted">
                                            <div><?= htmlspecialchars($media['category']) ?></div>
                                            <div><?= number_format($media['file_size']/1024, 1) ?> KB</div>
                                            <?php if ($media['dimensions']): ?>
                                            <div><?= $media['dimensions'] ?></div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center mt-2">
                                            <div class="btn-group btn-group-sm">
                                                <a href="/<?= $media['file_path'] ?>" class="btn btn-outline-primary" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button class="btn btn-outline-secondary">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-download me-1"></i><?= $media['download_count'] ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Drag and Drop functionality
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        uploadArea.addEventListener('click', () => fileInput.click());

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            fileInput.files = e.dataTransfer.files;
        });
    </script>
</body>
</html>
