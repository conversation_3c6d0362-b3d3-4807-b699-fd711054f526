<?php
// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    echo "<h2>✅ الاتصال بقاعدة البيانات ناجح</h2>";
    echo "<p>قاعدة البيانات: <strong>$dbname</strong></p>";
} catch (Exception $e) {
    die("<h2>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</h2>");
}

echo "<h2>🚀 بدء إنشاء جدول المستخدمين...</h2>";

// إنشاء جدول المستخدمين
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'manager', 'employee', 'user') DEFAULT 'user',
            is_active BOOLEAN DEFAULT TRUE,
            email_verified_at TIMESTAMP NULL,
            remember_token VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_role (role),
            INDEX idx_active (is_active)
        )
    ");
    echo "<p>✅ تم إنشاء جدول: <strong>users</strong></p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول users: " . $e->getMessage() . "</p>";
}

// إنشاء مستخدم إداري افتراضي
try {
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->exec("
        INSERT IGNORE INTO users (name, email, password, role, is_active) VALUES
        ('المدير العام', '<EMAIL>', '$admin_password', 'admin', 1)
    ");
    echo "<p>✅ تم إنشاء المستخدم الإداري الافتراضي</p>";
    echo "<p><strong>بيانات الدخول:</strong></p>";
    echo "<p>البريد الإلكتروني: <EMAIL></p>";
    echo "<p>كلمة المرور: admin123</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء المستخدم الإداري: " . $e->getMessage() . "</p>";
}

// إنشاء جدول الجلسات (إذا لم يكن موجوداً)
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS sessions (
            id VARCHAR(255) PRIMARY KEY,
            user_id INT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            payload TEXT NOT NULL,
            last_activity INT NOT NULL,
            INDEX idx_user_id (user_id),
            INDEX idx_last_activity (last_activity)
        )
    ");
    echo "<p>✅ تم إنشاء جدول: <strong>sessions</strong></p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء جدول sessions: " . $e->getMessage() . "</p>";
}

// التحقق من الجداول الموجودة
echo "<hr>";
echo "<h3>📋 الجداول الموجودة في قاعدة البيانات:</h3>";

try {
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($tables as $table) {
        $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
        echo "<p>✅ جدول <strong>$table</strong>: $count سجل</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ خطأ في جلب قائمة الجداول: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🎉 تم إنشاء جدول المستخدمين بنجاح!</h3>";
echo "<p>يمكنك الآن:</p>";
echo "<ul>";
echo "<li>تسجيل الدخول باستخدام: <EMAIL> / admin123</li>";
echo "<li><a href='/employees.php' target='_blank'>الوصول لإدارة الموظفين</a></li>";
echo "<li><a href='/dashboard.php' target='_blank'>الوصول للوحة التحكم</a></li>";
echo "</ul>";
echo "</div>";

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
ul { margin: 10px 0; padding-left: 20px; }
</style>";
?>
