<?php
session_start();
header('Content-Type: application/json');

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

// التحقق من البيانات المطلوبة
if (!isset($_POST['product_id']) || !isset($_POST['quantity'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
    exit;
}

$product_id = (int)$_POST['product_id'];
$quantity = (int)$_POST['quantity'];

if ($product_id <= 0 || $quantity <= 0) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

try {
    // التحقق من وجود المنتج وحالته
    $product_stmt = $pdo->prepare("
        SELECT id, name, price, sale_price, stock_quantity, status 
        FROM products 
        WHERE id = ? AND status = 'active'
    ");
    $product_stmt->execute([$product_id]);
    $product = $product_stmt->fetch();
    
    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'المنتج غير متوفر']);
        exit;
    }
    
    // التحقق من المخزون
    if ($product['stock_quantity'] < $quantity) {
        echo json_encode(['success' => false, 'message' => 'الكمية المطلوبة غير متوفرة في المخزون']);
        exit;
    }
    
    // تحديد السعر (سعر التخفيض إن وجد، وإلا السعر العادي)
    $price = $product['sale_price'] ?: $product['price'];
    
    // إنشاء session_id للزوار غير المسجلين
    if (!isset($_SESSION['cart_session_id'])) {
        $_SESSION['cart_session_id'] = session_id();
    }
    
    $customer_id = null;
    $session_id = $_SESSION['cart_session_id'];
    
    // إذا كان المستخدم مسجل دخول، جلب معرف العميل
    if (isset($_SESSION['user_id'])) {
        $customer_stmt = $pdo->prepare("SELECT id FROM customers WHERE user_id = ?");
        $customer_stmt->execute([$_SESSION['user_id']]);
        $customer_data = $customer_stmt->fetch();
        
        if ($customer_data) {
            $customer_id = $customer_data['id'];
        } else {
            // إنشاء عميل جديد إذا لم يكن موجود
            $user_stmt = $pdo->prepare("SELECT name, email FROM users WHERE id = ?");
            $user_stmt->execute([$_SESSION['user_id']]);
            $user_data = $user_stmt->fetch();
            
            if ($user_data) {
                $names = explode(' ', $user_data['name'], 2);
                $first_name = $names[0];
                $last_name = $names[1] ?? '';
                
                $create_customer = $pdo->prepare("
                    INSERT INTO customers (user_id, first_name, last_name, email, status)
                    VALUES (?, ?, ?, ?, 'active')
                ");
                $create_customer->execute([$_SESSION['user_id'], $first_name, $last_name, $user_data['email']]);
                $customer_id = $pdo->lastInsertId();
            }
        }
    }
    
    // التحقق من وجود المنتج في السلة
    $existing_stmt = $pdo->prepare("
        SELECT id, quantity FROM shopping_cart 
        WHERE product_id = ? AND " . ($customer_id ? "customer_id = ?" : "session_id = ?")
    );
    $existing_stmt->execute([$product_id, $customer_id ?: $session_id]);
    $existing_item = $existing_stmt->fetch();
    
    if ($existing_item) {
        // تحديث الكمية
        $new_quantity = $existing_item['quantity'] + $quantity;
        
        // التحقق من المخزون مرة أخرى
        if ($product['stock_quantity'] < $new_quantity) {
            echo json_encode(['success' => false, 'message' => 'الكمية الإجمالية تتجاوز المخزون المتاح']);
            exit;
        }
        
        $update_stmt = $pdo->prepare("
            UPDATE shopping_cart 
            SET quantity = ?, price = ?, updated_at = NOW() 
            WHERE id = ?
        ");
        $update_stmt->execute([$new_quantity, $price, $existing_item['id']]);
        
        $message = 'تم تحديث كمية المنتج في السلة';
    } else {
        // إضافة منتج جديد للسلة
        $insert_stmt = $pdo->prepare("
            INSERT INTO shopping_cart (customer_id, session_id, product_id, quantity, price)
            VALUES (?, ?, ?, ?, ?)
        ");
        $insert_stmt->execute([$customer_id, $session_id, $product_id, $quantity, $price]);
        
        $message = 'تم إضافة المنتج للسلة بنجاح';
    }
    
    // جلب عدد المنتجات في السلة
    $count_stmt = $pdo->prepare("
        SELECT SUM(quantity) FROM shopping_cart 
        WHERE " . ($customer_id ? "customer_id = ?" : "session_id = ?")
    );
    $count_stmt->execute([$customer_id ?: $session_id]);
    $cart_count = $count_stmt->fetchColumn() ?: 0;
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'cart_count' => $cart_count,
        'product_name' => $product['name']
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في إضافة المنتج: ' . $e->getMessage()]);
}
?>
