<?php
// تحديث قاعدة البيانات لإضافة نظام CMS
define('ROOT_PATH', dirname(__DIR__));

// بدء الجلسة
session_start();

// التحقق من تسجيل الدخول كمدير
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in'] || $_SESSION['user_role'] !== 'admin') {
    die('غير مسموح - يجب تسجيل الدخول كمدير');
}

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// إعداد قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

$success = false;
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // إنشاء جداول نظام CMS
        $tables = [
            // جدول تصنيفات الأخبار
            "CREATE TABLE IF NOT EXISTS news_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                slug VARCHAR(255) NOT NULL UNIQUE,
                description TEXT,
                color VARCHAR(7) DEFAULT '#007bff',
                status VARCHAR(50) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",

            // جدول الأخبار
            "CREATE TABLE IF NOT EXISTS news_articles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                slug VARCHAR(255) NOT NULL,
                excerpt TEXT,
                content LONGTEXT NOT NULL,
                featured_image VARCHAR(255),
                category_id INT,
                author_id INT NOT NULL,
                status VARCHAR(50) DEFAULT 'draft',
                is_featured BOOLEAN DEFAULT FALSE,
                views_count INT DEFAULT 0,
                published_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",

            // جدول التعليقات
            "CREATE TABLE IF NOT EXISTS news_comments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                article_id INT NOT NULL,
                author_name VARCHAR(255) NOT NULL,
                author_email VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                status VARCHAR(50) DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",

            // جدول الوسائط
            "CREATE TABLE IF NOT EXISTS media_files (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL,
                original_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                file_size INT NOT NULL,
                mime_type VARCHAR(100) NOT NULL,
                uploaded_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"
        ];

        foreach ($tables as $sql) {
            $pdo->exec($sql);
        }

        // إضافة فهارس منفصلة
        try {
            $pdo->exec("ALTER TABLE news_categories ADD UNIQUE INDEX idx_slug (slug)");
        } catch (Exception $e) {
            // تجاهل الخطأ إذا كان الفهرس موجود
        }

        try {
            $pdo->exec("ALTER TABLE news_articles ADD INDEX idx_slug (slug)");
        } catch (Exception $e) {
            // تجاهل الخطأ إذا كان الفهرس موجود
        }

        // إدراج تصنيفات افتراضية
        $categories = [
            ['أخبار عامة', 'general-news', 'آخر الأخبار والمستجدات', '#007bff'],
            ['فعاليات', 'events', 'الفعاليات والأنشطة', '#28a745'],
            ['إنجازات', 'achievements', 'الإنجازات والنجاحات', '#ffc107'],
            ['تبرعات', 'donations', 'أخبار التبرعات والمساهمات', '#dc3545'],
            ['مشاريع', 'projects', 'أخبار المشاريع والبرامج', '#6f42c1']
        ];

        $stmt = $pdo->prepare("INSERT IGNORE INTO news_categories (name, slug, description, color) VALUES (?, ?, ?, ?)");
        foreach ($categories as $category) {
            $stmt->execute($category);
        }

        // إدراج مقالات تجريبية
        $articles = [
            [
                'مرحباً بكم في نظام إدارة المحتوى',
                'welcome-to-cms',
                'نظام إدارة المحتوى الجديد جاهز للاستخدام مع العديد من المميزات الرائعة',
                '<p>نرحب بكم في نظام إدارة المحتوى الجديد الخاص بالمؤسسات غير الربحية.</p><p>يتضمن النظام العديد من المميزات:</p><ul><li>إدارة الأخبار والمقالات</li><li>نظام التصنيفات</li><li>إدارة التعليقات</li><li>رفع الوسائط</li></ul>',
                1, // category_id
                $_SESSION['user_id'], // author_id
                'published',
                1, // is_featured
                date('Y-m-d H:i:s')
            ],
            [
                'كيفية استخدام نظام إدارة المحتوى',
                'how-to-use-cms',
                'دليل شامل لاستخدام جميع مميزات نظام إدارة المحتوى',
                '<p>يمكنك الآن إدارة محتوى موقعك بسهولة من خلال لوحة التحكم.</p><h3>الخطوات الأساسية:</h3><ol><li>انتقل إلى قسم الأخبار في لوحة التحكم</li><li>اضغط على "إضافة خبر جديد"</li><li>املأ البيانات المطلوبة</li><li>احفظ ونشر</li></ol>',
                1, // category_id
                $_SESSION['user_id'], // author_id
                'published',
                0, // is_featured
                date('Y-m-d H:i:s')
            ]
        ];

        $stmt = $pdo->prepare("INSERT IGNORE INTO news_articles (title, slug, excerpt, content, category_id, author_id, status, is_featured, published_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($articles as $article) {
            $stmt->execute($article);
        }

        $success = true;

    } catch (PDOException $e) {
        $error = "خطأ في إنشاء الجداول: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .update-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            max-width: 600px;
            margin: 50px auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="update-card">
            <div class="text-center mb-4">
                <i class="fas fa-newspaper fa-3x text-primary mb-3"></i>
                <h2>تحديث نظام CMS</h2>
                <p class="text-muted">إضافة نظام إدارة المحتوى والأخبار</p>
            </div>

            <?php if ($success): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>تم التحديث بنجاح!</h5>
                <p>تم إنشاء جداول نظام CMS وإضافة البيانات التجريبية.</p>
                <hr>
                <div class="d-flex justify-content-center">
                    <a href="/dashboard.php" class="btn btn-success me-2">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        لوحة التحكم
                    </a>
                    <a href="/news.php" class="btn btn-primary">
                        <i class="fas fa-newspaper me-1"></i>
                        عرض الأخبار
                    </a>
                </div>
            </div>
            <?php elseif ($error): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>خطأ في التحديث</h5>
                <p><?= htmlspecialchars($error) ?></p>
            </div>
            <?php else: ?>
            <form method="POST">
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>ما سيتم إنشاؤه:</h5>
                    <ul class="mb-0">
                        <li>جدول تصنيفات الأخبار</li>
                        <li>جدول الأخبار والمقالات</li>
                        <li>جدول التعليقات</li>
                        <li>جدول الوسائط</li>
                        <li>تصنيفات افتراضية</li>
                        <li>مقالات تجريبية</li>
                    </ul>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-download me-2"></i>
                        بدء التحديث
                    </button>
                </div>
            </form>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
