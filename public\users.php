<?php
session_start();

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: /login.php');
    exit;
}

$success = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_user') {
        try {
            $required_fields = ['name', 'email', 'password', 'role'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("جميع الحقول المطلوبة يجب ملؤها");
                }
            }
            
            // التحقق من عدم وجود البريد الإلكتروني
            $check_email = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $check_email->execute([$_POST['email']]);
            if ($check_email->fetch()) {
                throw new Exception("البريد الإلكتروني موجود بالفعل");
            }
            
            $hashed_password = password_hash($_POST['password'], PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("
                INSERT INTO users (name, email, password, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $_POST['name'],
                $_POST['email'],
                $hashed_password,
                $_POST['role'],
                isset($_POST['is_active']) ? 1 : 0
            ]);
            
            $user_id = $pdo->lastInsertId();
            
            // إنشاء موظف مرتبط إذا تم تحديد ذلك
            if (!empty($_POST['create_employee'])) {
                $employee_id = 'EMP' . str_pad($user_id, 4, '0', STR_PAD_LEFT);
                $default_dept = $pdo->query("SELECT id FROM departments LIMIT 1")->fetchColumn();
                $default_pos = $pdo->query("SELECT id FROM positions LIMIT 1")->fetchColumn();
                
                $emp_stmt = $pdo->prepare("
                    INSERT INTO employees (user_id, employee_id, department_id, position_id, hire_date, salary, vacation_balance, sick_leave_balance)
                    VALUES (?, ?, ?, ?, CURDATE(), ?, 30, 15)
                ");
                
                $emp_stmt->execute([
                    $user_id,
                    $employee_id,
                    $_POST['department_id'] ?: $default_dept,
                    $_POST['position_id'] ?: $default_pos,
                    $_POST['salary'] ?: 5000
                ]);
            }
            
            $success = "تم إضافة المستخدم بنجاح";
            
        } catch (Exception $e) {
            $error = "خطأ في إضافة المستخدم: " . $e->getMessage();
        }
    }
    
    elseif ($action === 'edit_user') {
        try {
            $user_id = $_POST['user_id'];
            $update_fields = [];
            $params = [];
            
            if (!empty($_POST['name'])) {
                $update_fields[] = "name = ?";
                $params[] = $_POST['name'];
            }
            
            if (!empty($_POST['email'])) {
                // التحقق من عدم وجود البريد الإلكتروني لمستخدم آخر
                $check_email = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $check_email->execute([$_POST['email'], $user_id]);
                if ($check_email->fetch()) {
                    throw new Exception("البريد الإلكتروني موجود بالفعل");
                }
                
                $update_fields[] = "email = ?";
                $params[] = $_POST['email'];
            }
            
            if (!empty($_POST['password'])) {
                $update_fields[] = "password = ?";
                $params[] = password_hash($_POST['password'], PASSWORD_DEFAULT);
            }
            
            if (!empty($_POST['role'])) {
                $update_fields[] = "role = ?";
                $params[] = $_POST['role'];
            }
            
            $update_fields[] = "is_active = ?";
            $params[] = isset($_POST['is_active']) ? 1 : 0;
            
            $params[] = $user_id;
            
            $stmt = $pdo->prepare("UPDATE users SET " . implode(', ', $update_fields) . " WHERE id = ?");
            $stmt->execute($params);
            
            $success = "تم تحديث المستخدم بنجاح";
            
        } catch (Exception $e) {
            $error = "خطأ في تحديث المستخدم: " . $e->getMessage();
        }
    }
    
    elseif ($action === 'delete_user') {
        try {
            $user_id = $_POST['user_id'];
            
            // التحقق من عدم حذف المستخدم الحالي
            if ($user_id == $_SESSION['user_id']) {
                throw new Exception("لا يمكن حذف حسابك الشخصي");
            }
            
            $pdo->beginTransaction();
            
            // حذف الموظف المرتبط إن وجد
            $pdo->prepare("DELETE FROM employees WHERE user_id = ?")->execute([$user_id]);
            
            // حذف المستخدم
            $pdo->prepare("DELETE FROM users WHERE id = ?")->execute([$user_id]);
            
            $pdo->commit();
            $success = "تم حذف المستخدم بنجاح";
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $error = "خطأ في حذف المستخدم: " . $e->getMessage();
        }
    }
}

// جلب المستخدمين مع معلومات الموظفين
$users_query = "
    SELECT u.*, e.employee_id, e.department_id, e.position_id,
           d.name as department_name, p.title as position_title
    FROM users u
    LEFT JOIN employees e ON u.id = e.user_id
    LEFT JOIN departments d ON e.department_id = d.id
    LEFT JOIN positions p ON e.position_id = p.id
    ORDER BY u.created_at DESC
";

$users = $pdo->query($users_query)->fetchAll();

// جلب الأقسام والمناصب للنماذج
$departments = $pdo->query("SELECT * FROM departments ORDER BY name")->fetchAll();
$positions = $pdo->query("SELECT * FROM positions ORDER BY title")->fetchAll();

// إحصائيات
$stats = [
    'total_users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'active_users' => $pdo->query("SELECT COUNT(*) FROM users WHERE is_active = 1")->fetchColumn(),
    'admin_users' => $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'")->fetchColumn(),
    'linked_employees' => $pdo->query("SELECT COUNT(*) FROM users u JOIN employees e ON u.id = e.user_id")->fetchColumn()
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #17a2b8, #138496); }
        .stats-card { background: linear-gradient(45deg, #17a2b8, #138496); color: white; border-radius: 15px; }
        .user-card { border-radius: 10px; transition: transform 0.2s; }
        .user-card:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .role-admin { color: #dc3545; }
        .role-manager { color: #fd7e14; }
        .role-employee { color: #28a745; }
        .status-active { color: #28a745; }
        .status-inactive { color: #dc3545; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-users me-2"></i>إدارة المستخدمين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="/employees.php">
                    <i class="fas fa-user-tie me-1"></i>الموظفين
                </a>
                <a class="nav-link" href="/messages.php">
                    <i class="fas fa-envelope me-1"></i>الرسائل
                </a>
                <a class="nav-link" href="/leave-requests.php">
                    <i class="fas fa-calendar-alt me-1"></i>الإجازات
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4><?= $stats['total_users'] ?></h4>
                    <small>إجمالي المستخدمين</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h4><?= $stats['active_users'] ?></h4>
                    <small>المستخدمين النشطين</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-user-shield fa-2x mb-2"></i>
                    <h4><?= $stats['admin_users'] ?></h4>
                    <small>المديرين</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-user-tie fa-2x mb-2"></i>
                    <h4><?= $stats['linked_employees'] ?></h4>
                    <small>مرتبطين بموظفين</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- نموذج إضافة مستخدم جديد -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_user">

                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل *</label>
                                <input type="text" name="name" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني *</label>
                                <input type="email" name="email" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">كلمة المرور *</label>
                                <input type="password" name="password" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الدور *</label>
                                <select name="role" class="form-select" required>
                                    <option value="">اختر الدور</option>
                                    <option value="admin">مدير</option>
                                    <option value="manager">مدير قسم</option>
                                    <option value="employee">موظف</option>
                                </select>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" name="is_active" class="form-check-input" id="is_active" checked>
                                <label class="form-check-label" for="is_active">حساب نشط</label>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" name="create_employee" class="form-check-input" id="create_employee">
                                <label class="form-check-label" for="create_employee">إنشاء ملف موظف</label>
                            </div>

                            <div id="employee_fields" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">القسم</label>
                                    <select name="department_id" class="form-select">
                                        <option value="">اختر القسم</option>
                                        <?php foreach ($departments as $dept): ?>
                                        <option value="<?= $dept['id'] ?>"><?= htmlspecialchars($dept['name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">المنصب</label>
                                    <select name="position_id" class="form-select">
                                        <option value="">اختر المنصب</option>
                                        <?php foreach ($positions as $pos): ?>
                                        <option value="<?= $pos['id'] ?>"><?= htmlspecialchars($pos['title']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">الراتب</label>
                                    <input type="number" name="salary" class="form-control" value="5000">
                                </div>
                            </div>

                            <button type="submit" class="btn btn-info w-100">
                                <i class="fas fa-plus me-2"></i>إضافة المستخدم
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة المستخدمين -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>قائمة المستخدمين</h5>
                        <div class="d-flex gap-2">
                            <input type="text" id="searchUsers" class="form-control form-control-sm" placeholder="بحث...">
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مستخدمين</p>
                        </div>
                        <?php else: ?>
                        <div class="row" id="usersContainer">
                            <?php foreach ($users as $user): ?>
                            <div class="col-md-6 mb-3 user-item">
                                <div class="card user-card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0"><?= htmlspecialchars($user['name']) ?></h6>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="editUser(<?= $user['id'] ?>)">
                                                        <i class="fas fa-edit me-2"></i>تعديل
                                                    </a></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteUser(<?= $user['id'] ?>)">
                                                        <i class="fas fa-trash me-2"></i>حذف
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>

                                        <div class="small text-muted mb-2">
                                            <div><i class="fas fa-envelope me-1"></i><?= htmlspecialchars($user['email']) ?></div>
                                            <div><i class="fas fa-user-tag me-1"></i>
                                                <span class="role-<?= $user['role'] ?>">
                                                    <?php
                                                    $roles = [
                                                        'admin' => 'مدير',
                                                        'manager' => 'مدير قسم',
                                                        'employee' => 'موظف'
                                                    ];
                                                    echo $roles[$user['role']] ?? $user['role'];
                                                    ?>
                                                </span>
                                            </div>
                                            <div><i class="fas fa-circle me-1"></i>
                                                <span class="status-<?= $user['is_active'] ? 'active' : 'inactive' ?>">
                                                    <?= $user['is_active'] ? 'نشط' : 'غير نشط' ?>
                                                </span>
                                            </div>
                                            <?php if ($user['employee_id']): ?>
                                            <div><i class="fas fa-id-badge me-1"></i><?= htmlspecialchars($user['employee_id']) ?></div>
                                            <div><i class="fas fa-building me-1"></i><?= htmlspecialchars($user['department_name'] ?? 'غير محدد') ?></div>
                                            <div><i class="fas fa-briefcase me-1"></i><?= htmlspecialchars($user['position_title'] ?? 'غير محدد') ?></div>
                                            <?php else: ?>
                                            <div class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>غير مرتبط بموظف</div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="small text-muted">
                                            <i class="fas fa-calendar me-1"></i>انضم في: <?= date('Y/m/d', strtotime($user['created_at'])) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إظهار/إخفاء حقول الموظف
        document.getElementById('create_employee').addEventListener('change', function() {
            const employeeFields = document.getElementById('employee_fields');
            employeeFields.style.display = this.checked ? 'block' : 'none';
        });

        // البحث في المستخدمين
        document.getElementById('searchUsers').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const userItems = document.querySelectorAll('.user-item');
            
            userItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                item.style.display = text.includes(searchTerm) ? 'block' : 'none';
            });
        });

        function editUser(userId) {
            // سيتم تنفيذ هذا لاحقاً
            alert('سيتم إضافة نموذج التعديل قريباً');
        }

        function deleteUser(userId) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_user">
                    <input type="hidden" name="user_id" value="${userId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
