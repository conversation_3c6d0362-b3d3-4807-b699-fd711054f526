<?php

namespace App\Models;

use Core\Database\Model;

class Organization extends Model
{
    protected $table = 'organizations';
    protected $fillable = [
        'name', 'name_en', 'license_number', 'license_date', 'license_expiry',
        'organization_type', 'legal_status', 'registration_number', 'tax_number',
        'email', 'phone', 'website', 'address', 'city', 'region', 'postal_code',
        'country', 'description', 'mission', 'vision', 'objectives', 'activities',
        'target_beneficiaries', 'logo', 'documents', 'is_active'
    ];

    public function getActiveOrganizations(): array
    {
        return $this->where(['is_active' => 1, 'legal_status' => 'active']);
    }

    public function getOrganizationsByType(string $type): array
    {
        return $this->where(['organization_type' => $type, 'is_active' => 1]);
    }

    public function getOrganizationsByStatus(string $status): array
    {
        return $this->where(['legal_status' => $status]);
    }

    public function getOrganizationsByCity(string $city): array
    {
        return $this->where(['city' => $city, 'is_active' => 1]);
    }

    public function searchOrganizations(string $term): array
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE (name LIKE :term OR name_en LIKE :term OR license_number LIKE :term 
                       OR email LIKE :term OR phone LIKE :term) 
                AND is_active = 1";
        return $this->connection->fetchAll($sql, ['term' => "%{$term}%"]);
    }

    public function findByLicenseNumber(string $licenseNumber): ?array
    {
        return $this->findBy('license_number', $licenseNumber);
    }

    public function findByEmail(string $email): ?array
    {
        return $this->findBy('email', $email);
    }

    public function getOrganizationEmployees(int $organizationId): array
    {
        $sql = "SELECT u.* FROM users u 
                WHERE u.organization_id = :org_id AND u.is_active = 1
                ORDER BY u.name";
        
        return $this->connection->fetchAll($sql, ['org_id' => $organizationId]);
    }

    public function getOrganizationProjects(int $organizationId): array
    {
        $sql = "SELECT p.* FROM projects p 
                WHERE p.organization_id = :org_id 
                ORDER BY p.created_at DESC";
        
        return $this->connection->fetchAll($sql, ['org_id' => $organizationId]);
    }

    public function getOrganizationDonations(int $organizationId): array
    {
        $sql = "SELECT d.* FROM donations d 
                WHERE d.organization_id = :org_id 
                ORDER BY d.created_at DESC";
        
        return $this->connection->fetchAll($sql, ['org_id' => $organizationId]);
    }

    public function approve(int $organizationId): int
    {
        return $this->update($organizationId, [
            'legal_status' => 'active',
            'approved_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function suspend(int $organizationId, string $reason = null): int
    {
        $data = [
            'legal_status' => 'suspended',
            'suspended_at' => date('Y-m-d H:i:s')
        ];
        
        if ($reason) {
            $data['suspension_reason'] = $reason;
        }
        
        return $this->update($organizationId, $data);
    }

    public function dissolve(int $organizationId, string $reason = null): int
    {
        $data = [
            'legal_status' => 'dissolved',
            'dissolved_at' => date('Y-m-d H:i:s'),
            'is_active' => 0
        ];
        
        if ($reason) {
            $data['dissolution_reason'] = $reason;
        }
        
        return $this->update($organizationId, $data);
    }

    public function getOrganizationStats(): array
    {
        $sql = "SELECT 
                    COUNT(*) as total_organizations,
                    SUM(CASE WHEN legal_status = 'active' THEN 1 ELSE 0 END) as active_organizations,
                    SUM(CASE WHEN legal_status = 'suspended' THEN 1 ELSE 0 END) as suspended_organizations,
                    SUM(CASE WHEN legal_status = 'dissolved' THEN 1 ELSE 0 END) as dissolved_organizations,
                    SUM(CASE WHEN legal_status = 'pending' THEN 1 ELSE 0 END) as pending_organizations,
                    SUM(CASE WHEN organization_type = 'charity' THEN 1 ELSE 0 END) as charity_organizations,
                    SUM(CASE WHEN organization_type = 'foundation' THEN 1 ELSE 0 END) as foundation_organizations,
                    SUM(CASE WHEN organization_type = 'association' THEN 1 ELSE 0 END) as association_organizations,
                    SUM(CASE WHEN organization_type = 'cooperative' THEN 1 ELSE 0 END) as cooperative_organizations
                FROM {$this->table}";
        
        return $this->connection->fetch($sql) ?: [];
    }

    public function getOrganizationsByRegion(): array
    {
        $sql = "SELECT region, COUNT(*) as count 
                FROM {$this->table} 
                WHERE is_active = 1 AND region IS NOT NULL 
                GROUP BY region 
                ORDER BY count DESC";
        
        return $this->connection->fetchAll($sql);
    }

    public function getRecentOrganizations(int $limit = 10): array
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE is_active = 1 
                ORDER BY created_at DESC 
                LIMIT {$limit}";
        
        return $this->connection->fetchAll($sql);
    }

    public function getExpiringLicenses(int $days = 30): array
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE license_expiry IS NOT NULL 
                AND license_expiry <= DATE_ADD(NOW(), INTERVAL {$days} DAY)
                AND license_expiry >= NOW()
                AND legal_status = 'active'
                ORDER BY license_expiry ASC";
        
        return $this->connection->fetchAll($sql);
    }

    public function getExpiredLicenses(): array
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE license_expiry IS NOT NULL 
                AND license_expiry < NOW()
                AND legal_status = 'active'
                ORDER BY license_expiry DESC";
        
        return $this->connection->fetchAll($sql);
    }

    public function renewLicense(int $organizationId, string $newExpiryDate): int
    {
        return $this->update($organizationId, [
            'license_expiry' => $newExpiryDate,
            'license_renewed_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function uploadDocument(int $organizationId, array $documentData): bool
    {
        try {
            $organization = $this->find($organizationId);
            if (!$organization) {
                return false;
            }

            $documents = json_decode($organization['documents'] ?? '[]', true);
            $documents[] = $documentData;

            $this->update($organizationId, [
                'documents' => json_encode($documents)
            ]);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function removeDocument(int $organizationId, string $documentId): bool
    {
        try {
            $organization = $this->find($organizationId);
            if (!$organization) {
                return false;
            }

            $documents = json_decode($organization['documents'] ?? '[]', true);
            $documents = array_filter($documents, function($doc) use ($documentId) {
                return $doc['id'] !== $documentId;
            });

            $this->update($organizationId, [
                'documents' => json_encode(array_values($documents))
            ]);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function getDocuments(int $organizationId): array
    {
        $organization = $this->find($organizationId);
        if (!$organization) {
            return [];
        }

        return json_decode($organization['documents'] ?? '[]', true);
    }

    public function validateLicenseNumber(string $licenseNumber, int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE license_number = :license_number";
        
        $params = ['license_number' => $licenseNumber];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->connection->fetch($sql, $params);
        return $result['count'] == 0;
    }

    public function getOrganizationFinancialSummary(int $organizationId): array
    {
        // ملخص مالي للمنظمة (التبرعات، المصروفات، إلخ)
        $sql = "SELECT 
                    COALESCE(SUM(CASE WHEN d.type = 'cash' THEN d.amount ELSE 0 END), 0) as total_cash_donations,
                    COALESCE(SUM(CASE WHEN d.type = 'in_kind' THEN d.estimated_value ELSE 0 END), 0) as total_in_kind_donations,
                    COUNT(d.id) as total_donations_count
                FROM donations d 
                WHERE d.organization_id = :org_id AND d.status = 'approved'";
        
        return $this->connection->fetch($sql, ['org_id' => $organizationId]) ?: [];
    }
}
