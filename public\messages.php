<?php
session_start();

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];

    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: /login.php');
    exit;
}

// التحقق من وجود اسم المستخدم في الجلسة
if (!isset($_SESSION['user_name'])) {
    $user_stmt = $pdo->prepare("SELECT name FROM users WHERE id = ?");
    $user_stmt->execute([$_SESSION['user_id']]);
    $user_data = $user_stmt->fetch();
    if ($user_data) {
        $_SESSION['user_name'] = $user_data['name'];
    }
}

$success = '';
$error = '';

// جلب معلومات الموظف الحالي
$current_employee = null;
$stmt = $pdo->prepare("SELECT * FROM employees WHERE user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$current_employee = $stmt->fetch();

// إذا لم يكن المستخدم مرتبط بموظف، إنشاء موظف افتراضي
if (!$current_employee) {
    $user_stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $user_stmt->execute([$_SESSION['user_id']]);
    $user = $user_stmt->fetch();

    if ($user) {
        $default_dept = $pdo->query("SELECT id FROM departments LIMIT 1")->fetchColumn();
        $default_pos = $pdo->query("SELECT id FROM positions LIMIT 1")->fetchColumn();

        $create_employee = $pdo->prepare("
            INSERT INTO employees (user_id, employee_id, department_id, position_id, hire_date, salary, vacation_balance, sick_leave_balance)
            VALUES (?, ?, ?, ?, CURDATE(), 5000, 30, 15)
        ");

        $employee_id = 'EMP' . str_pad($_SESSION['user_id'], 4, '0', STR_PAD_LEFT);
        $create_employee->execute([$_SESSION['user_id'], $employee_id, $default_dept, $default_pos]);

        $stmt->execute([$_SESSION['user_id']]);
        $current_employee = $stmt->fetch();
    }
}

// جلب معلومات الموظف الحالي
$current_employee = null;
$stmt = $pdo->prepare("SELECT * FROM employees WHERE user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$current_employee = $stmt->fetch();

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'send_message') {
        try {
            // التحقق من البيانات المطلوبة
            $required_fields = ['subject', 'message'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("جميع الحقول المطلوبة يجب ملؤها");
                }
            }

            $pdo->beginTransaction();

            // إدراج الرسالة
            $message_stmt = $pdo->prepare("
                INSERT INTO internal_messages (sender_id, subject, message, priority, is_broadcast, department_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            $is_broadcast = isset($_POST['is_broadcast']) ? 1 : 0;
            $department_id = $_POST['department_id'] ?: null;

            $message_stmt->execute([
                $current_employee['id'],
                $_POST['subject'],
                $_POST['message'],
                $_POST['priority'] ?? 'normal',
                $is_broadcast,
                $department_id
            ]);

            $message_id = $pdo->lastInsertId();

            // إضافة المستقبلين
            if ($is_broadcast) {
                // إرسال لجميع موظفي القسم أو جميع الموظفين
                if ($department_id) {
                    $recipients_query = "SELECT id FROM employees WHERE department_id = ? AND id != ?";
                    $recipients_stmt = $pdo->prepare($recipients_query);
                    $recipients_stmt->execute([$department_id, $current_employee['id']]);
                } else {
                    $recipients_query = "SELECT id FROM employees WHERE id != ?";
                    $recipients_stmt = $pdo->prepare($recipients_query);
                    $recipients_stmt->execute([$current_employee['id']]);
                }

                $recipients = $recipients_stmt->fetchAll(PDO::FETCH_COLUMN);
            } else {
                // إرسال لموظفين محددين
                $recipients = $_POST['recipients'] ?? [];
            }

            // إدراج المستقبلين
            $recipient_stmt = $pdo->prepare("
                INSERT INTO message_recipients (message_id, recipient_id) VALUES (?, ?)
            ");

            foreach ($recipients as $recipient_id) {
                $recipient_stmt->execute([$message_id, $recipient_id]);

                // إضافة إشعار
                $notification_stmt = $pdo->prepare("
                    INSERT INTO notifications (employee_id, type, title, message, related_id)
                    VALUES (?, 'message', ?, ?, ?)
                ");

                $notification_stmt->execute([
                    $recipient_id,
                    'رسالة جديدة',
                    'رسالة جديدة من ' . $_SESSION['user_name'],
                    $message_id
                ]);
            }

            $pdo->commit();
            $success = "تم إرسال الرسالة بنجاح";

        } catch (Exception $e) {
            $pdo->rollBack();
            $error = "خطأ في إرسال الرسالة: " . $e->getMessage();
        }
    }

    elseif ($action === 'mark_read') {
        try {
            $message_id = $_POST['message_id'];

            $stmt = $pdo->prepare("
                UPDATE message_recipients
                SET is_read = 1, read_at = NOW()
                WHERE message_id = ? AND recipient_id = ?
            ");
            $stmt->execute([$message_id, $current_employee['id']]);

            $success = "تم تحديد الرسالة كمقروءة";

        } catch (Exception $e) {
            $error = "خطأ في تحديث الرسالة: " . $e->getMessage();
        }
    }

    elseif ($action === 'delete_message') {
        try {
            $message_id = $_POST['message_id'];

            $stmt = $pdo->prepare("
                UPDATE message_recipients
                SET is_deleted = 1
                WHERE message_id = ? AND recipient_id = ?
            ");
            $stmt->execute([$message_id, $current_employee['id']]);

            $success = "تم حذف الرسالة";

        } catch (Exception $e) {
            $error = "خطأ في حذف الرسالة: " . $e->getMessage();
        }
    }
}

// جلب الموظفين للإرسال
$employees = $pdo->query("
    SELECT e.id, e.employee_id, u.name, d.name as department_name
    FROM employees e
    JOIN users u ON e.user_id = u.id
    JOIN departments d ON e.department_id = d.id
    WHERE e.status = 'active' AND e.id != " . ($current_employee['id'] ?? 0) . "
    ORDER BY u.name
")->fetchAll();

// جلب الأقسام
$departments = $pdo->query("SELECT * FROM departments WHERE is_active = 1 ORDER BY name")->fetchAll();

// جلب الرسائل
$filter = $_GET['filter'] ?? 'inbox';

if ($filter === 'inbox') {
    // الرسائل الواردة
    $messages_query = "
        SELECT im.*, mr.is_read, mr.read_at, mr.is_deleted,
               se.employee_id as sender_employee_id, su.name as sender_name,
               sd.name as sender_department
        FROM internal_messages im
        JOIN message_recipients mr ON im.id = mr.message_id
        JOIN employees se ON im.sender_id = se.id
        JOIN users su ON se.user_id = su.id
        JOIN departments sd ON se.department_id = sd.id
        WHERE mr.recipient_id = ? AND mr.is_deleted = 0
        ORDER BY im.created_at DESC
    ";
    $stmt = $pdo->prepare($messages_query);
    $stmt->execute([$current_employee['id']]);
} else {
    // الرسائل المرسلة
    $messages_query = "
        SELECT im.*, COUNT(mr.id) as recipient_count,
               SUM(mr.is_read) as read_count
        FROM internal_messages im
        LEFT JOIN message_recipients mr ON im.id = mr.message_id
        WHERE im.sender_id = ?
        GROUP BY im.id
        ORDER BY im.created_at DESC
    ";
    $stmt = $pdo->prepare($messages_query);
    $stmt->execute([$current_employee['id']]);
}

$messages = $stmt->fetchAll();

// إحصائيات
$stats = [];

if ($current_employee) {
    $unread_stmt = $pdo->prepare("
        SELECT COUNT(*) FROM message_recipients mr
        JOIN internal_messages im ON mr.message_id = im.id
        WHERE mr.recipient_id = ? AND mr.is_read = 0 AND mr.is_deleted = 0
    ");
    $unread_stmt->execute([$current_employee['id']]);
    $stats['unread_messages'] = $unread_stmt->fetchColumn();

    $received_stmt = $pdo->prepare("
        SELECT COUNT(*) FROM message_recipients mr
        WHERE mr.recipient_id = ? AND mr.is_deleted = 0
    ");
    $received_stmt->execute([$current_employee['id']]);
    $stats['total_received'] = $received_stmt->fetchColumn();

    $sent_stmt = $pdo->prepare("
        SELECT COUNT(*) FROM internal_messages
        WHERE sender_id = ?
    ");
    $sent_stmt->execute([$current_employee['id']]);
    $stats['total_sent'] = $sent_stmt->fetchColumn();

    $today_stmt = $pdo->prepare("
        SELECT COUNT(*) FROM message_recipients mr
        JOIN internal_messages im ON mr.message_id = im.id
        WHERE mr.recipient_id = ? AND DATE(im.created_at) = CURDATE() AND mr.is_deleted = 0
    ");
    $today_stmt->execute([$current_employee['id']]);
    $stats['today_messages'] = $today_stmt->fetchColumn();
} else {
    $stats = [
        'unread_messages' => 0,
        'total_received' => 0,
        'total_sent' => 0,
        'today_messages' => 0
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المراسلة الداخلية - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(45deg, #6f42c1, #563d7c); }
        .stats-card { background: linear-gradient(45deg, #6f42c1, #563d7c); color: white; border-radius: 15px; }
        .message-card { border-radius: 10px; transition: transform 0.2s; }
        .message-card:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .message-unread { border-left: 4px solid #007bff; background-color: #f8f9ff; }
        .priority-high { border-left-color: #dc3545 !important; }
        .priority-urgent { border-left-color: #fd7e14 !important; }
        .priority-low { border-left-color: #6c757d !important; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-envelope me-2"></i>المراسلة الداخلية
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="/employees.php">
                    <i class="fas fa-users me-1"></i>الموظفين
                </a>
                <a class="nav-link" href="/leave-requests.php">
                    <i class="fas fa-calendar-alt me-1"></i>الإجازات
                </a>
                <a class="nav-link" href="/payroll.php">
                    <i class="fas fa-money-bill me-1"></i>الرواتب
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-envelope fa-2x mb-2"></i>
                    <h4><?= $stats['unread_messages'] ?></h4>
                    <small>رسائل غير مقروءة</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <h4><?= $stats['total_received'] ?></h4>
                    <small>إجمالي الواردة</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-paper-plane fa-2x mb-2"></i>
                    <h4><?= $stats['total_sent'] ?></h4>
                    <small>إجمالي المرسلة</small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center p-3">
                    <i class="fas fa-calendar-day fa-2x mb-2"></i>
                    <h4><?= $stats['today_messages'] ?></h4>
                    <small>رسائل اليوم</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- نموذج إرسال رسالة جديدة -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-edit me-2"></i>رسالة جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="send_message">

                            <div class="mb-3">
                                <label class="form-label">الموضوع *</label>
                                <input type="text" name="subject" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الأولوية</label>
                                <select name="priority" class="form-select">
                                    <option value="low">منخفضة</option>
                                    <option value="normal" selected>عادية</option>
                                    <option value="high">عالية</option>
                                    <option value="urgent">عاجلة</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_broadcast" id="isBroadcast">
                                    <label class="form-check-label" for="isBroadcast">
                                        رسالة جماعية
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3" id="departmentSection" style="display: none;">
                                <label class="form-label">إرسال لقسم محدد</label>
                                <select name="department_id" class="form-select">
                                    <option value="">جميع الموظفين</option>
                                    <?php foreach ($departments as $dept): ?>
                                    <option value="<?= $dept['id'] ?>"><?= htmlspecialchars($dept['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3" id="recipientsSection">
                                <label class="form-label">المستقبلين *</label>
                                <select name="recipients[]" class="form-select" multiple size="6" required>
                                    <?php foreach ($employees as $emp): ?>
                                    <option value="<?= $emp['id'] ?>">
                                        <?= htmlspecialchars($emp['name']) ?> - <?= htmlspecialchars($emp['department_name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <small class="text-muted">اضغط Ctrl لاختيار عدة موظفين</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الرسالة *</label>
                                <textarea name="message" class="form-control" rows="6" required placeholder="اكتب رسالتك هنا..."></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-paper-plane me-2"></i>إرسال الرسالة
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة الرسائل -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>الرسائل</h5>
                        <div class="btn-group">
                            <a href="?filter=inbox" class="btn btn-sm <?= $filter === 'inbox' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                <i class="fas fa-inbox me-1"></i>الواردة
                            </a>
                            <a href="?filter=sent" class="btn btn-sm <?= $filter === 'sent' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                <i class="fas fa-paper-plane me-1"></i>المرسلة
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($messages)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد رسائل</p>
                        </div>
                        <?php else: ?>
                        <div class="row">
                            <?php foreach ($messages as $message): ?>
                            <div class="col-12 mb-3">
                                <div class="card message-card <?= $filter === 'inbox' && !$message['is_read'] ? 'message-unread' : '' ?> priority-<?= $message['priority'] ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <h6 class="card-title mb-1"><?= htmlspecialchars($message['subject']) ?></h6>
                                                <small class="text-muted">
                                                    <?php if ($filter === 'inbox'): ?>
                                                        من: <?= htmlspecialchars($message['sender_name']) ?> - <?= htmlspecialchars($message['sender_department']) ?>
                                                    <?php else: ?>
                                                        إلى: <?= $message['recipient_count'] ?> موظف (<?= $message['read_count'] ?> مقروءة)
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                            <div class="d-flex align-items-center gap-2">
                                                <span class="badge bg-<?php
                                                    echo match($message['priority']) {
                                                        'urgent' => 'danger',
                                                        'high' => 'warning',
                                                        'low' => 'secondary',
                                                        default => 'info'
                                                    };
                                                ?>">
                                                    <?php
                                                    echo match($message['priority']) {
                                                        'urgent' => 'عاجل',
                                                        'high' => 'عالي',
                                                        'low' => 'منخفض',
                                                        default => 'عادي'
                                                    };
                                                    ?>
                                                </span>
                                                <small class="text-muted">
                                                    <?= date('Y/m/d H:i', strtotime($message['created_at'])) ?>
                                                </small>
                                            </div>
                                        </div>

                                        <p class="card-text"><?= nl2br(htmlspecialchars(substr($message['message'], 0, 150))) ?><?= strlen($message['message']) > 150 ? '...' : '' ?></p>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <?php if ($message['is_broadcast']): ?>
                                                <span class="badge bg-info">رسالة جماعية</span>
                                                <?php endif; ?>
                                                <?php if ($filter === 'inbox' && !$message['is_read']): ?>
                                                <span class="badge bg-primary">جديدة</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewMessage(<?= $message['id'] ?>)">
                                                    <i class="fas fa-eye"></i> عرض
                                                </button>
                                                <?php if ($filter === 'inbox'): ?>
                                                <?php if (!$message['is_read']): ?>
                                                <button class="btn btn-outline-success" onclick="markRead(<?= $message['id'] ?>)">
                                                    <i class="fas fa-check"></i> قراءة
                                                </button>
                                                <?php endif; ?>
                                                <button class="btn btn-outline-danger" onclick="deleteMessage(<?= $message['id'] ?>)">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لعرض الرسالة -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">عرض الرسالة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                    <!-- سيتم تحميل محتوى الرسالة هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحكم في عرض خيارات الإرسال
        document.getElementById('isBroadcast').addEventListener('change', function() {
            const departmentSection = document.getElementById('departmentSection');
            const recipientsSection = document.getElementById('recipientsSection');
            const recipientsSelect = recipientsSection.querySelector('select');

            if (this.checked) {
                departmentSection.style.display = 'block';
                recipientsSection.style.display = 'none';
                recipientsSelect.removeAttribute('required');
            } else {
                departmentSection.style.display = 'none';
                recipientsSection.style.display = 'block';
                recipientsSelect.setAttribute('required', 'required');
            }
        });

        function viewMessage(id) {
            // TODO: تحميل تفاصيل الرسالة
            alert('سيتم إضافة عرض تفاصيل الرسالة قريباً');
        }

        function markRead(id) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="mark_read">
                <input type="hidden" name="message_id" value="${id}">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        function deleteMessage(id) {
            if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_message">
                    <input type="hidden" name="message_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>