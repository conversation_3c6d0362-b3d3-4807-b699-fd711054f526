<?php

namespace Core\Validation;

class Validator
{
    private array $data;
    private array $rules;
    private array $errors = [];
    private array $messages = [];

    public function __construct(array $data, array $rules, array $messages = [])
    {
        $this->data = $data;
        $this->rules = $rules;
        $this->messages = $messages;
    }

    public static function make(array $data, array $rules, array $messages = []): self
    {
        return new self($data, $rules, $messages);
    }

    public function validate(): bool
    {
        foreach ($this->rules as $field => $rules) {
            $this->validateField($field, $rules);
        }

        return empty($this->errors);
    }

    public function fails(): bool
    {
        return !$this->validate();
    }

    public function errors(): array
    {
        return $this->errors;
    }

    public function getFirstError(string $field): ?string
    {
        return $this->errors[$field][0] ?? null;
    }

    private function validateField(string $field, $rules): void
    {
        if (is_string($rules)) {
            $rules = explode('|', $rules);
        }

        $value = $this->data[$field] ?? null;

        foreach ($rules as $rule) {
            $this->applyRule($field, $value, $rule);
        }
    }

    private function applyRule(string $field, $value, string $rule): void
    {
        if (strpos($rule, ':') !== false) {
            [$ruleName, $parameter] = explode(':', $rule, 2);
        } else {
            $ruleName = $rule;
            $parameter = null;
        }

        $method = 'validate' . ucfirst($ruleName);

        if (method_exists($this, $method)) {
            $this->$method($field, $value, $parameter);
        }
    }

    private function validateRequired(string $field, $value): void
    {
        if (is_null($value) || $value === '' || (is_array($value) && empty($value))) {
            $this->addError($field, 'required', 'حقل :field مطلوب');
        }
    }

    private function validateEmail(string $field, $value): void
    {
        if ($value && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $this->addError($field, 'email', 'حقل :field يجب أن يكون بريد إلكتروني صحيح');
        }
    }

    private function validateMin(string $field, $value, string $min): void
    {
        if (is_string($value) && strlen($value) < (int)$min) {
            $this->addError($field, 'min', "حقل :field يجب أن يكون على الأقل {$min} أحرف");
        } elseif (is_numeric($value) && $value < (int)$min) {
            $this->addError($field, 'min', "حقل :field يجب أن يكون على الأقل {$min}");
        }
    }

    private function validateMax(string $field, $value, string $max): void
    {
        if (is_string($value) && strlen($value) > (int)$max) {
            $this->addError($field, 'max', "حقل :field يجب أن لا يزيد عن {$max} أحرف");
        } elseif (is_numeric($value) && $value > (int)$max) {
            $this->addError($field, 'max', "حقل :field يجب أن لا يزيد عن {$max}");
        }
    }

    private function validateNumeric(string $field, $value): void
    {
        if ($value && !is_numeric($value)) {
            $this->addError($field, 'numeric', 'حقل :field يجب أن يكون رقم');
        }
    }

    private function validateInteger(string $field, $value): void
    {
        if ($value && !filter_var($value, FILTER_VALIDATE_INT)) {
            $this->addError($field, 'integer', 'حقل :field يجب أن يكون رقم صحيح');
        }
    }

    private function validateString(string $field, $value): void
    {
        if ($value && !is_string($value)) {
            $this->addError($field, 'string', 'حقل :field يجب أن يكون نص');
        }
    }

    private function validateArray(string $field, $value): void
    {
        if ($value && !is_array($value)) {
            $this->addError($field, 'array', 'حقل :field يجب أن يكون مصفوفة');
        }
    }

    private function validateDate(string $field, $value): void
    {
        if ($value && !strtotime($value)) {
            $this->addError($field, 'date', 'حقل :field يجب أن يكون تاريخ صحيح');
        }
    }

    private function validateDateFormat(string $field, $value, string $format): void
    {
        if ($value) {
            $date = \DateTime::createFromFormat($format, $value);
            if (!$date || $date->format($format) !== $value) {
                $this->addError($field, 'date_format', "حقل :field يجب أن يكون بصيغة {$format}");
            }
        }
    }

    private function validateUnique(string $field, $value, string $table): void
    {
        if ($value) {
            $connection = \Core\Database\Connection::getInstance();
            $result = $connection->fetch("SELECT COUNT(*) as count FROM {$table} WHERE {$field} = :value", ['value' => $value]);
            
            if ($result['count'] > 0) {
                $this->addError($field, 'unique', 'حقل :field موجود مسبقاً');
            }
        }
    }

    private function validateExists(string $field, $value, string $table): void
    {
        if ($value) {
            $connection = \Core\Database\Connection::getInstance();
            $result = $connection->fetch("SELECT COUNT(*) as count FROM {$table} WHERE id = :value", ['value' => $value]);
            
            if ($result['count'] == 0) {
                $this->addError($field, 'exists', 'حقل :field غير موجود');
            }
        }
    }

    private function validateConfirmed(string $field, $value): void
    {
        $confirmationField = $field . '_confirmation';
        $confirmationValue = $this->data[$confirmationField] ?? null;

        if ($value !== $confirmationValue) {
            $this->addError($field, 'confirmed', 'حقل :field غير متطابق مع التأكيد');
        }
    }

    private function validateIn(string $field, $value, string $list): void
    {
        if ($value) {
            $allowedValues = explode(',', $list);
            if (!in_array($value, $allowedValues)) {
                $this->addError($field, 'in', 'حقل :field يجب أن يكون أحد القيم المسموحة');
            }
        }
    }

    private function validateRegex(string $field, $value, string $pattern): void
    {
        if ($value && !preg_match($pattern, $value)) {
            $this->addError($field, 'regex', 'حقل :field لا يطابق الصيغة المطلوبة');
        }
    }

    private function validatePhone(string $field, $value): void
    {
        if ($value && !preg_match('/^(\+966|0)?[5][0-9]{8}$/', $value)) {
            $this->addError($field, 'phone', 'حقل :field يجب أن يكون رقم جوال صحيح');
        }
    }

    private function validateNationalId(string $field, $value): void
    {
        if ($value && !preg_match('/^[12][0-9]{9}$/', $value)) {
            $this->addError($field, 'national_id', 'حقل :field يجب أن يكون رقم هوية صحيح');
        }
    }

    private function addError(string $field, string $rule, string $message): void
    {
        $customMessage = $this->messages["{$field}.{$rule}"] ?? $this->messages[$rule] ?? $message;
        $customMessage = str_replace(':field', $this->getFieldName($field), $customMessage);
        
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        
        $this->errors[$field][] = $customMessage;
    }

    private function getFieldName(string $field): string
    {
        $fieldNames = [
            'name' => 'الاسم',
            'email' => 'البريد الإلكتروني',
            'password' => 'كلمة المرور',
            'phone' => 'رقم الجوال',
            'national_id' => 'رقم الهوية',
            'organization_name' => 'اسم المنظمة',
            'license_number' => 'رقم الترخيص',
            'address' => 'العنوان',
            'city' => 'المدينة',
            'description' => 'الوصف',
        ];

        return $fieldNames[$field] ?? $field;
    }
}
