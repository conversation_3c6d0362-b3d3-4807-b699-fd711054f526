<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام CMS الشامل - الصفحة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .hero-section {
            padding: 80px 0;
            color: white;
            text-align: center;
        }
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .feature-card:hover {
            transform: translateY(-10px);
        }
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 30px;
            margin: 0 auto 20px;
        }
        .btn-custom {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            color: white;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s;
        }
        .btn-custom:hover {
            transform: scale(1.05);
            color: white;
            text-decoration: none;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-danger { background: #dc3545; }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <h1 class="display-3 mb-4">🏢 نظام CMS الشامل</h1>
            <p class="lead mb-5">نظام إدارة محتوى متكامل للمؤسسات غير الربحية مع متجر إلكتروني وبوابات دفع سعودية</p>
            
            <!-- حالة النظام -->
            <div class="row justify-content-center mb-5">
                <div class="col-md-8">
                    <div class="card" style="background: rgba(255,255,255,0.1); border: none;">
                        <div class="card-body">
                            <h5 class="text-white mb-3">🔍 حالة النظام</h5>
                            <div class="row text-start">
                                <div class="col-md-6">
                                    <p class="text-white">
                                        <span class="status-indicator <?php echo function_exists('mysqli_connect') ? 'status-success' : 'status-danger'; ?>"></span>
                                        PHP: <?php echo function_exists('mysqli_connect') ? 'متاح' : 'غير متاح'; ?>
                                    </p>
                                    <p class="text-white">
                                        <span class="status-indicator status-success"></span>
                                        الملفات: متاحة
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-white">
                                        <span class="status-indicator <?php 
                                            try {
                                                $pdo = new PDO("mysql:host=localhost", "root", "");
                                                echo 'status-success';
                                            } catch (Exception $e) {
                                                echo 'status-warning';
                                            }
                                        ?>"></span>
                                        MySQL: <?php 
                                            try {
                                                $pdo = new PDO("mysql:host=localhost", "root", "");
                                                echo 'متصل';
                                            } catch (Exception $e) {
                                                echo 'غير متصل - تأكد من تشغيل WAMP/XAMPP';
                                            }
                                        ?>
                                    </p>
                                    <p class="text-white">
                                        <span class="status-indicator <?php 
                                            try {
                                                $pdo = new PDO("mysql:host=localhost;dbname=nonprofit_cms", "root", "");
                                                echo 'status-success';
                                            } catch (Exception $e) {
                                                echo 'status-warning';
                                            }
                                        ?>"></span>
                                        قاعدة البيانات: <?php 
                                            try {
                                                $pdo = new PDO("mysql:host=localhost;dbname=nonprofit_cms", "root", "");
                                                echo 'جاهزة';
                                            } catch (Exception $e) {
                                                echo 'تحتاج إعداد';
                                            }
                                        ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار سريعة -->
            <div class="mb-5">
                <?php
                try {
                    $pdo = new PDO("mysql:host=localhost;dbname=nonprofit_cms", "root", "");
                    echo '<a href="dashboard.php" class="btn-custom"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a>';
                    echo '<a href="store.php" class="btn-custom"><i class="fas fa-store me-2"></i>المتجر الإلكتروني</a>';
                    echo '<a href="login.php" class="btn-custom"><i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول</a>';
                } catch (Exception $e) {
                    echo '<a href="create-database.php" class="btn-custom"><i class="fas fa-database me-2"></i>إنشاء قاعدة البيانات</a>';
                }
                ?>
                <a href="test-connection.php" class="btn-custom"><i class="fas fa-plug me-2"></i>اختبار الاتصال</a>
            </div>
        </div>
    </div>

    <div class="container mb-5">
        <div class="row">
            <!-- الميزات الرئيسية -->
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4>إدارة الموارد البشرية</h4>
                    <p>نظام شامل لإدارة الموظفين والإجازات والرواتب مع نظام رسائل داخلية متطور</p>
                    <ul class="text-start">
                        <li>إدارة الموظفين والأقسام</li>
                        <li>طلبات الإجازات</li>
                        <li>نظام الرسائل الداخلية</li>
                        <li>المرفقات والتوقيعات الرقمية</li>
                    </ul>
                </div>
            </div>

            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <h4>المتجر الإلكتروني</h4>
                    <p>متجر إلكتروني متكامل مع بوابات الدفع السعودية الرائدة</p>
                    <ul class="text-start">
                        <li>كتالوج منتجات شامل</li>
                        <li>سلة تسوق ذكية</li>
                        <li>6 بوابات دفع سعودية</li>
                        <li>نظام كوبونات الخصم</li>
                    </ul>
                </div>
            </div>

            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h4>بوابات الدفع السعودية</h4>
                    <p>تكامل كامل مع أشهر بوابات الدفع في المملكة</p>
                    <ul class="text-start">
                        <li>مدى (mada) - 1.75%</li>
                        <li>تابي (Tabby) - 2.9%</li>
                        <li>تمارا (Tamara) - 3.5%</li>
                        <li>هايبر باي، باي تابس، STC Pay</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="feature-card">
                    <h4><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>🚀 للبدء السريع:</h6>
                            <ol class="text-start">
                                <li>تأكد من تشغيل WAMP/XAMPP (Apache + MySQL)</li>
                                <li>الملفات موجودة في: <code><?php echo __DIR__; ?></code></li>
                                <li>اذهب إلى: <code>http://localhost/CMS/public/home.php</code></li>
                                <li>أنشئ قاعدة البيانات من الرابط أعلاه</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6>🔧 متطلبات النظام:</h6>
                            <ul class="text-start">
                                <li>PHP 7.4+ مع MySQL</li>
                                <li>Apache Web Server</li>
                                <li>مساحة تخزين 100MB+</li>
                                <li>متصفح حديث يدعم Bootstrap 5</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-4 p-3" style="background: #f8f9fa; border-radius: 10px;">
                        <h6>📞 الدعم الفني:</h6>
                        <p class="mb-2"><strong>إذا ظهرت رسالة "PHP غير معرف":</strong></p>
                        <ol class="text-start mb-3">
                            <li>تأكد من تشغيل WAMP أو XAMPP</li>
                            <li>استخدم المتصفح بدلاً من Command Prompt</li>
                            <li>اذهب إلى: <code>http://localhost/CMS/public/home.php</code></li>
                        </ol>
                        <p class="mb-0"><strong>المسار الحالي:</strong> <code><?php echo $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?></code></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="text-center text-white py-4">
        <div class="container">
            <p>&copy; 2024 نظام CMS الشامل - مصمم خصيصاً للمؤسسات غير الربحية</p>
            <p>
                <i class="fas fa-code me-2"></i>مطور بـ PHP & MySQL
                <i class="fas fa-heart text-danger mx-2"></i>
                <i class="fas fa-shield-alt me-2"></i>آمن ومحمي
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
