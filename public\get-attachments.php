<?php
session_start();
header('Content-Type: application/json');

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'غير مصرح لك بالوصول']);
    exit;
}

// التحقق من وجود معرف الرسالة
if (!isset($_GET['message_id']) || !is_numeric($_GET['message_id'])) {
    echo json_encode(['success' => false, 'error' => 'معرف الرسالة غير صحيح']);
    exit;
}

$message_id = $_GET['message_id'];

// جلب معلومات الموظف الحالي
$current_employee = null;
$emp_stmt = $pdo->prepare("SELECT * FROM employees WHERE user_id = ?");
$emp_stmt->execute([$_SESSION['user_id']]);
$current_employee = $emp_stmt->fetch();

if (!$current_employee) {
    echo json_encode(['success' => false, 'error' => 'غير مصرح لك بالوصول']);
    exit;
}

// التحقق من صلاحية الوصول للرسالة
$access_check = $pdo->prepare("
    SELECT COUNT(*) FROM internal_messages im
    LEFT JOIN message_recipients mr ON im.id = mr.message_id
    WHERE im.id = ? AND (im.sender_id = ? OR mr.recipient_id = ?)
");
$access_check->execute([$message_id, $current_employee['id'], $current_employee['id']]);

if ($access_check->fetchColumn() == 0) {
    echo json_encode(['success' => false, 'error' => 'غير مصرح لك بعرض مرفقات هذه الرسالة']);
    exit;
}

// جلب المرفقات
try {
    $stmt = $pdo->prepare("SELECT * FROM message_attachments WHERE message_id = ? ORDER BY original_name");
    $stmt->execute([$message_id]);
    $attachments = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'attachments' => $attachments
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'خطأ في جلب المرفقات']);
}
?>
