<?php
// إدارة الأخبار - لوحة التحكم
define('ROOT_PATH', dirname(__DIR__));
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: /login.php');
    exit;
}

// تحميل متغيرات البيئة
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// إعداد قاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// معالجة الإجراءات
$action = $_GET['action'] ?? 'list';
$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'delete' && isset($_POST['id'])) {
        try {
            $stmt = $pdo->prepare("DELETE FROM news_articles WHERE id = ?");
            $stmt->execute([$_POST['id']]);
            $message = "تم حذف الخبر بنجاح";
            header('Location: /admin-news.php');
            exit;
        } catch (PDOException $e) {
            $message = "خطأ في حذف الخبر: " . $e->getMessage();
        }
    }
}

// جلب الأخبار
try {
    $sql = "SELECT a.*, c.name as category_name, u.name as author_name
            FROM news_articles a
            LEFT JOIN news_categories c ON a.category_id = c.id
            LEFT JOIN users u ON a.author_id = u.id
            ORDER BY a.created_at DESC";
    $stmt = $pdo->query($sql);
    $articles = $stmt->fetchAll();
} catch (PDOException $e) {
    $articles = [];
    $message = "خطأ في جلب الأخبار: " . $e->getMessage();
}

// جلب التصنيفات
try {
    $stmt = $pdo->query("SELECT * FROM news_categories WHERE status = 'active' ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $categories = [];
}

$user_name = $_SESSION['user_name'] ?? 'المستخدم';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأخبار - نظام إدارة المؤسسات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .article-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .article-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }
        .featured-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard.php">
                <i class="fas fa-hands-helping me-2"></i>
                نظام إدارة المؤسسات
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?= htmlspecialchars($user_name) ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a></li>
                        <li><a class="dropdown-item" href="?logout=1"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="content-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-newspaper me-2 text-primary"></i>إدارة الأخبار</h2>
                    <p class="text-muted mb-0">إدارة وتحرير أخبار ومقالات الموقع</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="/add-news.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة خبر جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
        <div class="alert alert-info alert-dismissible fade show">
            <i class="fas fa-info-circle me-2"></i>
            <?= htmlspecialchars($message) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="content-card text-center">
                    <i class="fas fa-newspaper fa-2x text-primary mb-2"></i>
                    <h4><?= count($articles) ?></h4>
                    <small class="text-muted">إجمالي الأخبار</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="content-card text-center">
                    <i class="fas fa-eye fa-2x text-success mb-2"></i>
                    <h4><?= array_sum(array_column($articles, 'views_count')) ?></h4>
                    <small class="text-muted">إجمالي المشاهدات</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="content-card text-center">
                    <i class="fas fa-tags fa-2x text-warning mb-2"></i>
                    <h4><?= count($categories) ?></h4>
                    <small class="text-muted">التصنيفات</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="content-card text-center">
                    <i class="fas fa-star fa-2x text-info mb-2"></i>
                    <h4><?= count(array_filter($articles, fn($a) => $a['is_featured'])) ?></h4>
                    <small class="text-muted">الأخبار المميزة</small>
                </div>
            </div>
        </div>

        <!-- Articles List -->
        <div class="content-card">
            <h5 class="mb-4"><i class="fas fa-list me-2"></i>قائمة الأخبار</h5>

            <?php if (empty($articles)): ?>
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أخبار حالياً</h5>
                <p class="text-muted">ابدأ بإضافة أول خبر للموقع</p>
                <a href="/add-news.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة خبر جديد
                </a>
            </div>
            <?php else: ?>

            <?php foreach ($articles as $article): ?>
            <div class="article-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-2">
                            <h6 class="mb-0 me-3"><?= htmlspecialchars($article['title']) ?></h6>

                            <?php if ($article['is_featured']): ?>
                            <span class="badge featured-badge me-2">
                                <i class="fas fa-star me-1"></i>مميز
                            </span>
                            <?php endif; ?>

                            <span class="badge status-badge <?= $article['status'] === 'published' ? 'bg-success' : 'bg-warning' ?>">
                                <?= $article['status'] === 'published' ? 'منشور' : 'مسودة' ?>
                            </span>
                        </div>

                        <p class="text-muted mb-2"><?= htmlspecialchars(substr($article['excerpt'] ?? '', 0, 100)) ?>...</p>

                        <div class="small text-muted">
                            <i class="fas fa-user me-1"></i><?= htmlspecialchars($article['author_name']) ?>
                            <i class="fas fa-calendar me-1 ms-3"></i><?= date('Y-m-d', strtotime($article['created_at'])) ?>
                            <i class="fas fa-tag me-1 ms-3"></i><?= htmlspecialchars($article['category_name'] ?? 'بدون تصنيف') ?>
                            <i class="fas fa-eye me-1 ms-3"></i><?= number_format($article['views_count']) ?> مشاهدة
                        </div>
                    </div>

                    <div class="col-md-4 text-end">
                        <div class="btn-group">
                            <a href="/news-single.php?slug=<?= urlencode($article['slug']) ?>"
                               class="btn btn-outline-primary btn-sm" target="_blank">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="/edit-news.php?id=<?= $article['id'] ?>"
                               class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="btn btn-outline-danger btn-sm"
                                    onclick="deleteArticle(<?= $article['id'] ?>, '<?= htmlspecialchars($article['title']) ?>')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>

            <?php endif; ?>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف هذا الخبر؟</p>
                    <p class="text-muted" id="articleTitle"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="id" id="deleteId">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteArticle(id, title) {
            document.getElementById('deleteId').value = id;
            document.getElementById('articleTitle').textContent = title;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
