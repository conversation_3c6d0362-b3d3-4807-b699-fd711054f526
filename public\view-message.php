<?php
session_start();

// إنشاء اتصال قاعدة البيانات
try {
    $config = require_once '../config/database.php';
    $db_config = $config['connections']['mysql'];
    
    $host = $db_config['host'];
    $dbname = $db_config['database'];
    $username = $db_config['username'];
    $password = $db_config['password'];
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: /login.php');
    exit;
}

// التحقق من وجود معرف الرسالة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    die("معرف الرسالة غير صحيح");
}

$message_id = $_GET['id'];

// جلب معلومات الموظف الحالي
$current_employee = null;
$emp_stmt = $pdo->prepare("SELECT * FROM employees WHERE user_id = ?");
$emp_stmt->execute([$_SESSION['user_id']]);
$current_employee = $emp_stmt->fetch();

if (!$current_employee) {
    die("غير مصرح لك بالوصول");
}

// جلب تفاصيل الرسالة
$message_query = "
    SELECT im.*, u.name as sender_name, mr.is_read, mr.read_at
    FROM internal_messages im
    JOIN employees e ON im.sender_id = e.id
    JOIN users u ON e.user_id = u.id
    LEFT JOIN message_recipients mr ON im.id = mr.message_id AND mr.recipient_id = ?
    WHERE im.id = ? AND (im.sender_id = ? OR mr.recipient_id = ?)
";

$stmt = $pdo->prepare($message_query);
$stmt->execute([$current_employee['id'], $message_id, $current_employee['id'], $current_employee['id']]);
$message = $stmt->fetch();

if (!$message) {
    die("الرسالة غير موجودة أو غير مصرح لك بعرضها");
}

// تحديد الرسالة كمقروءة إذا كان المستخدم مستقبل
if ($message['is_read'] === 0 && $current_employee['id'] != $message['sender_id']) {
    $update_stmt = $pdo->prepare("
        UPDATE message_recipients 
        SET is_read = 1, read_at = NOW() 
        WHERE message_id = ? AND recipient_id = ?
    ");
    $update_stmt->execute([$message_id, $current_employee['id']]);
}

// جلب المرفقات
$attachments_query = "SELECT * FROM message_attachments WHERE message_id = ?";
$attachments_stmt = $pdo->prepare($attachments_query);
$attachments_stmt->execute([$message_id]);
$attachments = $attachments_stmt->fetchAll();

// جلب المستقبلين
$recipients_query = "
    SELECT u.name, mr.is_read, mr.read_at
    FROM message_recipients mr
    JOIN employees e ON mr.recipient_id = e.id
    JOIN users u ON e.user_id = u.id
    WHERE mr.message_id = ?
    ORDER BY u.name
";
$recipients_stmt = $pdo->prepare($recipients_query);
$recipients_stmt->execute([$message_id]);
$recipients = $recipients_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الرسالة - نظام CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .message-container { max-width: 800px; margin: 0 auto; }
        .message-header { background: linear-gradient(45deg, #17a2b8, #138496); color: white; }
        .priority-high { border-left: 4px solid #dc3545; }
        .priority-urgent { border-left: 4px solid #fd7e14; }
        .priority-normal { border-left: 4px solid #28a745; }
        .priority-low { border-left: 4px solid #6c757d; }
        .signature-content { background: #f8f9fa; border-top: 1px solid #dee2e6; padding: 15px; margin-top: 20px; }
        .attachment-item { border: 1px solid #dee2e6; border-radius: 5px; padding: 10px; margin: 5px 0; }
        .recipient-read { color: #28a745; }
        .recipient-unread { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="message-container">
            <!-- رأس الرسالة -->
            <div class="card">
                <div class="card-header message-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4><i class="fas fa-envelope me-2"></i>تفاصيل الرسالة</h4>
                        <div>
                            <a href="/messages-new.php" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-right me-1"></i>العودة للرسائل
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body priority-<?= $message['priority'] ?>">
                    <!-- معلومات الرسالة -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5><?= htmlspecialchars($message['subject']) ?></h5>
                            <div class="text-muted">
                                <i class="fas fa-user me-1"></i>من: <strong><?= htmlspecialchars($message['sender_name']) ?></strong>
                            </div>
                            <div class="text-muted">
                                <i class="fas fa-calendar me-1"></i>تاريخ الإرسال: <?= date('Y/m/d H:i', strtotime($message['created_at'])) ?>
                            </div>
                            <?php if ($message['read_at']): ?>
                            <div class="text-muted">
                                <i class="fas fa-eye me-1"></i>قُرئت في: <?= date('Y/m/d H:i', strtotime($message['read_at'])) ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge bg-<?= 
                                $message['priority'] === 'urgent' ? 'danger' : 
                                ($message['priority'] === 'high' ? 'warning' : 
                                ($message['priority'] === 'low' ? 'secondary' : 'success')) 
                            ?> fs-6">
                                <?php
                                $priority_labels = [
                                    'low' => 'أولوية منخفضة',
                                    'normal' => 'أولوية عادية',
                                    'high' => 'أولوية عالية',
                                    'urgent' => 'أولوية عاجلة'
                                ];
                                echo $priority_labels[$message['priority']] ?? $message['priority'];
                                ?>
                            </span>
                            
                            <?php if (!empty($attachments)): ?>
                            <div class="mt-2">
                                <span class="badge bg-info">
                                    <i class="fas fa-paperclip me-1"></i><?= count($attachments) ?> مرفق(ات)
                                </span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- محتوى الرسالة -->
                    <div class="message-content">
                        <h6>محتوى الرسالة:</h6>
                        <div class="border rounded p-3 bg-white">
                            <?= nl2br(htmlspecialchars($message['message'])) ?>
                        </div>
                    </div>
                    
                    <!-- التوقيع -->
                    <?php if (!empty($message['signature'])): ?>
                    <div class="signature-content">
                        <h6>التوقيع:</h6>
                        <?= $message['signature'] ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- المرفقات -->
            <?php if (!empty($attachments)): ?>
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-paperclip me-2"></i>المرفقات (<?= count($attachments) ?>)</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($attachments as $attachment): ?>
                        <div class="col-md-6 mb-2">
                            <div class="attachment-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-file me-2"></i>
                                    <strong><?= htmlspecialchars($attachment['original_name']) ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        الحجم: <?= number_format($attachment['file_size'] / 1024, 2) ?> KB
                                    </small>
                                </div>
                                <div>
                                    <a href="/download-attachment.php?id=<?= $attachment['id'] ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-download me-1"></i>تحميل
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- المستقبلين -->
            <?php if (!empty($recipients)): ?>
            <div class="card mt-3">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-users me-2"></i>المستقبلين (<?= count($recipients) ?>)</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($recipients as $recipient): ?>
                        <div class="col-md-6 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-user me-2"></i>
                                    <?= htmlspecialchars($recipient['name']) ?>
                                </div>
                                <div>
                                    <?php if ($recipient['is_read']): ?>
                                    <span class="badge bg-success recipient-read">
                                        <i class="fas fa-check me-1"></i>قُرئت
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        <?= date('Y/m/d H:i', strtotime($recipient['read_at'])) ?>
                                    </small>
                                    <?php else: ?>
                                    <span class="badge bg-warning recipient-unread">
                                        <i class="fas fa-clock me-1"></i>لم تُقرأ
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- أزرار الإجراءات -->
            <div class="card mt-3">
                <div class="card-body text-center">
                    <a href="/messages-new.php" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للرسائل
                    </a>
                    
                    <?php if ($current_employee['id'] != $message['sender_id']): ?>
                    <button class="btn btn-success" onclick="replyToMessage()">
                        <i class="fas fa-reply me-2"></i>رد على الرسالة
                    </button>
                    <?php endif; ?>
                    
                    <button class="btn btn-info" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function replyToMessage() {
            // إعادة توجيه لصفحة الرسائل مع ملء الموضوع
            const subject = "رد: <?= htmlspecialchars($message['subject']) ?>";
            const sender = "<?= htmlspecialchars($message['sender_name']) ?>";
            
            // يمكن تطوير هذا لاحقاً لفتح نموذج الرد مباشرة
            window.location.href = `/messages-new.php?reply_to=<?= $message_id ?>&subject=${encodeURIComponent(subject)}`;
        }
        
        // إخفاء عناصر التنقل عند الطباعة
        window.addEventListener('beforeprint', function() {
            document.querySelector('.container').style.margin = '0';
            document.querySelector('.card-header').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.style.display = 'none');
        });
        
        window.addEventListener('afterprint', function() {
            location.reload();
        });
    </script>
</body>
</html>
