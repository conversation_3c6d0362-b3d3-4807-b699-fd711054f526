<?php

namespace App\Middleware;

use Core\Auth\Auth;

class PermissionMiddleware
{
    private $permission;
    
    public function __construct(string $permission = null)
    {
        $this->permission = $permission;
    }
    
    public function handle(): bool
    {
        $auth = Auth::getInstance();
        
        // التحقق من تسجيل الدخول أولاً
        if (!$auth->check()) {
            $this->unauthorized();
            return false;
        }
        
        // إذا لم يتم تحديد صلاحية، السماح بالمرور
        if (!$this->permission) {
            return true;
        }
        
        // التحقق من الصلاحية
        if (!$auth->hasPermission($this->permission)) {
            $this->forbidden();
            return false;
        }
        
        return true;
    }
    
    private function unauthorized(): void
    {
        if ($this->isAjaxRequest()) {
            http_response_code(401);
            header('Content-Type: application/json');
            echo json_encode(['message' => 'غير مصرح', 'redirect' => '/auth/login']);
        } else {
            header('Location: /auth/login');
        }
        exit;
    }
    
    private function forbidden(): void
    {
        if ($this->isAjaxRequest()) {
            http_response_code(403);
            header('Content-Type: application/json');
            echo json_encode(['message' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة']);
        } else {
            http_response_code(403);
            echo "<h1>403 - ممنوع</h1>";
            echo "<p>ليس لديك صلاحية للوصول إلى هذه الصفحة.</p>";
            echo "<a href='/dashboard'>العودة إلى لوحة التحكم</a>";
        }
        exit;
    }
    
    private function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
