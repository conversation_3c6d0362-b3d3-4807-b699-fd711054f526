<?php

namespace App\Models;

use Core\Database\Model;

class User extends Model
{
    protected $table = 'users';
    protected $fillable = [
        'name', 'email', 'password', 'phone', 'national_id', 'role', 
        'organization_id', 'department', 'position', 'is_active', 
        'avatar', 'last_login', 'email_verified_at', 'remember_token'
    ];

    public function create(array $data): int
    {
        if (isset($data['password'])) {
            $data['password'] = $this->hashPassword($data['password']);
        }
        
        return parent::create($data);
    }

    public function update(int $id, array $data): int
    {
        if (isset($data['password'])) {
            $data['password'] = $this->hashPassword($data['password']);
        }
        
        return parent::update($id, $data);
    }

    public function findByEmail(string $email): ?array
    {
        return $this->findBy('email', $email);
    }

    public function findByNationalId(string $nationalId): ?array
    {
        return $this->findBy('national_id', $nationalId);
    }

    public function getActiveUsers(): array
    {
        return $this->where(['is_active' => 1]);
    }

    public function getUsersByRole(string $role): array
    {
        return $this->where(['role' => $role, 'is_active' => 1]);
    }

    public function getUsersByOrganization(int $organizationId): array
    {
        return $this->where(['organization_id' => $organizationId, 'is_active' => 1]);
    }

    public function searchUsers(string $term): array
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE (name LIKE :term OR email LIKE :term OR phone LIKE :term) 
                AND is_active = 1";
        return $this->connection->fetchAll($sql, ['term' => "%{$term}%"]);
    }

    public function getUserPermissions(int $userId): array
    {
        $sql = "SELECT p.name 
                FROM permissions p
                INNER JOIN role_permissions rp ON p.id = rp.permission_id
                INNER JOIN user_roles ur ON rp.role_id = ur.role_id
                WHERE ur.user_id = :user_id
                UNION
                SELECT p.name 
                FROM permissions p
                INNER JOIN user_permissions up ON p.id = up.permission_id
                WHERE up.user_id = :user_id";
        
        $results = $this->connection->fetchAll($sql, ['user_id' => $userId]);
        return array_column($results, 'name');
    }

    public function getUserRoles(int $userId): array
    {
        $sql = "SELECT r.name, r.display_name 
                FROM roles r
                INNER JOIN user_roles ur ON r.id = ur.role_id
                WHERE ur.user_id = :user_id";
        
        return $this->connection->fetchAll($sql, ['user_id' => $userId]);
    }

    public function assignRole(int $userId, int $roleId): bool
    {
        try {
            $this->connection->insert('user_roles', [
                'user_id' => $userId,
                'role_id' => $roleId,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function removeRole(int $userId, int $roleId): bool
    {
        try {
            $this->connection->delete('user_roles', [
                'user_id' => $userId,
                'role_id' => $roleId
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function assignPermission(int $userId, int $permissionId): bool
    {
        try {
            $this->connection->insert('user_permissions', [
                'user_id' => $userId,
                'permission_id' => $permissionId,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function removePermission(int $userId, int $permissionId): bool
    {
        try {
            $this->connection->delete('user_permissions', [
                'user_id' => $userId,
                'permission_id' => $permissionId
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function activate(int $userId): int
    {
        return $this->update($userId, ['is_active' => 1]);
    }

    public function deactivate(int $userId): int
    {
        return $this->update($userId, ['is_active' => 0]);
    }

    public function updateLastLogin(int $userId): int
    {
        return $this->update($userId, ['last_login' => date('Y-m-d H:i:s')]);
    }

    public function verifyEmail(int $userId): int
    {
        return $this->update($userId, ['email_verified_at' => date('Y-m-d H:i:s')]);
    }

    public function updatePassword(int $userId, string $newPassword): int
    {
        return $this->update($userId, ['password' => $this->hashPassword($newPassword)]);
    }

    public function getRecentUsers(int $limit = 10): array
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE is_active = 1 
                ORDER BY created_at DESC 
                LIMIT {$limit}";
        return $this->connection->fetchAll($sql);
    }

    public function getUserStats(): array
    {
        $sql = "SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_users,
                    SUM(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as active_last_month
                FROM {$this->table}";
        
        return $this->connection->fetch($sql) ?: [];
    }

    public function getUsersByDepartment(): array
    {
        $sql = "SELECT department, COUNT(*) as count 
                FROM {$this->table} 
                WHERE is_active = 1 AND department IS NOT NULL 
                GROUP BY department";
        
        return $this->connection->fetchAll($sql);
    }

    private function hashPassword(string $password): string
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    public function validatePassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    public function generatePasswordResetToken(int $userId): string
    {
        $token = bin2hex(random_bytes(32));
        $hashedToken = hash('sha256', $token);
        
        $this->connection->insert('password_resets', [
            'user_id' => $userId,
            'token' => $hashedToken,
            'created_at' => date('Y-m-d H:i:s'),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+1 hour'))
        ]);
        
        return $token;
    }

    public function validatePasswordResetToken(string $token): ?array
    {
        $hashedToken = hash('sha256', $token);
        
        $sql = "SELECT pr.*, u.email 
                FROM password_resets pr
                INNER JOIN users u ON pr.user_id = u.id
                WHERE pr.token = :token AND pr.expires_at > NOW()
                ORDER BY pr.created_at DESC
                LIMIT 1";
        
        return $this->connection->fetch($sql, ['token' => $hashedToken]);
    }

    public function deletePasswordResetToken(string $token): void
    {
        $hashedToken = hash('sha256', $token);
        $this->connection->delete('password_resets', ['token' => $hashedToken]);
    }
}
