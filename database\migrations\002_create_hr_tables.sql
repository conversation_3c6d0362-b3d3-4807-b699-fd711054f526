-- جداول نظام إدارة الموظفين والموارد البشرية

-- جدول الأقسام
CREATE TABLE departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    manager_id INT,
    budget DECIMAL(15,2) DEFAULT 0,
    location VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول المناصب
CREATE TABLE positions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    department_id INT NOT NULL,
    min_salary DECIMAL(10,2),
    max_salary DECIMAL(10,2),
    requirements TEXT,
    responsibilities TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
);

-- جدول الموظفين
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    employee_id VARCHAR(20) UNIQUE NOT NULL,
    department_id INT NOT NULL,
    position_id INT NOT NULL,
    manager_id INT,
    hire_date DATE NOT NULL,
    employment_type ENUM('full_time', 'part_time', 'contract', 'intern') DEFAULT 'full_time',
    status ENUM('active', 'inactive', 'terminated', 'on_leave') DEFAULT 'active',
    salary DECIMAL(10,2) NOT NULL,
    phone VARCHAR(20),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    address TEXT,
    national_id VARCHAR(20),
    passport_number VARCHAR(20),
    bank_account VARCHAR(50),
    vacation_balance INT DEFAULT 21,
    sick_leave_balance INT DEFAULT 10,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (position_id) REFERENCES positions(id),
    FOREIGN KEY (manager_id) REFERENCES employees(id) ON DELETE SET NULL,
    INDEX idx_employee_id (employee_id),
    INDEX idx_department (department_id),
    INDEX idx_status (status)
);

-- جدول طلبات الإجازات
CREATE TABLE leave_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    leave_type ENUM('vacation', 'sick', 'emergency', 'maternity', 'paternity', 'unpaid') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    days_count INT NOT NULL,
    reason TEXT,
    status ENUM('pending', 'approved', 'rejected', 'cancelled') DEFAULT 'pending',
    approved_by INT,
    approved_at TIMESTAMP NULL,
    rejection_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES employees(id) ON DELETE SET NULL,
    INDEX idx_employee (employee_id),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date)
);

-- جدول الرواتب
CREATE TABLE payroll (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    pay_period_start DATE NOT NULL,
    pay_period_end DATE NOT NULL,
    basic_salary DECIMAL(10,2) NOT NULL,
    overtime_hours DECIMAL(5,2) DEFAULT 0,
    overtime_rate DECIMAL(10,2) DEFAULT 0,
    allowances DECIMAL(10,2) DEFAULT 0,
    deductions DECIMAL(10,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    insurance_amount DECIMAL(10,2) DEFAULT 0,
    net_salary DECIMAL(10,2) NOT NULL,
    status ENUM('draft', 'approved', 'paid') DEFAULT 'draft',
    paid_at TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    INDEX idx_employee (employee_id),
    INDEX idx_period (pay_period_start, pay_period_end),
    INDEX idx_status (status)
);

-- جدول المراسلة الداخلية
CREATE TABLE internal_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sender_id INT NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    is_broadcast BOOLEAN DEFAULT FALSE,
    department_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    INDEX idx_sender (sender_id),
    INDEX idx_created_at (created_at)
);

-- جدول مستقبلي الرسائل
CREATE TABLE message_recipients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    recipient_id INT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (message_id) REFERENCES internal_messages(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES employees(id) ON DELETE CASCADE,
    INDEX idx_message (message_id),
    INDEX idx_recipient (recipient_id),
    INDEX idx_unread (recipient_id, is_read)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    type ENUM('leave_request', 'leave_approved', 'leave_rejected', 'payroll', 'message', 'system') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    related_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    INDEX idx_employee (employee_id),
    INDEX idx_unread (employee_id, is_read),
    INDEX idx_type (type)
);

-- إدراج بيانات أولية للأقسام
INSERT INTO departments (name, description, location) VALUES
('الإدارة العامة', 'الإدارة العليا والتخطيط الاستراتيجي', 'الطابق الثالث'),
('الموارد البشرية', 'إدارة شؤون الموظفين والتوظيف', 'الطابق الثاني'),
('المالية والمحاسبة', 'الشؤون المالية والمحاسبية', 'الطابق الأول'),
('تقنية المعلومات', 'تطوير وصيانة الأنظمة التقنية', 'الطابق الأول'),
('المشاريع', 'إدارة وتنفيذ المشاريع', 'الطابق الثاني'),
('التسويق والاتصال', 'التسويق والعلاقات العامة', 'الطابق الثاني');

-- إدراج بيانات أولية للمناصب
INSERT INTO positions (title, description, department_id, min_salary, max_salary) VALUES
('مدير عام', 'الإشراف العام على المؤسسة', 1, 15000, 25000),
('مدير إداري', 'إدارة العمليات اليومية', 1, 10000, 15000),
('مدير موارد بشرية', 'إدارة شؤون الموظفين', 2, 8000, 12000),
('أخصائي موارد بشرية', 'تنفيذ سياسات الموارد البشرية', 2, 5000, 8000),
('مدير مالي', 'إدارة الشؤون المالية', 3, 8000, 12000),
('محاسب', 'تنفيذ العمليات المحاسبية', 3, 4000, 7000),
('مدير تقنية المعلومات', 'إدارة الأنظمة التقنية', 4, 8000, 12000),
('مطور برمجيات', 'تطوير وصيانة التطبيقات', 4, 5000, 9000),
('مدير مشاريع', 'إدارة وتنفيذ المشاريع', 5, 7000, 11000),
('منسق مشاريع', 'تنسيق أنشطة المشاريع', 5, 4000, 7000),
('مدير تسويق', 'إدارة أنشطة التسويق', 6, 6000, 10000),
('أخصائي تسويق', 'تنفيذ الحملات التسويقية', 6, 3500, 6000);
