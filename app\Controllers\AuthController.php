<?php

namespace App\Controllers;

use App\Models\User;
use Core\Validation\Validator;

class AuthController extends BaseController
{
    public function showLogin(): void
    {
        if ($this->auth->check()) {
            $this->redirect('/dashboard');
        }
        
        $this->view('auth.login');
    }

    public function login(): void
    {
        $data = $this->validate($_POST, [
            'email' => 'required|email',
            'password' => 'required|min:6',
        ]);

        $remember = isset($_POST['remember']) && $_POST['remember'] === '1';

        if ($this->auth->attempt($data['email'], $data['password'], $remember)) {
            $this->flash('success', 'تم تسجيل الدخول بنجاح');
            
            // إعادة التوجيه إلى الصفحة المطلوبة أو لوحة التحكم
            $redirectTo = $_SESSION['intended_url'] ?? '/dashboard';
            unset($_SESSION['intended_url']);
            
            $this->redirect($redirectTo);
        } else {
            $this->flash('error', 'بيانات تسجيل الدخول غير صحيحة');
            $this->back();
        }
    }

    public function logout(): void
    {
        $this->auth->logout();
        $this->flash('success', 'تم تسجيل الخروج بنجاح');
        $this->redirect('/auth/login');
    }

    public function showForgotPassword(): void
    {
        if ($this->auth->check()) {
            $this->redirect('/dashboard');
        }
        
        $this->view('auth.forgot-password');
    }

    public function forgotPassword(): void
    {
        $data = $this->validate($_POST, [
            'email' => 'required|email',
        ]);

        $userModel = new User();
        $user = $userModel->findByEmail($data['email']);

        if ($user) {
            $token = $userModel->generatePasswordResetToken($user['id']);
            
            // إرسال بريد إلكتروني (يجب تنفيذ خدمة البريد الإلكتروني)
            $this->sendPasswordResetEmail($user['email'], $token);
            
            $this->flash('success', 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني');
        } else {
            $this->flash('error', 'البريد الإلكتروني غير مسجل في النظام');
        }
        
        $this->back();
    }

    public function showResetPassword(string $token): void
    {
        if ($this->auth->check()) {
            $this->redirect('/dashboard');
        }

        $userModel = new User();
        $resetData = $userModel->validatePasswordResetToken($token);

        if (!$resetData) {
            $this->flash('error', 'رابط إعادة تعيين كلمة المرور غير صحيح أو منتهي الصلاحية');
            $this->redirect('/auth/forgot-password');
        }

        $this->view('auth.reset-password', ['token' => $token]);
    }

    public function resetPassword(): void
    {
        $data = $this->validate($_POST, [
            'token' => 'required',
            'password' => 'required|min:8|confirmed',
        ]);

        $userModel = new User();
        $resetData = $userModel->validatePasswordResetToken($data['token']);

        if (!$resetData) {
            $this->flash('error', 'رابط إعادة تعيين كلمة المرور غير صحيح أو منتهي الصلاحية');
            $this->redirect('/auth/forgot-password');
        }

        // تحديث كلمة المرور
        $userModel->updatePassword($resetData['user_id'], $data['password']);
        
        // حذف رمز إعادة التعيين
        $userModel->deletePasswordResetToken($data['token']);

        $this->flash('success', 'تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول');
        $this->redirect('/auth/login');
    }

    public function verifyEmail(string $token): void
    {
        // تنفيذ تأكيد البريد الإلكتروني
        $userModel = new User();
        
        // البحث عن المستخدم بالرمز المميز
        $user = $userModel->findBy('email_verification_token', $token);
        
        if ($user && !$user['email_verified_at']) {
            $userModel->verifyEmail($user['id']);
            $userModel->update($user['id'], ['email_verification_token' => null]);
            
            $this->flash('success', 'تم تأكيد البريد الإلكتروني بنجاح');
        } else {
            $this->flash('error', 'رابط تأكيد البريد الإلكتروني غير صحيح أو منتهي الصلاحية');
        }
        
        $this->redirect('/auth/login');
    }

    private function sendPasswordResetEmail(string $email, string $token): void
    {
        // تنفيذ إرسال البريد الإلكتروني
        // يمكن استخدام PHPMailer أو خدمة بريد إلكتروني خارجية
        
        $resetUrl = $_ENV['APP_URL'] . "/auth/reset-password/{$token}";
        
        $subject = 'إعادة تعيين كلمة المرور - نظام إدارة المؤسسات';
        $message = "
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>إعادة تعيين كلمة المرور</title>
        </head>
        <body dir='rtl'>
            <h2>إعادة تعيين كلمة المرور</h2>
            <p>تم طلب إعادة تعيين كلمة المرور لحسابك.</p>
            <p>للمتابعة، يرجى النقر على الرابط التالي:</p>
            <p><a href='{$resetUrl}' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعادة تعيين كلمة المرور</a></p>
            <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.</p>
            <p>هذا الرابط صالح لمدة ساعة واحدة فقط.</p>
            <hr>
            <p><small>نظام إدارة المؤسسات غير الربحية</small></p>
        </body>
        </html>
        ";
        
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . ($_ENV['MAIL_FROM_NAME'] ?? 'نظام إدارة المؤسسات') . ' <' . ($_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>') . '>',
            'Reply-To: ' . ($_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>'),
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // إرسال البريد الإلكتروني
        mail($email, $subject, $message, implode("\r\n", $headers));
        
        // تسجيل محاولة الإرسال
        $logData = [
            'to' => $email,
            'subject' => $subject,
            'type' => 'password_reset',
            'time' => date('Y-m-d H:i:s')
        ];
        
        $logFile = ROOT_PATH . '/storage/logs/email_' . date('Y-m-d') . '.log';
        file_put_contents($logFile, json_encode($logData) . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
}
